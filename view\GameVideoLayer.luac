--region *.lua
--Date
--麻将游戏最底层的UI管理类
--此文件由[BabeLua]插件自动生成




local GameVideoLayer = class("GameVideoLayer" , function()
    return createCSB("ui_csb/GameVideoLayer.csb", true)
end)

function GameVideoLayer:ctor( game_scene )
    self.game_scene = game_scene


    local Panel_video = self:getChildByName("Panel_video")
    --返回按钮
    local Button_back = Panel_video:getChildByName("Button_back")
    Button_back:addClickEventListener( function()
         self.game_scene:Exit_game( 4 , self.game_scene:getDeskInfo().roomID )
    end)
    local Image_menu = Panel_video:getChildByName("Image_menu")
    
    self.is_pause = false
    self.l_is_pase_data = false
    self.l_is_update_scene = false
    self.event_call = {}
    --暂停按钮
    local Button_pause = Image_menu:getChildByName("Button_pause")
    self.Button_pause = Button_pause
        Button_pause:addClickEventListener( function()
            --Music:playEffect_click()
            if self.is_pause == false then
                self.is_pause = true
                self.Button_pause:loadTextures("ui_res/video/jixu.png","ui_res/video/jixu.png","")


            else
                self.is_pause = false
                self.Button_pause:loadTextures("ui_res/video/pause.png","ui_res/video/pause.png","")
                performWithDelay( self , function()
                    self:play_data()
                end , 0.5)
            end
        end)

    --重播按钮
    --若点击重播后出现之前碰杠牌留在新开始的界面上,是因为play_data()中的clone(self.game_data[self.m_index])出现问题
    --导致无法进行深拷贝,在子类进行重写MJGSModule的构造即可,可参考监利红中的svn的12660版本
    local Button_return = Image_menu:getChildByName("Button_return")
        Button_return:addClickEventListener( function()
            --Music:playEffect_click()
            
            print(" ----------录像-----  重新播放 ")
            if self.m_nextIndex ~= 1 then
                self.m_nextIndex = 1
                self.is_pause = true
                performWithDelay( self , function()
                    self.Button_pause:loadTextures("video/pause.png","video/pause.png","")
                    self.is_pause = false
                    self:play_data()
                    end , 1)
            end
        end)

    self.Text_press = Panel_video:getChildByName("Text_press")

    self:init_event()
end

function GameVideoLayer:begin_video( game_data )
    luaLog("开始播放游戏录像数据")
    self.m_index = 0                                -- 当前播放的下标
    self.m_nextIndex = 1                            -- 下一帧所需要播放的下标（受快进与快退的影响）
    self.game_video_data = clone( game_data )       -- 录像数据的步骤帧
    self.game_data = {} 
    --直接创建一个具体麻将的断线重连数据module
    --self.game_data[0] = GSModule:create()

                                -- 所有的数据帧
    self.Text_press:setString( "1/"..#self.game_video_data)
    performWithDelay( self , function ()
            self:play_data()
        end, 0.5 )
end

--@desc 更新场景
function GameVideoLayer:update_scene( id , data )
    if self.l_is_update_scene then
        luaLog("播放游戏数据：180_"..id)
        --dump( data , "---- data ----" , 10 )
        self.game_scene:getHelper():handleGameMessage(id,data)
    end
end

--@desc 同意游戏
function GameVideoLayer:l_agree_game(game_data_cmd , cmd_data )
    self:update_scene( cmd_data.ID ,cmd_data.data)
end


--@desc 游戏开始
function GameVideoLayer:l_game_begin( game_data_cmd , cmd_data )
    print(" ----------录像-----  游戏开始 ")
    self:update_scene( cmd_data.ID , cmd_data.data )
    return 2
end

--@desc 摆牌结果
function GameVideoLayer:l_game_baipairesult( game_data_cmd , cmd_data )
    print(" ----------录像-----  摆牌结果 ")
    self:update_scene( cmd_data.ID , cmd_data.data)
    return 2
end

function GameVideoLayer:l_game_FetchCards( game_data_cmd , cmd_data )
    print("----------录像--发牌")

    local select_index = 1
    local chairId = 0
    for i=1, #cmd_data.data.videoCardDatas  do
         chairId = ViewHelp.getUIChairIndexByServerChair(i-1)
         if chairId == 1 then
             select_index = i  
             break
         end
    end

    self:update_scene( cmd_data.ID , cmd_data.data.videoCardDatas[select_index] )
    return 4
end

--@desc 回合亮牌
function GameVideoLayer:l_game_roundresult( game_data_cmd , cmd_data )
    print(" ----------录像-----  回合亮牌 ")
    self:update_scene( cmd_data.ID , cmd_data.data )
    local delayTime = 1
    if cmd_data.data.delayTime then
        delayTime = cmd_data.data.delayTime
    end
    return delayTime
end

--@desc 回合算分
function GameVideoLayer:l_game_roundFinish( game_data_cmd , cmd_data )
    print(" ----------录像-----  回合结算 ")
    self:update_scene( cmd_data.ID , cmd_data.data )
    return 1
end


function GameVideoLayer:init_event()
    self.event_call[120] = handler(self , self.l_game_begin )            -- 游戏开始
    self.event_call[125] = handler(self , self.l_game_FetchCards)
    self.event_call[127] = handler(self , self.l_game_baipairesult )  -- 摆牌结果
    self.event_call[149] = handler(self , self.l_game_roundresult )  -- 回合结算
    self.event_call[148] = handler(self , self.l_game_roundFinish )  -- 回合结算
end

--@desc     设置断线重连数据
--@param    cmd_data   当前的操作命令
--@param    is_update_scene 是否需要刷新场景数据
function GameVideoLayer:set_game_data(  video_index , is_update_scene )
    -- 是否需要解析数据 （已经已经解析过了就不需要再解析了）
    self.l_is_update_scene = is_update_scene
    if self.game_data[video_index] == nil then
        self.game_data[video_index] = clone( self.game_data[ video_index - 1 ] )
        self.l_is_pase_data = true
    else
        self.l_is_pase_data = false
    end
    local ret = 1
    if self.event_call[self.game_video_data[video_index].ID] then
        local event_ret = self.event_call[self.game_video_data[video_index].ID]( self.game_data[video_index] , clone(self.game_video_data[video_index]) )
        if event_ret then
            ret = event_ret
        end
    end
    return ret
end

--@desc  播放数据
--@param space 操作数据 0. 暂停在这一帧 , 大于0 快进的步数 , 小于0 快退的步数
--@param perGameData  之前的数据
function GameVideoLayer:play_data()
    if self.is_pause == true then
        -- 暂停了
        self.is_wait_paly = true
        return nil
    end

    if #self.game_video_data < self.m_nextIndex then
        api_show_tips("录像已播完")
        self.is_pause = true
        self.Button_pause:loadTextures("video/jixu.png","video/jixu.png","")
        return nil
    end

    local delay = 1.0

    --if self.m_nextIndex > self.m_index then
        -- 向终点播放
        if self.m_nextIndex > self.m_index + 1 then
            for index = self.m_index+1 , self.m_nextIndex-1 do
                --执行快进的命令
                self:set_game_data( index )
            end
            -- 发送断线重连数据
            self.m_nextIndex = self.m_nextIndex - 1
            local resp_json = clone(self.game_data[self.m_nextIndex])
            resp_json.chair = self.game_scene:getSelfInfo().bDeskStation
            resp_json.GSID = 26
            dump( resp_json , "断线重连数据" , 10 )
            self.game_scene:HandleGameMessage(MJMessageID.ASS_GM_GAME_STATION , resp_json)
            self.m_index = self.m_nextIndex
            delay = 0.2
        else
            self.m_index = self.m_nextIndex
            delay = self:set_game_data( self.m_nextIndex , true )       
            self.Text_press:setString( self.m_nextIndex.."/"..#self.game_video_data)
        end
        
--    else
--        -- 向起点回退
--        self.m_index = self.m_nextIndex

--        -- 发送断线重连数据
--       local resp_json = clone(self.game_data[self.m_index])
--       resp_json.chair = self.game_scene:getSelfInfo().bDeskStation
--       resp_json.GSID = 26
--       self.game_scene:HandleGameMessage(MJMessageID.ASS_GM_GAME_STATION , resp_json)
--        -- 执行当前这一帧的录像数据
--        --快退的时候不用再处理出牌消息了 
--        if MJMessageID.ASS_OUT_CARD_INFO ~= self.game_video_data[self.m_index].ID 
--        and MJMessageID.ASS_RESULT ~= self.game_video_data[self.m_index].ID then
--            delay = self:set_game_data( self.m_index , true )
--        end

--        self.Text_press:setString( self.m_nextIndex.."/"..#self.game_video_data)
--    end
    self.m_nextIndex = self.m_nextIndex + 1
    performWithDelay( self , function()
        self:play_data()
    end , delay)
end


--立刻播放某一帧的录像
function GameVideoLayer:play_data_atonce()
   
    if #self.game_video_data < self.m_nextIndex then
        api_show_tips("录像已播完")
        self.is_pause = true
        self.Button_pause:loadTextures("video/jixu.png","video/jixu.png","")
        return nil
    end

    --self.game_scene:tryRemoveGameResultLayer()
    local layerManager = GameSceneModule:getInstance():getGameScene():getGameLayerManager()
    layerManager:hideGameResultLayer()
    if self.m_nextIndex > self.m_index then
        -- 向终点播放
        if self.m_nextIndex > self.m_index + 1 then
            for index = self.m_index+1 , self.m_nextIndex-1 do
                --执行快进的命令
                self:set_game_data( index , true)
            end
            -- 发送断线重连数据
            self.m_nextIndex = self.m_nextIndex - 1
            local resp_json = clone(self.game_data[self.m_nextIndex])
            resp_json.chair = self.game_scene:getSelfInfo().bDeskStation
            resp_json.GSID = 26
            dump( resp_json , "断线重连数据" , 10 )
            self.game_scene:HandleGameMessage(MJMessageID.ASS_GM_GAME_STATION , resp_json)
            self.m_index = self.m_nextIndex
           
            self.Text_press:setString( self.m_nextIndex.."/"..#self.game_video_data)
        end
        
    else
        -- 向起点回退
        self.m_index = self.m_nextIndex

        -- 发送断线重连数据
       local resp_json = clone(self.game_data[self.m_index])
       resp_json.chair = self.game_scene:getSelfInfo().bDeskStation
       resp_json.GSID = 26
       self.game_scene:HandleGameMessage(MJMessageID.ASS_GM_GAME_STATION , resp_json)
       if MJMessageID.ASS_OUT_CARD_INFO ~= self.game_video_data[self.m_index].ID 
        and MJMessageID.ASS_RESULT ~= self.game_video_data[self.m_index].ID then
            self:set_game_data( self.m_index , true )
       end

       -- 执行当前这一帧的录像数据
        self.Text_press:setString( self.m_nextIndex.."/"..#self.game_video_data)
    end
   
end

return GameVideoLayer

--endregion
