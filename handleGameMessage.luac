--region *.lua
--Date
--此文件由[BabeLua]插件自动生成

module("handleGameMessage", package.seeall)
local GameMessage_MainID = 180


Table_GameMessage_ASSID = 
{
    ASS_GAME_AUTO_BEGIN	= 130,				-- 房主手动开始游戏
    ASS_GAME_PRESTART_REQ = 131,            -- 提前开局请求
    ASS_PRESTART_INFO = 132,                 --服务端下发提前开局的信息
    ASS_GAME_BEGIN = 120,                   -- 服务器下发游戏开始

    ASS_ROBBANKER_NOTIFY = 121,             -- 服务端通知抢庄
    ASS_ROBBANKER_REQ = 122,                -- 客户端发送抢庄消息
    ASS_ROBBANKER_INFO = 123,               -- 服务端下发抢庄结果

    ASS_MAKE_NT_INFO = 124, 				-- 服务器下发定庄的消息
    ASS_CATCH_CARD = 125,                   -- 服务端下发抓牌消息

    ASS_BAIPAI_OVER = 126,                  -- 客户端发送摆牌完成消息
    ASS_BAIPAI_RESULT = 127,                -- 服务端发送摆牌完成结果

    ASS_XIAZHUANG_REQ = 128,                -- 下庄请求消息
    ASS_ROUND_FINISH = 148,                 --动画结束后，这局结束
    ASS_ROUND_RESULT = 149,             	-- 服务端发送一回合结束的消息
    ASS_FINISH = 150,                       -- 服务端下发牌局结束消息
 
    ASS_GM_AGREE_GAME = 1,                  -- 同意游戏
    ASS_GM_GAME_STATION = 2,                -- 断线重连
    ASS_DISMISS_DESK = 3,                   -- 申请解散桌子
    ASS_AGREE_DISMISS_DESK = 4,             -- 申请同意解散桌子
    ASS_QUIT_REQ = 6,                       -- 玩家申请站起
    ASS_USER_CUT_OVER = 7,                  -- 玩家切换的消息
    ASS_USER_CHEATER_REQ = 20,               -- 玩家换牌请求
    ASS_USER_CHEATER_INFO = 21,              --玩家换牌消息
    ASS_PLAYER_DO_XIPAI = 52,
}

local Table_Action_Handler = {}

local Local_handler = nil
local isGetGameStation = false

function setGameMessageHandler(handler)

   Local_handler = handler
   Table_Action_Handler[Table_GameMessage_ASSID.ASS_GAME_BEGIN] = handler.response_game_start
   Table_Action_Handler[Table_GameMessage_ASSID.ASS_PRESTART_INFO] = handler.response_game_prestart
   Table_Action_Handler[Table_GameMessage_ASSID.ASS_ROBBANKER_NOTIFY] = handler.response_robbanker_info
   Table_Action_Handler[Table_GameMessage_ASSID.ASS_ROBBANKER_INFO] = handler.response_robbanker_result
   Table_Action_Handler[Table_GameMessage_ASSID.ASS_MAKE_NT_INFO] = handler.response_game_make_nt

   Table_Action_Handler[Table_GameMessage_ASSID.ASS_CATCH_CARD] = handler.response_catch_card

   Table_Action_Handler[Table_GameMessage_ASSID.ASS_BAIPAI_RESULT] = handler.response_baipai_result

   Table_Action_Handler[Table_GameMessage_ASSID.ASS_ROUND_FINISH] = handler.response_round_finish
  
   Table_Action_Handler[Table_GameMessage_ASSID.ASS_ROUND_RESULT] = handler.response_point_result
   Table_Action_Handler[Table_GameMessage_ASSID.ASS_FINISH] = handler.response_game_paiju_info

   Table_Action_Handler[Table_GameMessage_ASSID.ASS_GM_AGREE_GAME] = handler.response_agree_game
   Table_Action_Handler[Table_GameMessage_ASSID.ASS_GM_GAME_STATION] = handler.response_game_station

   Table_Action_Handler[Table_GameMessage_ASSID.ASS_USER_CHEATER_INFO] = handler.response_cheat_info
   Table_Action_Handler[Table_GameMessage_ASSID.ASS_PLAYER_DO_XIPAI] = handler.response_player_xi_pai_info
   
   

end

--处理服务器发来的消息
function HandleGameMessage( AssistantID, json_value )
   print("****************************************AssistantID start = "..AssistantID)

   if AssistantID == Table_GameMessage_ASSID.ASS_GM_GAME_STATION then
        isGetGameStation = true
   end
    
    if isGetGameStation == false and g_server_obj:get_server_type() ~= "video" then
        return 
    end

   if Table_Action_Handler[AssistantID]~=nil then
      Table_Action_Handler[AssistantID](Local_handler, json_value)
   else
        print("----Table_Action_Handler["..AssistantID.."]---nil")
   end

   print("****************************************AssistantID end = "..AssistantID)
end

--客户端发送消息
function SendGameMessage(AssistantID, request)
    print("send mj game message AssistantID = " .. AssistantID)

    GameSceneModule:getInstance():getGameScene():getHelper():sendGameMessage(AssistantID, request)

end

function resethandleGameMessage()
    print("resethandleGameMessage isGetGameStation = ",isGetGameStation)
    isGetGameStation = false
end

--endregion
