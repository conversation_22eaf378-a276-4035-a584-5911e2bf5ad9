--region *.lua
--Date
--此文件由[BabeLua]插件自动生成

local GameMusic = class("GameMusic")


--播放音效(根据男女方言)
function GameMusic:PlayEffect(effectname,ui_chair_index)
    local filename = self:getEffectPath(ui_chair_index)..effectname
    print("------------filename = " .. filename)

    local isExit = cc.FileUtils:getInstance():isFileExist(filename)

    if isExit == true then
        Music:playEffect(filename,false)
    else
        --api_show_Msg_Box("音效不存在  " .. filename )
    end
 
end

-- 开始比牌
function GameMusic:playStartThanCardEffect(ui_chair_index)
    self:PlayEffect("startthancard.mp3",ui_chair_index)
end

-- 打枪
function GameMusic:playShootSpeak(ui_chair_index)
    self:PlayEffect("shoot.mp3",ui_chair_index)
end

-- 全垒打
function GameMusic:playQLDSpeak(ui_chair_index)
    self:PlayEffect("qld.mp3",ui_chair_index)
end

--播放牌型音效
function GameMusic:playCardTypeEffect(cardType, ui_chair_index)
    local file = string.format("cardType/cardType_%d.mp3", cardType)
    self:PlayEffect(file, ui_chair_index)
end

--播放特殊牌型音效
function GameMusic:playSpecialCardTypeEffect(cardType, ui_chair_index)
    local file = string.format("cardTypeSpecial/cardType_%d.mp3", cardType)
    self:PlayEffect(file, ui_chair_index)
end


-- 点击音效
function GameMusic:playClickEffect()
    local path = "game_res/" .. GAME_ID .. "/sound/other/click.mp3"
    Music:playEffect(path,false)
end

-- 翻牌音效
function GameMusic:playFanPaiEffect()
    local path = "game_res/" .. GAME_ID .. "/sound/other/fanpai.mp3"
    Music:playEffect(path,false)
end

-- 输了音效
function GameMusic:playFailureEffect()
--    local path = "game_res/" .. GAME_ID .. "/sound/other/failure.mp3"
--    Music:playEffect(path,false)
end

-- 赢了音效
function GameMusic:playWinEffect()
--    local path = "game_res/" .. GAME_ID .. "/sound/other/win.mp3"
--    Music:playEffect(path,false)
end

-- 游戏开始音效
function GameMusic:playStartEffect()
    local path = "game_res/" .. GAME_ID .. "/sound/other/gamebegin.mp3"
    Music:playEffect(path,false)
end

-- 定庄音效
function GameMusic:playMakeNTEffect()
    local path = "game_res/" .. GAME_ID .. "/sound/other/makent.mp3"
    Music:playEffect(path,false)
end

-- 选庄音效
function GameMusic:playXuanNTEffect()
    local path = "game_res/" .. GAME_ID .. "/sound/other/paod.mp3"
    Music:playEffect(path,false)
end

-- 打枪音效
function GameMusic:playShootEffect()
    local path = "game_res/" .. GAME_ID .. "/sound/other/shoot.mp3"
    Music:playEffect(path,false)
end

-- 全垒打音效
function GameMusic:playQLDEffect()
    local path = "game_res/" .. GAME_ID .. "/sound/other/qld.mp3"
    Music:playEffect(path,false)
end

-- 发牌音效
function GameMusic:playSendCardEffect()
    local path = "game_res/" .. GAME_ID .. "/sound/other/sendcard.mp3"
    Music:playEffect(path,false)
end

--  倒计时音效
function GameMusic:playCountDownEffect(bEnd)
    local path = ""
    if bEnd then
        path = "game_res/" .. GAME_ID .. "/sound/other/CountDown2.mp3"
    else
        path = "game_res/" .. GAME_ID .. "/sound/other/CountDown1.mp3"
    end
    Music:playEffect(path,false)
end


--播放游戏背景音乐
function GameMusic:PlayBGMusic()
    local path = "game_res/" .. GAME_ID .."/sound/other/bgm.mp3" 
    local isExit = cc.FileUtils:getInstance():isFileExist(path)

    if isExit == true then
        print("----------------> 播放背景音乐。")
        Music:playMusic(path)
    else
        api_show_Msg_Box("音效不存在  " .. path )
    end
end

--获取当前配置音效路径
function GameMusic:getEffectPath(ui_chair_index)
    local server_chair = ViewHelp.getServerChairByUIChair(ui_chair_index)

    local isBoy = GameSceneModule:getInstance():getGameScene().PlayersInfos[ViewHelp.getIndexByServerChair(server_chair)].bBoy
    print("ui_chair_index = " .. ui_chair_index)
    if isBoy then
        print(" isBoy  == " ,isBoy)
    end
--    local GameID = GameSceneModule:getInstance():getGameScene():getDeskInfo().gameID
    --local isFangyan = (UserData:getMJLanguage() == 0)
    local isFangyan = false
    local path = ""
    if isBoy == 1 then
        if isFangyan then
            path = path .. "game_res/" .. GAME_ID .."/sound/putongnan/"
        else
            path = path .. "game_res/" .. GAME_ID .."/sound/putongnan/"
        end
    else
        if isFangyan then
            path = path.. "game_res/" .. GAME_ID .."/sound/putongnv/"
        else
            path = path .. "game_res/" .. GAME_ID .."/sound/putongnv/"
        end
    end
    return path
end

----------------------------------------------------------------------
--不区分男女方言相关
EffectEnum = {
    ANN     = "ann.mp3",
    WIN     = "win.mp3",
    LOSE    = "lose.mp3",
    KAIS    = "kais.mp3",
    PAOD    = "paod.mp3",
    DAOJS   = "daojs.mp3",
    LIK     = "lik.mp3",
    JIAR    = "jiar.mp3", 
    ZHUAP   = "zhuap.mp3",
    CHUP    = "chup.mp3",
    QIEP    = "qiep.mp3",
    SHOUP   = "shoup.mp3",
    DIANP   = "dianp.mp3",
    XIAOX   = "xiaox.mp3",
    TOUZ    = "touz.mp3",
}
--播放其他音效
function GameMusic:playOtherEffect(effectname,isLoop)
    local filename = "game_res/" .. GAME_ID .."/sound/other/" .. effectname 
    Music:playEffect(filename,isLoop)
end


--播放倒计时音效
function GameMusic:playCountdownEffect()
    local filename = "game_res/" .. GAME_ID .."/sound/other/daojs.mp3"
    --self.sound_id = AudioEngine.playEffect(filename, true)

    local isExit = cc.FileUtils:getInstance():isFileExist(filename)
    if isExit == true then
        self.sound_id = AudioEngine.playEffect(filename, true)
    else
        api_show_Msg_Box("音效不存在  " .. filename )
    end

end

--停止播放倒计时音效
function GameMusic:stopCountdownEffect()
    print("GameMusic:stopCountdownEffect")
    if self.sound_id ~= nil then
        AudioEngine.stopEffect(self.sound_id)
        self.sound_id = nil
    end
    
end


return GameMusic

--endregion
