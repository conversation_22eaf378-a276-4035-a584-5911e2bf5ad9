--region *.lua
--Date
--此文件由[BabeLua]插件自动生成

--import("..GameDefine")

local GameLogic = class("GameLogic")


local cardStruct={
    Wangval1 = 0,
    Wangval2 = 0,
    Cardval = 0,
}

local guiNum = 0
local bA_5BigShunzi = false

local AllSpecialCardType = {}

function GameLogic:ctor()

end

function GameLogic:setA_5ShunziMode(bMode)
    bA_5BigShunzi = bMode
end

function GameLogic:setSpecialCardType(sepcialCardTypeList)
    AllSpecialCardType = sepcialCardTypeList
    
end

function GameLogic:bExistCurSpecialCardType(scType)
    if AllSpecialCardType == nil  then
        return false
    end

    for k, itemType in pairs(AllSpecialCardType) do
        if itemType == scType then
            return true
        end
    end

    return false
end

function GameLogic:setGuiNum(num)
   -- guiNum = num    闲游十三水不需要
end

-- 牌型检测(带癞子)
function GameLogic:detectionCardType_L(handCardData)
    local tempCards = clone(handCardData)
    self:sort(tempCards,0)
    local cardType = {0,0,0,0,0,0,0,0}       -- 牌型类型
    local cardTypeData = {}                  -- 牌型数据
    cardTypeData.SingleCard = {}             -- 所有单牌

    --分析手牌组成
    local paraseStruct = self:ParaseCards(tempCards)

    --获取单牌
    for i = 1, #paraseStruct.PointNumTab do 
        if paraseStruct.PointNumTab[i] and #paraseStruct.PointNumTab[i]==1 then
            table.insert(cardTypeData.SingleCard, paraseStruct.PointNumTab[i][1])
        end
    end
    
    self:detectionPairs_L(tempCards,paraseStruct,cardType,cardTypeData)
    self:detectionTwoPairs_L(tempCards,paraseStruct,cardType,cardTypeData)
    self:detectionThree_L(tempCards,paraseStruct,cardType,cardTypeData)
    self:detectionShunza_L(tempCards,paraseStruct,cardType,cardTypeData)
    self:detectionTonghua_L(tempCards,paraseStruct,cardType,cardTypeData)
    self:detectionGourd_L(tempCards,paraseStruct,cardType,cardTypeData)
    self:detectionTiezhi_L(tempCards,paraseStruct,cardType,cardTypeData)
    self:detectionTonghuasz_L(tempCards,paraseStruct,cardType,cardTypeData)
    self:detectionWutong_L(tempCards,paraseStruct,cardType,cardTypeData)
    
    return cardType,cardTypeData
end
-- 检测对子(带癞子)
function GameLogic:detectionPairs_L(handCardData,paraseStruct, cardType, cardTypeData)
    cardTypeData.Pairs = { }
    cardTypeData.Pairs.index = 1
    cardTypeData.Pairs.twoIndex = 1
    cardTypeData.Pairs.twoIndex2 = 2
    cardTypeData.Pairs.PairsTab = { }

    local PointNumTab = paraseStruct.PointNumTab
    local LaiziTab = paraseStruct.LaiziTab
    for i = #PointNumTab, 1, -1 do
        if PointNumTab[i] and #PointNumTab[i]>=2 then
            local cards = {}
            table.insert(cards, #cards+1, PointNumTab[i][1])
            table.insert(cards, #cards+1, PointNumTab[i][2])
            table.insert(cardTypeData.Pairs.PairsTab, #cardTypeData.Pairs.PairsTab+1,cards)
            cardType[CardType.CT_PAIRS] = 1
        end
    end
    if #cardTypeData.Pairs.PairsTab==0 and #LaiziTab > 0 then
        local cards = {}
        table.insert(cards, #cards+1, LaiziTab[1])
    end
--    table.sort(cardTypeData.Pairs.PairsValue, function(a, b)
--        return self:getCardPoint(a.Cardval) > self:getCardPoint(b.Cardval)
--    end)
end

-- 检测两队(带癞子)
function GameLogic:detectionTwoPairs_L(handCardData,paraseStruct, cardType, cardTypeData)
    cardTypeData.TwoPairs = { }
    cardTypeData.TwoPairs.index = 1
    cardTypeData.TwoPairs.TwoPairsTab = { }
    local pairNum = #cardTypeData.Pairs.PairsTab
    if pairNum>=2 then
        cardType[CardType.CT_TWO_PAIRS] = 1
        for i = 1,pairNum do
            for j = pairNum, 1, -1 do
                if i<j then
                    local cards = clone(cardTypeData.Pairs.PairsTab[i])
                    table.insert(cards, #cards+1, cardTypeData.Pairs.PairsTab[j][1])
                    table.insert(cards, #cards+1, cardTypeData.Pairs.PairsTab[j][2])
                    table.insert(cardTypeData.TwoPairs.TwoPairsTab, #cardTypeData.TwoPairs.TwoPairsTab+1,cards)
                end
            end
        end        
    end
end

-- 检测三条(带癞子)
function GameLogic:detectionThree_L(handCardData, paraseStruct,cardType, cardTypeData)
    cardTypeData.Three = { }
    cardTypeData.Three.index = 1
    cardTypeData.Three.ThreeTab = { }

    local PointNumTab = paraseStruct.PointNumTab
    local LaiziTab = paraseStruct.LaiziTab
     for i = #PointNumTab, 1, -1 do
        if PointNumTab[i] then
            local cards = {}
            if #PointNumTab[i] >= 3  then
                if guiNum>0 and i>=2 and i<=guiNum+1 then
                   
                else
                    table.insert(cards, #cards+1, PointNumTab[i][1])
                    table.insert(cards, #cards+1, PointNumTab[i][2])
                    table.insert(cards, #cards+1, PointNumTab[i][3])
                    table.insert(cardTypeData.Three.ThreeTab, #cardTypeData.Three.ThreeTab+1,cards)
                    cardType[CardType.CT_THREE] = 1
                end
            elseif #PointNumTab[i]>=2 and #LaiziTab >= 1 then
                table.insert(cards, #cards+1,PointNumTab[i][1])
                table.insert(cards, #cards+1,PointNumTab[i][2])
                table.insert(cards, #cards+1, LaiziTab[1])
                table.insert(cardTypeData.Three.ThreeTab, #cardTypeData.Three.ThreeTab+1,cards)
                cardType[CardType.CT_THREE] = 1
            end
        end
    end
    if #cardTypeData.Three.ThreeTab <= 0 and #LaiziTab>=2 then
        local cards = {}
        table.insert(cards, #cards+1, LaiziTab[1])
        table.insert(cards, #cards+1, LaiziTab[2])
        table.insert(cardTypeData.Three.ThreeTab, #cardTypeData.Three.ThreeTab+1,cards)
        cardType[CardType.CT_THREE] = 1
    end
end

-- 检测顺子(带癞子)
function GameLogic:detectionShunza_L(handCardData, paraseStruct,cardType, cardTypeData)
    cardTypeData.Shunzi = { }
    cardTypeData.Shunzi.index = 1
    cardTypeData.Shunzi.ShunziTab = { }

    local PointNumTab = paraseStruct.PointNumTab
    local LaiziTab = paraseStruct.LaiziTab


    local startPoint = 14
    while startPoint >= 6 do
        local needLaizi = 0
        local bLaiziFirstPos = false
        local bLaiziSecondPos = false
        local cards = {}
        for i = 0,4 do
            local cardPoint = startPoint-i
            if PointNumTab[cardPoint] and #PointNumTab[cardPoint]>0 then
                table.insert(cards, #cards+1, PointNumTab[cardPoint][1])
            else
                if needLaizi > #LaiziTab or needLaizi==2 then
                    break
                end
                needLaizi = needLaizi+1
                table.insert(cards, #cards+1, LaiziTab[needLaizi]) 
            end
        end
        if #cards == 5 then
            local cardsParase = self:ParaseCards(cards)
            local bTonghua = self:judgeTonghua_L(cards,cardsParase)
            if  not bTonghua  then 
                table.insert(cardTypeData.Shunzi.ShunziTab, #cardTypeData.Shunzi.ShunziTab+1,cards) 
                cardType[CardType.CT_SHUNZA] = 1
            end
        end
        startPoint = startPoint -1
    end

    --检测顺子A,2,3,4,5
    local needLaizi = 0
    local bLaiziFirstPos = false
    local bLaiziSecondPos = false
    local cards = {}
    for i =1,5 do
        local cardPoint = i
        if cardPoint == 1 then
            cardPoint = 14
        end
        if PointNumTab[cardPoint] and #PointNumTab[cardPoint]>0 then
            table.insert(cards, #cards+1, PointNumTab[cardPoint][1])
        else
            if needLaizi > #LaiziTab or needLaizi==2 then
                break
            end
            needLaizi = needLaizi+1
            table.insert(cards, #cards+1, LaiziTab[needLaizi]) 
        end
    end
    if #cards == 5 then
        local cardsParase = self:ParaseCards(cards)
        local bTonghua = self:judgeTonghua_L(cards,cardsParase) 
        if not bTonghua then --剔除同花順
            table.insert(cardTypeData.Shunzi.ShunziTab, #cardTypeData.Shunzi.ShunziTab+1, cards) 
            cardType[CardType.CT_SHUNZA] = 1
        end
    end

end

-- 检测同花(带癞子)
function GameLogic:detectionTonghua_L(handCardData, paraseStruct,cardType, cardTypeData)
    self:sortByHuaKind(handCardData)

    cardTypeData.Tonghua = { }
    cardTypeData.Tonghua.index = 1
    cardTypeData.Tonghua.TonghuaTab = { }

    local HuaKindTab = paraseStruct.HuaKindTab
    local LaiziTab = paraseStruct.LaiziTab
    dump(HuaKindTab, "HuaKindTab = ")
    for i=1,4 do
        local huaKindNum = #HuaKindTab[i]
        if huaKindNum + #LaiziTab >= 5 and huaKindNum >= 3  then
--        if huaKindNum + #LaiziTab >= 5  then
            local cardValTab = clone(HuaKindTab[i])
            for laiziindex = 1, 2 do
                if LaiziTab[laiziindex] then
                    table.insert(cardValTab, #cardValTab+1, LaiziTab[laiziindex])
                end
            end
            self:CombineCard(cardValTab, cardTypeData.Tonghua.TonghuaTab, 1, 5)
            if #cardTypeData.Tonghua.TonghuaTab >0 then
                cardType[CardType.CT_TONGHUA] = 1
            end
        end
    end

    for i=1,4 do
        local huaKindNum = #HuaKindTab[i]
        if huaKindNum + #LaiziTab >= 5 and huaKindNum >= 2  then
            local cardValTab = clone(HuaKindTab[i])
            for laiziindex = 1, 3 do
                if LaiziTab[laiziindex] then
                    table.insert(cardValTab, #cardValTab+1, LaiziTab[laiziindex])
                end
            end
            self:CombineCard(cardValTab, cardTypeData.Tonghua.TonghuaTab, 1, 5)
            if #cardTypeData.Tonghua.TonghuaTab >0 then
                cardType[CardType.CT_TONGHUA] = 1
            end
        end
    end

    
end

-- 检测葫芦(带癞子)
function GameLogic:detectionGourd_L(handCardData, paraseStruct,cardType, cardTypeData)
    cardTypeData.Gourd = { }
    cardTypeData.Gourd.index = 1
    cardTypeData.Gourd.GourdTab = { }

    local PairsTab = cardTypeData.Pairs.PairsTab
    local ThreeTab = cardTypeData.Three.ThreeTab
    if #PairsTab <= 0 or #ThreeTab<=0 then
        return
    end
    for pairIndex = #PairsTab, 1, -1 do
        if #PairsTab[pairIndex] == 2 then --对子不应该有癞子，否则为铁支
            for threeIndex = 1, #ThreeTab do 
                if #ThreeTab[threeIndex] == 3 and self:getCardPoint(ThreeTab[threeIndex][1]) ~= self:getCardPoint(PairsTab[pairIndex][1]) then
                    local cards = clone(ThreeTab[threeIndex])
                    table.insert(cards, #cards+1, PairsTab[pairIndex][1])
                    table.insert(cards, #cards+1, PairsTab[pairIndex][2])
                    table.insert(cardTypeData.Gourd.GourdTab,#cardTypeData.Gourd.GourdTab+1,cards)
                    cardType[CardType.CT_GOURD] = 1
                end
            end
        end
    end
end

-- 检测铁支(带癞子)
function GameLogic:detectionTiezhi_L(handCardData, paraseStruct,cardType, cardTypeData)
    cardTypeData.Tiezhi = { }
    cardTypeData.Tiezhi.index = 1
    cardTypeData.Tiezhi.TiezhiTab = { }

    local PointNumTab = paraseStruct.PointNumTab
    local LaiziTab = paraseStruct.LaiziTab
    for i = #PointNumTab, 1, -1 do
        if #PointNumTab[i]>= 2 and (#LaiziTab+#PointNumTab[i]>=4) then
            if guiNum>0 and i>=2 and i<=guiNum+1  then
            else    
                cardType[CardType.CT_TIEZHI] = 1
                local cards = {}
                local cardnum = 4
                for cardIndex = 1,#PointNumTab[i] do 
                    table.insert(cards,#cards+1, PointNumTab[i][cardIndex])
                    cardnum = cardnum-1
                    if cardnum <= 0 then
                        break
                    end
                end
                if cardnum>0 then
                --补癞子
                    for _,v in pairs(LaiziTab) do
                        table.insert(cards,#cards+1, v)
                        cardnum = cardnum-1
                        if cardnum <= 0 then
                            break
                        end
                    end
                end
                table.insert(cardTypeData.Tiezhi.TiezhiTab,#cardTypeData.Tiezhi.TiezhiTab+1,cards)
            end
        end

        if #PointNumTab[i] == 3 and i>=2 and i <= guiNum+1 and guiNum>0  then
            cardType[CardType.CT_TIEZHI] = 1
            local cards = {}
            local cardnum = 3
            for cardIndex = 1,#PointNumTab[i] do 
                table.insert(cards,#cards+1, PointNumTab[i][cardIndex])
                cardnum = cardnum-1
                if cardnum <= 0 then
                    break
                end
            end
            table.insert(cardTypeData.Tiezhi.TiezhiTab,#cardTypeData.Tiezhi.TiezhiTab+1,cards)
        end
    end
   
    if #LaiziTab>=3 then
        cardType[CardType.CT_TIEZHI] = 1
        local cards = {}
        for i =1 ,3 do
            table.insert(cards,#cards+1, LaiziTab[i])
        end
        table.insert(cardTypeData.Tiezhi.TiezhiTab,#cardTypeData.Tiezhi.TiezhiTab+1,cards)
    end

--    table.sort(cardTypeData.Tiezhi.TiezhiValue, function(a, b)
--        return self:getCardPoint(a.Cardval) > self:getCardPoint(b.Cardval)
--    end)
end

-- 检测同花顺(带癞子)
function GameLogic:detectionTonghuasz_L(handCardData, paraseStruct,cardType, cardTypeData)
--    self:sortByHuaKind(handCardData)

    cardTypeData.Tonghuasz = { }
    cardTypeData.Tonghuasz.index = 1
    cardTypeData.Tonghuasz.TonghuaszTab = { }

    local HuaKindTab = paraseStruct.HuaKindTab
    local LaiziTab = paraseStruct.LaiziTab
    local TonghuaTab = cardTypeData.Tonghua.TonghuaTab
    local deleteIdxTab = {}
    local addCount = 0;
    if #TonghuaTab > 0 then
        for k,cards in pairs(TonghuaTab) do
            local paraseCards = self:ParaseCards(cards)
            local bshunzi = self:judgeShunza_L(cards,paraseCards)
            if bshunzi then
                table.insert(deleteIdxTab, k)
                local thsCards = clone(cards)
                cardType[CardType.CT_TONGHUASZ] = 1
                table.insert(cardTypeData.Tonghuasz.TonghuaszTab, #cardTypeData.Tonghuasz.TonghuaszTab+1, thsCards)
                addCount = addCount + 1
            end
        end
    end

    --剔除同花里的同花順牌型
    if  #deleteIdxTab > 0 then
        table.sort(deleteIdxTab)
        for i = #deleteIdxTab, 1, -1 do
            table.remove(TonghuaTab, deleteIdxTab[i])
        end
        if #TonghuaTab <= 0 then
            cardType[CardType.CT_TONGHUA] = 0
        end
    end


--    table.sort(cardTypeData.Shunzi.ShunzaValue, function(a, b)
--        local aPoint = self:getCardPoint(a.Cardval)
--        local bPoint = self:getCardPoint(b.Cardval)
--        if aPoint == 14 and bPoint==10 then
--           return false
--        elseif aPoint==10 and bPoint==14 then 
--            return true
--        else
--            return aPoint > bPoint
--        end
--    end)
--    self:sortBySize(handCardData)
end


-- 检测同花顺(带癞子)
function GameLogic:detectionWutong_L(handCardData, paraseStruct,cardType, cardTypeData)
--    self:sortByHuaKind(handCardData)

    cardTypeData.Wutong = { }
    cardTypeData.Wutong.index = 1
    cardTypeData.Wutong.WutongTab = { }

    local PointNumTab = paraseStruct.PointNumTab
    local LaiziTab = paraseStruct.LaiziTab
    for i = #PointNumTab, 1, -1 do
        if #PointNumTab[i]>= 1 and (#LaiziTab+#PointNumTab[i]>=5) then
            cardType[CardType.CT_WUTONG] = 1
            local cards = {}
            local cardnum = 5
            for cardIndex = 1,#PointNumTab[i] do 
                table.insert(cards,#cards+1, PointNumTab[i][cardIndex])
                cardnum = cardnum-1
                if cardnum <= 0 then
                    break
                end
            end
            if cardnum>0 then
            --补癞子
                for _,v in pairs(LaiziTab) do
                    table.insert(cards,#cards+1, v)
                    cardnum = cardnum-1
                    if cardnum <= 0 then
                        break
                    end
                end
            end
            table.insert(cardTypeData.Wutong.WutongTab,#cardTypeData.Wutong.WutongTab+1,cards)
        end

        if #PointNumTab[i]==3 and i>=2 and i<=guiNum+1  and #LaiziTab >=1 then
            cardType[CardType.CT_WUTONG] = 1
            local cards = {}
            local cardnum = 4
            for cardIndex = 1,#PointNumTab[i] do 
                table.insert(cards,#cards+1, PointNumTab[i][cardIndex])
                cardnum = cardnum-1
                if cardnum <= 0 then
                    break
                end
            end
            if cardnum>0 then
            --补癞子
                for _,v in pairs(LaiziTab) do
                    table.insert(cards,#cards+1, v)
                    cardnum = cardnum-1
                    if cardnum <= 0 then
                        break
                    end
                end
            end
            table.insert(cardTypeData.Wutong.WutongTab,#cardTypeData.Wutong.WutongTab+1,cards)
        end
    end

--    table.sort(cardTypeData.Shunzi.ShunzaValue, function(a, b)
--        local aPoint = self:getCardPoint(a.Cardval)
--        local bPoint = self:getCardPoint(b.Cardval)
--        if aPoint == 14 and bPoint==10 then
--           return false
--        elseif aPoint==10 and bPoint==14 then 
--            return true
--        else
--            return aPoint > bPoint
--        end
--    end)
--    self:sortBySize(handCardData)
end


-- 获取所有单牌
function GameLogic:getAllSingleCard(handCardData)
    local singleCard = {}
    for i=1,#handCardData do
        if self:getCardNum(handCardData[i],handCardData) == 1 then
            table.insert(singleCard,handCardData[i])
        end
    end

    return singleCard
end

------------------------------------------------普通牌型判断
-- 得到所有弹起牌的数值并排序
function GameLogic:getUpCardData(handCardData,handCard)
    local tab = {}
    local cardType = 0
    for k,v in pairs(handCard) do
        if v:getPositionY() == CARD_POSITION_Y_UP and v:isVisible() then
            table.insert(tab,handCardData[k])
        end
    end
    self:sort(tab,0)
    cardType = self:judgeCardType_L(tab,cardType)
    return tab,cardType
end

-- 判断牌型
function GameLogic:judgeCardType(tab)
    local cardType = 0
    if self:judgeTonghuasz(tab) then
        cardType = CardType.CT_TONGHUASZ
    elseif self:judgeTiezhi(tab) then
        cardType = CardType.CT_TIEZHI
    elseif self:judgeGourd(tab) then
        cardType = CardType.CT_GOURD
    elseif self:judgeTonghua(tab) then
        cardType = CardType.CT_TONGHUA
    elseif self:judgeShunza(tab) then
        cardType = CardType.CT_SHUNZA
    elseif self:judgeThree(tab) then
        cardType = CardType.CT_THREE
    elseif self:judgeTwopairs(tab) then
        cardType = CardType.CT_TWO_PAIRS
    elseif self:judgePairs(tab) then
        cardType = CardType.CT_PAIRS
    end

    return cardType
end

-- 判断同花顺
function GameLogic:judgeTonghuasz(tab)
    if self:judgeTonghua(tab) and self:judgeShunza(tab) then
        return true
    end

    return false
end

-- 判断铁支
function GameLogic:judgeTiezhi(tab)
    if #tab ~= 5 then
        return false
    end

    local flag = false

    for k, v in pairs(tab) do
        if self:getCardNum(v, tab) == 4 then
            flag = true
        end
    end

    if flag then
        for k, v in pairs(tab) do
            if self:getCardNum(v, tab) == 1 then
                table.remove(tab, k)
                table.insert(tab, v)
                break
            end
        end
    end

    return flag
end

-- 判断葫芦
function GameLogic:judgeGourd(tab)
    if #tab ~= 5 then
        return false
    end

    local flag1 = false
    local flag2 = false

    for k, v in pairs(tab) do
        if self:getCardNum(v, tab) == 3 then
            flag1 = true
        elseif self:getCardNum(v, tab) == 2 then
            flag2 = true
        end
    end

    if flag1 and flag2 then
        local m = { }
        for i = 1, 2 do
            for k, v in pairs(tab) do
                if self:getCardNum(v, tab) == 2 or self:getCardNum(v, tab) == 1 then
                    table.remove(tab, k)
                    table.insert(m, v)
                    break
                end
            end
        end

        for i=1,#m do
            table.insert(tab,m[i])
        end

    end

    return(flag1 and flag2)
end

-- 判断同花
function GameLogic:judgeTonghua(tab)
    if #tab ~= 5 then
        return false
    end

    if self:getHuaKindNumByCard(tab[1],tab) == 5 then
        return true
    end

    return false
end

-- 判断顺子
function GameLogic:judgeShunza(tab)
    local flag = false
    if #tab ~= 5 then
        return flag
    end

    local cardPoint = self:getCardPoint(tab[1])
    if cardPoint == 14 then
        flag = true
        local cardP = self:getCardPoint(tab[2])
        for i = 3, 5 do
            if (cardP - i + 2) ~= self:getCardPoint(tab[i]) then
                flag = false
                break
            end
        end
        if self:getCardPoint(tab[5]) ~= 2 then
            flag = false
        end
    end

    if not flag then
        flag = true
        for i = 2, 5 do
            if (cardPoint - i + 1) ~= self:getCardPoint(tab[i]) then
                flag = false
                break
            end
        end
    end


    return flag
end

-- 判断三条
function GameLogic:judgeThree(tab)
    local flag = false

    local cardPoint = 0
    for k, v in pairs(tab) do
        if self:getCardNum(v, tab) == 3 then
            flag = true
            cardPoint = self:getCardPoint(v)
            break
        end
    end

    if flag and #tab == 5 then
        local m = { }
        for i = 1, 3 do
            for k, v in pairs(tab) do
                if self:getCardNum(v, tab) == 1 then
                    table.remove(tab, k)
                    table.insert(m, v)
                    break
                end
            end
        end

        for i = 1, #m do
            table.insert(tab, m[i])
        end

    end

    return flag
end

-- 判断两对
function GameLogic:judgeTwopairs(tab)
if #tab ~= 5 then
        return false
    end

    local flag = false
    local pairsNums = 0
    local card = 0
    for k, v in pairs(tab) do
        if card ~= self:getCardPoint(v) then
            if self:getCardNum(v, tab) == 2 then
                pairsNums = pairsNums + 1
            end
        end
        card = self:getCardPoint(v)
    end

    if pairsNums == 2 then
        flag = true
    end

    if flag then
        for k, v in pairs(tab) do
            if self:getCardNum(v, tab) == 1 then
                table.remove(tab, k)
                table.insert(tab, v)
                break
            end
        end
    end

    return flag

end

-- 判断对子
function GameLogic:judgePairs(tab)
    local flag = false
    local m = {}
    for k, v in pairs(tab) do
        if self:getCardNum(v, tab) == 2 then
            flag = true
            table.insert(m,v)
        end
    end


    if flag then
        for i=1,#m do
            for k, v in pairs(tab) do
                if m[i] == v then
                    table.remove(tab,k)
                    break
                end
            end
        end

        for i=1,#m do
            table.insert(tab,i,m[i])
        end
    end


    return flag
end

-- 判断牌型(带癞子)
function GameLogic:judgeCardType_L(tab)
    local cardType = 0
    local forbiddenCardTypeMap = {}
    forbiddenCardTypeMap[CardType.CT_LIUTONG] = 1
    local paraseStruct = self:ParaseCards(tab)
    if self:judgeWuTongAddOne_L(tab,paraseStruct) and forbiddenCardTypeMap[CardType.CT_LIUTONG] == nil  then
        cardType = CardType.CT_LIUTONG
    elseif self:judgeWutong_L(tab,paraseStruct) then
        cardType = CardType.CT_WUTONG
    elseif self:judgeTonghuasz_L(tab,paraseStruct) then
        cardType = CardType.CT_TONGHUASZ
    elseif self:judgeTiezhi_L(tab,paraseStruct) then
        cardType = CardType.CT_TIEZHI
    elseif self:judgeGourd_L(tab,paraseStruct) then
        cardType = CardType.CT_GOURD
    elseif self:judgeTonghua_L(tab,paraseStruct) then
        cardType = CardType.CT_TONGHUA
    elseif self:judgeShunza_L(tab,paraseStruct) then
        cardType = CardType.CT_SHUNZA
    elseif self:judgeThree_L(tab,paraseStruct) then
        cardType = CardType.CT_THREE
    elseif self:judgeTwopairs_L(tab,paraseStruct) then
        cardType = CardType.CT_TWO_PAIRS
    elseif self:judgePairs_L(tab,paraseStruct) then
        cardType = CardType.CT_PAIRS
    end

    return cardType
end
-- 判断同花顺(带癞子)
function GameLogic:judgeTonghuasz_L(tab,paraseStruct)
    if self:judgeTonghua_L(tab,paraseStruct) and self:judgeShunza_L(tab,paraseStruct) then
        return true
    end

    return false
end

function GameLogic:judgeWutong_L(tab,paraseStruct)
    local ret = false
    local LaiziTab = paraseStruct.LaiziTab 
    local PointNumTab = paraseStruct.PointNumTab


    local laiziNum = #LaiziTab
    for k,cards in pairs(PointNumTab) do 
        if #cards+laiziNum == 5 then
            ret = true
            break
        end

--        if #cards == 3 and laiziNum > 0 and k>=2 and k<=guiNum+1 then
--             ret = true
--             break
--        end 
    end
    if ret then
        table.sort(tab,function(a,b)
            local bAlaizi = self:IsLaizi(a)
            local bBlaizi = self:IsLaizi(b)
            if not bAlaizi and bBlaizi then
                return true
            elseif (bBlaizi and not bAlaizi) or (bAlaizi and not bBlaizi) then
                return false
            else 
                local aPoint = self:getCardPoint(a)
                local bPoint = self:getCardPoint(b)
                local aHuakind = self:GetCardHuaKind(a)
                local bHuakind = self:GetCardHuaKind(b)
                if aPoint > bPoint then
                    return true
                elseif aPoint==bPoint then
                    return aHuakind > bHuakind
                else
                    return false
                end
            end
        end)
    end
    return ret
end

-- 判断铁支(带癞子)
function GameLogic:judgeTiezhi_L(tab,paraseStruct)
    if #tab ~= 5 then
       -- return false
    end
    local ret = false
    local LaiziTab = paraseStruct.LaiziTab 
    local PointNumTab = paraseStruct.PointNumTab

    local laiziNum = #LaiziTab
    local TiezhiPoint = 14
    for point = #PointNumTab,1,-1 do 
        if #PointNumTab[point]+laiziNum == 4 then
            TiezhiPoint = point
            ret = true
            break
        end

--        if #PointNumTab[point] == 3 and point>=2 and point<=guiNum+1  then
--            TiezhiPoint = point
--            ret = true
--            break
--        end
    end
    if ret then
        table.sort(tab,function(a,b)
            local bAlaizi = self:IsLaizi(a)
            local bBlaizi = self:IsLaizi(b)
            local aPoint = self:getCardPoint(a)
            local bPoint = self:getCardPoint(b)
            if aPoint == TiezhiPoint and bPoint ~= TiezhiPoint then
                return true
            elseif bAlaizi and bPoint ~= TiezhiPoint and not bBlaizi then
                return true
            else 
                return false
            end
        end)
    end
    return ret
end
--判断六同
function GameLogic:judgeWuTongAddOne_L(tab,paraseStruct)
    if #tab ~= 5 then
        return false
    end
    local ret = false
    local LaiziTab = paraseStruct.LaiziTab 
    local PointNumTab = paraseStruct.PointNumTab
    local laiziNum = #LaiziTab
    local liuTongPoint = 14
    for point = #PointNumTab,1,-1 do
        if #PointNumTab[point] == 3 and point>=2 and point<=guiNum+1  and laiziNum == 2 then
            liuTongPoint = PointNumTab[point]
            ret = true
            break
        end
    end
    
    if ret then
        table.sort(tab,function(a,b)
            local bAlaizi = self:IsLaizi(a)
            local bBlaizi = self:IsLaizi(b)
            local aPoint = self:getCardPoint(a)
            local bPoint = self:getCardPoint(b)
            if aPoint == TiezhiPoint and bPoint ~= TiezhiPoint then
                return true
            elseif bAlaizi and bPoint ~= TiezhiPoint and not bBlaizi then
                return true
            else 
                return false
            end
        end)
    end
    return ret 
end

-- 判断葫芦(带癞子)
function GameLogic:judgeGourd_L(tab,paraseStruct)
    if #tab ~= 5 then
        return false
    end
    local ret = false
    local LaiziTab = paraseStruct.LaiziTab 
    local PointNumTab = paraseStruct.PointNumTab
    local laiziNum = #LaiziTab

    local threePoint = 0
    local pairPoint = 0
    for point = #PointNumTab,1,-1 do 
        if threePoint == 0 and #PointNumTab[point]+laiziNum == 3 then
            threePoint = point
        elseif #PointNumTab[point] == 2 then
            pairPoint = point 
        end
    end
    if pairPoint~=0 and threePoint~=0 then
        ret = true
    end

     if ret then
        table.sort(tab,function(a,b)
            local bAlaizi = self:IsLaizi(a)
            local bBlaizi = self:IsLaizi(b)
            local aPoint = self:getCardPoint(a)
            local bPoint = self:getCardPoint(b)
            if aPoint == threePoint and bPoint ~= threePoint then
                return true
            elseif bAlaizi and bPoint ~= threePoint and not bBlaizi then
                return true
            else 
                return false
            end
        end)
    end

    return ret
end

-- 判断同花(带癞子)
function GameLogic:judgeTonghua_L(tab,paraseStruct)
    local ret = false
    local LaiziTab = paraseStruct.LaiziTab 
    local HuaKindTab = paraseStruct.HuaKindTab
    local laiziNum = #LaiziTab

    for _,cards in pairs(HuaKindTab) do 
        if #cards + laiziNum == 5 then
            ret = true
            break
        end
    end

    if ret then
        table.sort(tab,function(a,b)
            local bAlaizi = self:IsLaizi(a)
            local bBlaizi = self:IsLaizi(b)
            local aPoint = self:getCardPoint(a)
            local bPoint = self:getCardPoint(b)
            local aHuakind = self:GetCardHuaKind(a)
            local bHuakind = self:GetCardHuaKind(b)
            if ( bAlaizi and not bBlaizi) then
                return true
            elseif (bAlaizi and bBlaizi) or (not bAlaizi and not bBlaizi) then
                if aPoint>bPoint then
                    return true
                elseif aPoint == bPoint and aHuakind>bHuakind then
                    return true
                else
                    return false
                end
            else 
                return false
            end
        end)
    end

    return ret
end

-- 判断顺子(带癞子)
function GameLogic:judgeShunza_L(tab,paraseStruct)
    local ret = false
    local LaiziTab = paraseStruct.LaiziTab 
    local PointNumTab = paraseStruct.PointNumTab
    local laiziNum = #LaiziTab
    local tmpTab = {}
    local startPoint = 0
    for cardPoint = 14, 5, -1  do
        startPoint = cardPoint
        tmpTab = {}
        local laiziIndex = 1 
        for i = 0,4 do
            
            if #PointNumTab[cardPoint-i]>0 then
                table.insert(tmpTab, #tmpTab+1, PointNumTab[cardPoint-i][1])
            elseif cardPoint-i == 1 and #PointNumTab[14]>0 then
                table.insert(tmpTab, #tmpTab+1, PointNumTab[14][1])
            elseif laiziIndex<=laiziNum then
                table.insert(tmpTab, #tmpTab+1, LaiziTab[laiziIndex])
                laiziIndex = laiziIndex + 1
            else
                break
            end
        end
        if #tmpTab == 5 then
            ret = true
            break
        end
    end
    if ret then
        if startPoint == 6 and self:IsLaizi(tmpTab[1]) then
            --当顺子为65432，且6为癞子时，转换成5432A
            table.insert(tmpTab, #tmpTab+1, tmpTab[1])
            table.remove(tmpTab,1)
        elseif startPoint==7 and self:IsLaizi(tmpTab[1]) and self:IsLaizi(tmpTab[2]) then
            table.insert(tmpTab, #tmpTab+1, tmpTab[1])
            table.remove(tmpTab,1)
            table.insert(tmpTab, #tmpTab+1, tmpTab[1])
            table.remove(tmpTab,1)
        end
        for k,v in pairs(tmpTab) do
            tab[k] = v
        end
    end
    return ret
end

-- 判断三条(带癞子)
function GameLogic:judgeThree_L(tab,paraseStruct)
    local ret = false
    local LaiziTab = paraseStruct.LaiziTab 
    local PointNumTab = paraseStruct.PointNumTab
    local laiziNum = #LaiziTab
    local ThreePoint = 14
    for point = #PointNumTab,1,-1 do 
        if #PointNumTab[point]+laiziNum == 3 then
            ThreePoint = point
            ret = true
            break
        end
    end
    if ret then
        table.sort(tab,function(a,b)
            local bAlaizi = self:IsLaizi(a)
            local bBlaizi = self:IsLaizi(b)
            local aPoint = self:getCardPoint(a)
            local bPoint = self:getCardPoint(b)
            local aHuakind = self:GetCardHuaKind(a)
            local bHuakind = self:GetCardHuaKind(b)
            if aPoint == ThreePoint and bPoint ~= ThreePoint then
                return true
            elseif bAlaizi and not bBlaizi and bPoint ~= ThreePoint then
                return true
            elseif (bAlaizi and bBlaizi)then
                if (aPoint > bPoint) or (apoint == bpoint and aHuakind>bHuakind) then
                    return true
                end
            elseif ((not bAlaizi) and (not bBlaizi) and aPoint ~= ThreePoint and bPoint ~= ThreePoint) then
                if (aPoint > bPoint) or (aPoint == bPoint and aHuakind>bHuakind) then
                    return true
                end
            end
            return false
        end)
    end
    return ret
end

-- 判断两对(带癞子)
function GameLogic:judgeTwopairs_L(tab,paraseStruct)
--两对牌型不可能有癞子
    if #tab ~= 5 then
        return false
    end
    local ret = false
    local PointNumTab = paraseStruct.PointNumTab
    local singCard = 0
    local pairNum = 0
    for point = #PointNumTab,1,-1 do 
        if #PointNumTab[point] == 2 then
            pairNum = pairNum+1
        elseif #PointNumTab[point] == 1 then
            singCard = PointNumTab[point][1]
        end
    end
    if pairNum ==2 then
        ret = true
    end
    
    if ret then
        for k,v in pairs(tab) do
            if v==singCard then
                table.remove(tab, k)
                table.insert(tab, #tab+1, singCard)
                break
            end
        end
    end
    return ret
end

-- 判断对子(带癞子)
function GameLogic:judgePairs_L(tab,paraseStruct)
    local ret = false
    local LaiziTab = paraseStruct.LaiziTab 
    local PointNumTab = paraseStruct.PointNumTab
    local laiziNum = #LaiziTab
    local PairPoint = 14
    for point = #PointNumTab,1,-1 do 
        if #PointNumTab[point]+laiziNum == 2 then
            PairPoint = point
            ret = true
            break
        end
    end
    if ret then
        table.sort(tab,function(a,b)
            local bAlaizi = self:IsLaizi(a)
            local bBlaizi = self:IsLaizi(b)
            local aPoint = self:getCardPoint(a)
            local bPoint = self:getCardPoint(b)
            local aHuakind = self:GetCardHuaKind(a)
            local bHuakind = self:GetCardHuaKind(b)
            if (aPoint == PairPoint and bPoint ~= PairPoint) or (aPoint ~= PairPoint and bPoint ~= PairPoint and aPoint>bPoint) then
                return true
            else 
                return false
            end
        end)
    end
    return ret
end


------------------------------------------------特殊牌型判断
-- 判断特殊牌型
function GameLogic:judgeSpecialCardType(tab)
    local special_card_type = 0
    if self:judgeQingLong(tab) then
        special_card_type = SpecialCardType.SCT_QL
    elseif self:judgeYiTiaoLong(tab) then
        special_card_type = SpecialCardType.SCT_YTL
    elseif self:judgeSTongHuaShun(tab) then
        special_card_type = SpecialCardType.SCT_STHS
    elseif self:judgeSFTianXia(tab) then
        special_card_type = SpecialCardType.SCT_SFTX
    elseif self:judgeSTSanTiao(tab) then
        special_card_type = SpecialCardType.SCT_STST
    elseif self:judgeLiuDuiBan(tab) then
        special_card_type = SpecialCardType.SCT_LDB
    elseif self:judgeSanShunZi(tab) then
        special_card_type = SpecialCardType.SCT_SSZ
    elseif self:judgeSanTongHua(tab) then
        special_card_type = SpecialCardType.SCT_STH
    end

    return special_card_type
end

-- 判断清龙
function GameLogic:judgeQingLong(tab)
    if self:getHuaKindNumByCard(tab[1],tab) == 13 and self:judgeYiTiaoLong(tab) then
        return true
    end
    return false
end

-- 判断一条龙
function GameLogic:judgeYiTiaoLong(tab)
    local card = 14
    local flag = true

    for i=1,#tab do
        if self:getCardPoint(tab[i]) == card then
            card = card - 1
        else
            flag = false
            break
        end 
    end

    return flag
end

-- 判断三同花顺
function GameLogic:judgeSTongHuaShun(tab)
    local flag = false
    if self:judgeSanTongHua(tab) and self:judgeSanShunZi(tab) then
        flag = true
    end

    return flag
end

-- 判断三分天下
function GameLogic:judgeSFTianXia(tab)
    local flag = false
    local num = 0

    local tempCardPoint = 0
    for i=1,#tab do
        local cardPoint = self:getCardPoint(tab[i])
        if tempCardPoint ~= cardPoint  and self:getCardNum(tab[i],tab) == 4 then
            num = num + 1
            tempCardPoint = cardPoint
        end
    end

    if num == 3 then
        flag = true
    end

    return flag
end

-- 判断四套三条
function GameLogic:judgeSTSanTiao(tab)
    local flag = false
    local num = 0

    local tempCardPoint = 0
    for i = 1, #tab do
        local cardPoint = self:getCardPoint(tab[i])
        if tempCardPoint ~= cardPoint and self:getCardNum(tab[i], tab) == 3 then
            num = num + 1
            tempCardPoint = cardPoint
        end
    end

    if num == 4 then
        flag = true
    end

    return flag
end

-- 判断六对半
function GameLogic:judgeLiuDuiBan(tab)
    local flag = false
    local num = 0

    local tempCardPoint = 0
    for i = 1, #tab do
        local cardPoint = self:getCardPoint(tab[i])
        if tempCardPoint ~= cardPoint and self:getCardNum(tab[i], tab) % 2 == 1 then
            num = num + 1
            tempCardPoint = cardPoint
        end
    end

    if num == 1 then
        flag = true
    end

    return flag
end

-- 判断三顺子
function GameLogic:judgeSanShunZi(tab)
    local flag = false
    local cardPoint = 0

    for i = 1, 2 do
        local tempTab1 = clone(tab)
        local tempTab2 = clone(tab)
        local tempTab3 = clone(tab)
        if i == 1 then
            self:sortBySize(tempTab1)
            self:sortBySize(tempTab2)
            self:sortBySize(tempTab3)
        else
            self:sortBySizeNormal(tempTab1)
            self:sortBySizeNormal(tempTab2)
            self:sortBySizeNormal(tempTab3)
        end

        -- 3 5 5顺子
        cardPoint = self:getCardPointByNormal(tempTab1[1], i)
        if self:isInTable(cardPoint - 1, tempTab1) and self:isInTable(cardPoint - 2, tempTab1) then
            for j = 0, 2 do
                self:removeOneCardByCardPoint(tempTab1,cardPoint - j)
            end
            cardPoint = self:getCardPointByNormal(tempTab1[1], i)
            if self:isInTable(cardPoint - 1, tempTab1) and self:isInTable(cardPoint - 2, tempTab1) and
                self:isInTable(cardPoint - 3, tempTab1) and self:isInTable(cardPoint - 4, tempTab1) then
                for j = 0, 4 do
                    self:removeOneCardByCardPoint(tempTab1,cardPoint - j)
                end
                cardPoint = self:getCardPointByNormal(tempTab1[1], i)
                if self:isInTable(cardPoint - 1, tempTab1) and self:isInTable(cardPoint - 2, tempTab1) and
                    self:isInTable(cardPoint - 3, tempTab1) and self:isInTable(cardPoint - 4, tempTab1) then
                    flag = true
                end
            end
        end

        -- 5 5 3顺子
        cardPoint = self:getCardPointByNormal(tempTab2[1], i)
        if self:isInTable(cardPoint - 1, tempTab2) and self:isInTable(cardPoint - 2, tempTab2) and
            self:isInTable(cardPoint - 3, tempTab2) and self:isInTable(cardPoint - 4, tempTab2) then
            for j = 0, 4 do
                self:removeOneCardByCardPoint(tempTab2,cardPoint - j)
            end
            cardPoint = self:getCardPointByNormal(tempTab2[1], i)
            if self:isInTable(cardPoint - 1, tempTab2) and self:isInTable(cardPoint - 2, tempTab2) and
                self:isInTable(cardPoint - 3, tempTab1) and self:isInTable(cardPoint - 4, tempTab2) then
                for j = 0, 4 do
                    self:removeOneCardByCardPoint(tempTab2,cardPoint - j)
                end
                cardPoint = self:getCardPointByNormal(tempTab2[1], i)
                if self:isInTable(cardPoint - 1, tempTab2) and self:isInTable(cardPoint - 2, tempTab2) then
                    flag = true
                end
            end
        end

        -- 5 3 5顺子
        cardPoint = self:getCardPointByNormal(tempTab3[1], i)
        if self:isInTable(cardPoint - 1, tempTab3) and self:isInTable(cardPoint - 2, tempTab3) and
            self:isInTable(cardPoint - 3, tempTab3) and self:isInTable(cardPoint - 4, tempTab3) then
            for j = 0, 4 do
                self:removeOneCardByCardPoint(tempTab3,cardPoint - j)
            end
            cardPoint = self:getCardPointByNormal(tempTab3[1], i)
            if self:isInTable(cardPoint - 1, tempTab3) and self:isInTable(cardPoint - 2, tempTab3) then
                for j = 0, 2 do
                    self:removeOneCardByCardPoint(tempTab3,cardPoint - j)
                end
                cardPoint = self:getCardPointByNormal(tempTab3[1], i)
                if self:isInTable(cardPoint - 1, tempTab3) and self:isInTable(cardPoint - 2, tempTab3) and
                    self:isInTable(cardPoint - 3, tempTab3) and self:isInTable(cardPoint - 4, tempTab3) then
                    flag = true
                end
            end
        end


    end


    return flag
end

-- 判断三同花
function GameLogic:judgeSanTongHua(tab)
    dump(tab, "tab=")
    local flag = true
    for i=1,#tab do
        local num = self:getHuaKindNumByCard(tab[i],tab)
        if num ~= 3 and num ~= 5 and num ~= 8 and num ~= 13 then
            flag = false
            break
        end
    end
    return flag
end



------------------------------------------------特殊牌型判断(带癞子)
-- 判断特殊牌型(带癞子)
function GameLogic:judgeSpecialCardType_L(Cardtab)
    local tab = clone(Cardtab)
    self:sortBySize(tab)
    local special_card_type = 0
    local bAddPoint = false

    --有大小王不支持特殊牌型
    local bhasGui = false
    for _,v in pairs(tab) do
        if v==53 or v==54 then
            bhasGui = true 
            break
        end
    end

     --分析手牌组成
    local paraseStruct = self:ParaseCards(tab,true)

    if self:judgeQingLong_L(tab,paraseStruct) and self:bExistCurSpecialCardType(SpecialCardType.SCT_QL) then
        special_card_type = SpecialCardType.SCT_QL
    --elseif not bhasGui and self:judgeQitong_L(tab,paraseStruct) then
    --   special_card_type = SpecialCardType.SCT_QT
    elseif self:judeAllRed(tab,paraseStruct) and self:bExistCurSpecialCardType(SpecialCardType.SCT_ALLRED) then
        special_card_type = SpecialCardType.SCT_ALLRED
    elseif self:judeAllBlack(tab,paraseStruct) and self:bExistCurSpecialCardType(SpecialCardType.SCT_ALLBLACK) then
        special_card_type = SpecialCardType.SCT_ALLBLACK

    elseif self:judgeYiTiaoLong_L(tab,paraseStruct) and self:bExistCurSpecialCardType(SpecialCardType.SCT_YTL) then
        special_card_type = SpecialCardType.SCT_YTL
    elseif self:judgeOneBlack(tab,paraseStruct) and self:bExistCurSpecialCardType(SpecialCardType.SCT_ONEBLACK) then
        special_card_type = SpecialCardType.SCT_ONEBLACK
    elseif self:judgeOneRed(tab,paraseStruct) and self:bExistCurSpecialCardType(SpecialCardType.SCT_ONERED) then
        special_card_type = SpecialCardType.SCT_ONERED
    elseif self:judgeSTSanTiao_L(tab,paraseStruct) and self:bExistCurSpecialCardType(SpecialCardType.SCT_STST) then
        special_card_type = SpecialCardType.SCT_STST
    elseif self:judgeWDSanTiao_L(tab,paraseStruct) and self:bExistCurSpecialCardType(SpecialCardType.SCT_WDST)  then
        special_card_type = SpecialCardType.SCT_WDST
    elseif self:judgeQuanDa_L(tab) and self:bExistCurSpecialCardType(SpecialCardType.SCT_QD) then
        special_card_type = SpecialCardType.SCT_QD
    elseif self:judgeQuanXiao_L(tab) and self:bExistCurSpecialCardType(SpecialCardType.SCT_QX) then
        special_card_type = SpecialCardType.SCT_QX

    
--    elseif  not bhasGui and self:judgeSTongHuaShun_L(tab,paraseStruct) then
--        special_card_type = SpecialCardType.SCT_STHS
--    elseif  not bhasGui and self:judgeLiutong_L(tab,paraseStruct) then
--        special_card_type = SpecialCardType.SCT_LT

--    elseif self:judgeSFTianXia_L(tab) then
--        special_card_type = SpecialCardType.SCT_SFTX
    
   
--    elseif self:judgeCouYise_L(tab) then
--        special_card_type = SpecialCardType.SCT_CYS
    
    elseif  not bhasGui then
        local bLiuduiban
        local bLiuduibanHasTiezhi
        local bSanShunzi
        local bSanShunziHasTonghuashun
        local bSanTonghua
        local bSanTonghuaHasTonghuashun
        bliuduiban,bLiuduibanHasTiezhi = self:judgeLiuDuiBan_L(tab,paraseStruct)
        bSanShunzi,bSanShunziHasTonghuashun = self:judgeSanShunZi_L(tab,paraseStruct)
        bSanTonghua,bSanTonghuaHasTonghuashun = self:judgeSanTongHua_L(tab,paraseStruct)
        if bliuduiban and self:bExistCurSpecialCardType(SpecialCardType.SCT_LDB) then
            special_card_type = SpecialCardType.SCT_LDB
            if bLiuduibanHasTiezhi then
                bAddPoint = true
            end
        elseif bSanShunzi  and self:bExistCurSpecialCardType(SpecialCardType.SCT_SSZ) then
            special_card_type = SpecialCardType.SCT_SSZ
             if bSanShunziHasTonghuashun then
                bAddPoint = true
            end
        elseif bSanTonghua  and self:bExistCurSpecialCardType(SpecialCardType.SCT_STH) then
            special_card_type = SpecialCardType.SCT_STH
             if bSanTonghuaHasTonghuashun then
                bAddPoint = true
            end
        elseif self:judgeBanDa(tab) and self:bExistCurSpecialCardType(SpecialCardType.SCT_HALF_BIG) then
            special_card_type = SpecialCardType.SCT_HALF_BIG
        elseif self:judgeBanXiao(tab) and self:bExistCurSpecialCardType(SpecialCardType.SCT_HALF_SMALL) then
            special_card_type = SpecialCardType.SCT_HALF_SMALL
        end
    end

    return special_card_type,bAddPoint
end

-- 判断清龙(带癞子)
function GameLogic:judgeQingLong_L(tab,paraseStruct)
    local ret = false
    local HuaKindTab = paraseStruct.HuaKindTab 
    local laiziNum = #paraseStruct.LaiziTab
    local sameHuaKind = false
    for i = 1,#HuaKindTab do
        if laiziNum + #HuaKindTab[i] == 13 then
            sameHuaKind = true 
            break
        end
    end
    if sameHuaKind and self:judgeYiTiaoLong_L(tab,paraseStruct) then
        ret = true
    end

    return ret
end

-- 判断一条龙(带癞子)
function GameLogic:judgeYiTiaoLong_L(tab,paraseStruct)
    local ret = true
    local LaiziTab = paraseStruct.LaiziTab
    local PointNumTab = paraseStruct.PointNumTab 
    local laiziNum = #paraseStruct.LaiziTab
    for cardPoint = 2,14 do
        if #PointNumTab[cardPoint] <= 0 then
            if laiziNum <= 0 then
                ret = false
                break
            else
                laiziNum = laiziNum - 1
            end
        end
    end
    return ret
end

-- 判断七同(带癞子)
function GameLogic:judgeQitong_L(tab,paraseStruct)
    local ret = false
    local LaiziTab = paraseStruct.LaiziTab
    local PointNumTab = paraseStruct.PointNumTab 
    local laiziNum = #paraseStruct.LaiziTab
    for cardPoint = 2,14 do
        if #PointNumTab[cardPoint]+laiziNum >= 7 then
            ret = true
            break
        end
    end
    return ret
end

-- 判断三同花顺(带癞子)
function GameLogic:judgeSTongHuaShun_L(tab,paraseStruct)
    local flag = false
    local HuaKindNumsTab ={0,0,0,0}
    local MinKindnumsIndex = 1
    local MaxKindnumsIndex = 1
    local hasHuakindNums = 0
    local wangnums = #paraseStruct.LaiziTab
    local needwangnums1 = 0
    local needwangnums2 = 0
    local needwangnums3 =0
    for i =0, 3 do
        HuaKindNumsTab[i+1] = #paraseStruct.HuaKindTab[i+1]
        if HuaKindNumsTab[i+1] > 0 then
            hasHuakindNums = hasHuakindNums + 1
        end
        if (HuaKindNumsTab[MinKindnumsIndex] > HuaKindNumsTab[i+1] or HuaKindNumsTab[MinKindnumsIndex] == 0) and HuaKindNumsTab[i+1] ~= 0 then
            MinKindnumsIndex = i+1
        end
        if HuaKindNumsTab[MaxKindnumsIndex] < HuaKindNumsTab[i+1] or HuaKindNumsTab[MaxKindnumsIndex] == 0 then
            MaxKindnumsIndex = i+1
        end
    end

    if hasHuakindNums == 1 then --仅有一个花色的牌是青龙，也是三同花顺
        flag =true
    elseif hasHuakindNums == 2 then --两花色
        if HuaKindNumsTab[MinKindnumsIndex] <= 3 then --花色牌最少的做头道
            wangnums = wangnums - 3 + HuaKindNumsTab[MinKindnumsIndex]
            if (wangnums + HuaKindNumsTab[MaxKindnumsIndex]) == 10 then
                --能够组成三套同花，每套同花进行顺子判断
                local tempTap1 = clone(paraseStruct.HuaKindTab[MinKindnumsIndex])
                local bshunzi1 = false
                needwangnums1 = 0
                needwangnums2 = 0
                needwangnums3 =0
                for i = 1, 12 do
                    needwangnums1 = self:GetShunziNeewangNums(i, tempTap1, true)
                    if needwangnums1 == (3 - HuaKindNumsTab[MinKindnumsIndex]) then 
                        bshunzi1 = true
                        break   
                    end
                end
                if not bshunzi1 then
                    return false
                end
                local tempTap2 = clone(paraseStruct.HuaKindTab[MaxKindnumsIndex])
                for i = 1, 10 do
                    needwangnums2 = self:GetShunziNeewangNums(i, tempTap2)
                    if needwangnums2 <= wangnums then
                        local tempTap3 = clone(tempTap2)
                        for j =0,4 do
                            self:removeOneCardByCardPoint(tempTap3, i+j)
                        end
                        for j = 1, 10 do
                            needwangnums3 = self:GetShunziNeewangNums(j, tempTap3)
                            if (needwangnums2 + needwangnums3) == wangnums then
                                return true
                            end
                        end
                    end
                end
            end
        elseif HuaKindNumsTab[MinKindnumsIndex] <= 5 then  --花色最少的放在中道或尾道
            wangnums = wangnums - 5 + HuaKindNumsTab[MinKindnumsIndex]
            needwangnums1 = 0
            needwangnums2 = 0
            needwangnums3 =0
            if (wangnums + HuaKindNumsTab[MaxKindnumsIndex]) == 8 then
                --flag = true
                local bshunzi1 = false
                local tempTap1 = clone(paraseStruct.HuaKindTab[MinKindnumsIndex])
                for i = 1, 10 do
                    if self:GetShunziNeewangNums(i, tempTap1) == (5 - HuaKindNumsTab[MinKindnumsIndex])then--保证全部的牌都用上
                        bshunzi1 = true
                    end
                end
                if not bshunzi1 then
                     return false
                end
                local tempTap2 = clone(paraseStruct.HuaKindTab[MaxKindnumsIndex])
                for i = 1, 10 do
                    needwangnums2 = self:GetShunziNeewangNums(i, tempTap2)
                    if needwangnums2 <= wangnums then
                        local tempTap3 = clone(tempTap2)
                        for j =0,4 do
                            self:removeOneCardByCardPoint(tempTap3, i+j)
                        end
                        for j = 1, 12 do
                            needwangnums3 = self:GetShunziNeewangNums(j, tempTap3, true)
                            if (needwangnums2 + needwangnums3) == wangnums then
                                return true
                            end
                        end
                    end
                end
            end
        end 
    elseif hasHuakindNums == 3 then --三花色  
        if HuaKindNumsTab[MinKindnumsIndex] <= 3 and 
        HuaKindNumsTab[MaxKindnumsIndex] <= 5 and
        ( wangnums - (3 - HuaKindNumsTab[MinKindnumsIndex]) - (5 - HuaKindNumsTab[MaxKindnumsIndex]) ) >= 0 then
            --flag = true
            local bshunzi = false
            local tempTab1 = clone(paraseStruct.HuaKindTab[MaxKindnumsIndex])
            for i = 1, 10 do 
                needwangnums1 = self:GetShunziNeewangNums(i, tempTab1)
                if needwangnums1 == (5 - HuaKindNumsTab[MaxKindnumsIndex]) then
                    bshunzi = true
                    break
                end
            end
            if not bshunzi then
                return false
            end
            local MidKindIndex
            for i = 1, 4 do
                if i~=MinKindnumsIndex and i~=MaxKindnumsIndex and HuaKindNumsTab[i] ~= 0 then
                    MidKindIndex = i
                    break
                end
            end
            local tempTap2 =  clone(paraseStruct.HuaKindTab[MidKindIndex])
            local tempTap3 = clone(paraseStruct.HuaKindTab[MinKindnumsIndex])
            --MidKindIndex 在中道或尾道
            for i = 1, 10 do 
                needwangnums2 = self:GetShunziNeewangNums(i, tempTap2) 
                if (3 - HuaKindNumsTab[MinKindnumsIndex]) == (wangnums - needwangnums1-needwangnums2) then
                    for i = 1, 12 do 
                        needwangnums3 = self:GetShunziNeewangNums(i, tempTap3, true) 
                        if (needwangnums1+needwangnums2+needwangnums3) == wangnums then
                            return true
                        end
                    end 
                end                
            end

            if HuaKindNumsTab[MidKindIndex] == 3 and (wangnums-needwangnums1) == 2 then  --MidKindIndex和MinKindnumsIndex牌的数量都等于3，MinKindnumsIndex作为中道或尾道
                for i = 1, 10 do 
                    if self:GetShunziNeewangNums(i, tempTap3) == 2 then
                        for j = 1,12 do
                            if self:GetShunziNeewangNums(j, tempTap2) then
                                return true
                            end
                        end
                                         
                    end
                end
            end

        end
    end
    return flag
end

-- 判断六同(带癞子)
function GameLogic:judgeLiutong_L(tab,paraseStruct)
    local ret = false
    local LaiziTab = paraseStruct.LaiziTab
    local PointNumTab = paraseStruct.PointNumTab 
    local laiziNum = #paraseStruct.LaiziTab
    for cardPoint = 2,14 do
        if #PointNumTab[cardPoint]+laiziNum >= 6 then
           ret = true
        end
    end
    return ret
end

-- 判断三分天下(带癞子)
function GameLogic:judgeSFTianXia_L(tab)
    local flag = false
    local fournums = 0
    local threenums = 0
    local pairsnums = 0
    local wangnums = self:getWangnumInfo(tab)
    
    local tempCardPoint = 0
    for i=1,#tab do
        local cardPoint = self:getCardPoint(tab[i])
        if tempCardPoint ~= cardPoint then
            if self:getCardNum(tab[i],tab) == 4 then
                fournums = fournums + 1
            elseif self:getCardNum(tab[i],tab) == 3 then
                threenums = threenums + 1
            elseif self:getCardNum(tab[i],tab) == 2 then
                pairsnums = pairsnums + 1
            end
        end
        tempCardPoint = cardPoint
    end
    if (fournums == 3) or
        (fournums == 2 and threenums == 1 and wangnums > 0) or
        (fournums == 2 and pairsnums == 1 and wangnums == 2) or
        (fournums == 1 and threenums == 2 and wangnums == 2)then
        flag = true
    end

    return flag
end

-- 判断四套三条(带癞子)
function GameLogic:judgeSTSanTiao_L(tab,paraseStruct)
    local ret = false
    local PointNumTab = paraseStruct.PointNumTab 
    local laiziNum = #paraseStruct.LaiziTab
    local threenums = 0
    local pairsnums = 0

    for _,cards in pairs(PointNumTab) do
        if #cards>=3 then
            threenums = threenums+1
        elseif #cards==2 then
            pairsnums = pairsnums + 1
        end
    end
 
    if  (threenums == 4) or
        (threenums == 3 and pairsnums == 1 and laiziNum > 0) or
        (threenums == 3 and pairsnums == 0 and laiziNum == 2) or
        (threenums == 2 and pairsnums == 2 and laiziNum == 2) then
        ret = true
    end

    return ret
end

function GameLogic:judgeWDSanTiao_L(tab,paraseStruct)
    local ret = false
    local PointNumTab = paraseStruct.PointNumTab 
    local laiziNum = #paraseStruct.LaiziTab
    local threenums = 0
    local pairsnums = 0

    for _,cards in pairs(PointNumTab) do
        if #cards==4 then
            pairsnums = pairsnums+2
        elseif #cards==3 then
            threenums = threenums+1
        elseif #cards==2 then
            pairsnums = pairsnums + 1
        end
    end
 
    if  (pairsnums == 5 and threenums == 1) or
        (pairsnums == 6 and laiziNum > 0) or
        (pairsnums == 5 and laiziNum >= 2) or
        (pairsnums == 4 and threenums == 1 and laiziNum == 1)  then
        ret = true
    end

    return ret
end

-- 判断全大(带癞子)
function GameLogic:judgeQuanDa_L(tab)
    for i=1,#tab do
        local cardPoint = self:getCardPoint(tab[i])
        if cardPoint < 6 or cardPoint >= 14 then
            return false
        end
    end
    return true
end

-- 半大
function GameLogic:judgeBanDa(tab)
    for i=1,#tab do
        local cardPoint = self:getCardPoint(tab[i])
        if cardPoint < 6 then
            return false
        end
    end
    return true
end

-- 判断全小(带癞子)
function GameLogic:judgeQuanXiao_L(tab)
    for i=1,#tab do
        local cardPoint = self:getCardPoint(tab[i])
        if cardPoint > 10   then
            return false
        end
    end
    return true
end

-- 半小
function GameLogic:judgeBanXiao(tab)
    for i=1,#tab do
        local cardPoint = self:getCardPoint(tab[i])
        print("judgeBanXiao---->cardPoint = " .. cardPoint)
        if cardPoint > 10 and cardPoint < 14  then
            print("judgeBanXiao---->cardPoint > 10 and cardPoint < 14 ")
            return false
        end
    end
    print("judgeBanXiao---->return true ")
    return true
end

-- 一点黑
function GameLogic:judgeOneBlack(tab)
    local blackCount = 0
    local redCount = 0
    for i=1,#tab do
        local cardKind = self:GetCardHuaKind(tab[i])
        if tab[i] == 53  then
            blackCount = blackCount + 1
        elseif tab[i] == 54 then
            redCount = redCount + 1
        elseif cardKind == 1 or  cardKind == 3 then
            blackCount = blackCount + 1
        else
            redCount = redCount + 1
        end
    end
    if blackCount == 1  then
        return true
    end
    return false
end

-- 一点红
function GameLogic:judgeOneRed(tab)
    local blackCount = 0
    local redCount = 0
    for i=1,#tab do
        local cardKind = self:GetCardHuaKind(tab[i])
        if tab[i] == 53  then
            blackCount = blackCount + 1
        elseif tab[i] == 54 then
            redCount = redCount + 1
        elseif cardKind == 1 or  cardKind == 3 then
            blackCount = blackCount + 1
        else
            redCount = redCount + 1
        end
    end
    if redCount == 1  then
        return true
    end
    return false
end

-- 全红
function GameLogic:judeAllRed(tab)
    local blackCount = 0
    local redCount = 0
    for i=1,#tab do
        local cardKind = self:GetCardHuaKind(tab[i])
        if tab[i] == 53  then
            blackCount = blackCount + 1
        elseif tab[i] == 54 then
            redCount = redCount + 1
        elseif cardKind == 1 or  cardKind == 3 then
            blackCount = blackCount + 1
        else
            redCount = redCount + 1
        end
    end
    if blackCount == 0  then
        return true
    end
    return false
end

-- 全黑
function GameLogic:judeAllBlack(tab)
    local blackCount = 0
    local redCount = 0
    for i=1,#tab do
        local cardKind = self:GetCardHuaKind(tab[i])
        if tab[i] == 53  then
            blackCount = blackCount + 1
        elseif tab[i] == 54 then
            redCount = redCount + 1
        elseif cardKind == 1 or  cardKind == 3 then
            blackCount = blackCount + 1
        else
            redCount = redCount + 1
        end
    end
    if redCount == 0  then
        return true
    end
    return false
end

-- 判断凑一色(带癞子)0.方块Diamond 1.梅花Club 2.红桃Hearts 3.黑桃Spade
function GameLogic:judgeCouYise_L(tab)
    local wangnums = self:getWangnumInfo(tab)
    local DiamondNums = self:getHuaKindNumByHK(0, tab)
    local HeartsNums = self:getHuaKindNumByHK(2, tab)
    if ((wangnums + DiamondNums + HeartsNums) == 13) or (DiamondNums == 0 and HeartsNums == 0) then
        return true
    end
    return false
end

-- 判断六对半(带癞子)
function GameLogic:judgeLiuDuiBan_L(tab,paraseStruct)
    local ret = false
    local PointNumTab = paraseStruct.PointNumTab 
    local laiziNum = #paraseStruct.LaiziTab
    local threenums = 0
    local pairsnums = 0
    local fournums = 0
    local bHasTiezhi = false
    for _,cards in pairs(PointNumTab) do
        if #cards==4 then
            fournums = fournums+1
        elseif #cards==3 then
            threenums = threenums+1
        elseif #cards==2 then
            pairsnums = pairsnums + 1
        end
    end

    if (fournums * 2 + threenums + pairsnums + laiziNum) >= 6 then
        ret = true
    end
    if fournums>0 or (threenums>0 and laiziNum>0) or (pairsnums == 5 and laiziNum == 2) then
        bHasTiezhi = true
    end
    return ret,bHasTiezhi
end

-- 判断三顺子(带癞子)
function GameLogic:judgeSanShunZi_L(tab,paraseStruct)
    local cardPoint = 0
    local wangnums = #paraseStruct.LaiziTab
    local bHasTonghuashun = false
    for cardPoint1 = 1, 10 do
        local tempTab1 = {}
        for _,cards in pairs(paraseStruct.HuaKindTab) do
            if #cards >0 then
                for _,card in pairs(cards) do 
                    table.insert(tempTab1, card)
                end
            end
        end
        
        local needwangnums1 = self:GetShunziNeewangNums(cardPoint1, tempTab1)
        if needwangnums1 <= wangnums then
            bHasTonghuashun = self:IsShunziTonghua(cardPoint1,tab,needwangnums1)
            for i = 0,4 do
                self:removeOneCardByCardPoint(tempTab1 ,cardPoint1+i)
            end
            for cardPoint2 = 1, 10 do
                local tempTab2 = clone(tempTab1)
                local needwangnums2 = self:GetShunziNeewangNums(cardPoint2, tempTab2)
                if (needwangnums1 + needwangnums2) <= wangnums then
                    bHasTonghuashun = self:IsShunziTonghua(cardPoint2,tab,needwangnums2)
                    for j = 0,4 do
                        self:removeOneCardByCardPoint(tempTab2 ,cardPoint2+j)
                    end
                    if #tempTab2 == 1 then
                        return true, bHasTonghuashun
                    elseif #tempTab2 == 2 then
                        local difference = math.abs(self:getCardPoint(tempTab2[1]) - self:getCardPoint(tempTab2[2]))
                        if difference <= 2 and difference > 0 then
                            return true,bHasTonghuashun
                        end
                    elseif #tempTab2 == 3 then
                        self:sortBySize(tempTab2)
                        local point1 = self:getCardPoint(tempTab2[1])
                        local point2 = self:getCardPoint(tempTab2[2])
                        local point3 = self:getCardPoint(tempTab2[3])
                        if point1 == 14 and point2 == 3 and point3 == 2 then
                            return true,bHasTonghuashun
                        end
                        if (point2+1) == point1 then
                            if (point3 + 2) == point1  then
                                return true,bHasTonghuashun
                            elseif point3==14 and point2 == 2 then
                                return true,bHasTonghuashun
                            end
                        end
                    end
                end
            end
        end     
    end
    return false,bHasTonghuashun
end

-- 判断三同花(带癞子)
function GameLogic:judgeSanTongHua_L(tab,paraseStruct)
    local ret = false
    local HuaKindTab = paraseStruct.HuaKindTab 
    dump(HuaKindTab, "HuaKindTab =")
    local laiziNum = #paraseStruct.LaiziTab
    for _,cards in pairs(HuaKindTab) do
        local cardNum = #cards
        if cardNum > 0 then
            if cardNum<=3 then
                laiziNum = laiziNum - (3 - cardNum)
            elseif cardNum <=5 then
                laiziNum = laiziNum - (5 - cardNum)
            elseif cardNum <=8 then
                --laiziNum = laiziNum - (8 - cardNum)
                return false
            elseif cardNum <=10 then
                --laiziNum = laiziNum - (10 - cardNum)   
                return false
            end
        end
    end
    if laiziNum >= 0 then
        ret = true
    end
    return ret
end



-- 手牌排序 0.按大小排序 1.按花色排序
function GameLogic:sort(tab, sort_type)
    if sort_type == 0 then
        self:sortBySize(tab)
    elseif sort_type == 1 then
        self:sortByHuaKind(tab)
    end
end



-- 按大小排序,1为14
function GameLogic:sortBySize(tab)
    for i = 1, #tab do
        table.sort(tab, function(a, b)
            m = self:getCardPoint(a)
            n = self:getCardPoint(b)
            return m > n

        end )
    end

    -- 大小相同，按花色排序 黑 红 梅 方
    local point = 0
    for i = 1, #tab do
        local cardpoint = self:getCardPoint(tab[i])
        if point ~= cardpoint then
            local num = self:getCardNum(tab[i], tab)
            if num > 1 then
                -- 点数相同的tab
                local cardtab = {}
                for j = 1,num do
                    cardtab[j] = tab[i + j - 1]
                end
                --self:sortByHuaKind(cardtab)
                for i = 1, #cardtab do
                    table.sort(cardtab, function(a, b)
                        m = self:GetCardHuaKind(a)
                        n = self:GetCardHuaKind(b)
                        return m > n
                    end )
                end

                for j = 1,num do
                    tab[i + j - 1] = cardtab[j]
                end

            end
            point = cardpoint
        end

    end
end

-- 按大小排序,1为1
function GameLogic:sortBySizeNormal(tab)
    for i = 1, #tab do
        table.sort(tab, function(a, b)
            m = self:getCardPointByNormal(a,2)
            n = self:getCardPointByNormal(b,2)
            return m > n

        end )
    end

    -- 大小相同，按花色排序 黑 红 梅 方
    local point = 0
    for i = 1, #tab do
        local cardpoint = self:getCardPointByNormal(tab[i],2)
        if point ~= cardpoint then
            local num = self:getCardNum(tab[i], tab)
            if num > 1 then
                -- 点数相同的tab
                local cardtab = {}
                for j = 1,num do
                    cardtab[j] = tab[i + j - 1]
                end
                self:sortByHuaKind(cardtab)

                for j = 1,num do
                    tab[i + j - 1] = cardtab[j]
                end

            end
            point = cardpoint
        end

    end
end

-- 按花色排序
function GameLogic:sortByHuaKind(tab)
    for i = 1, #tab do
        table.sort(tab, function(a, b)
            m = self:GetCardHuaKind(a)
            n = self:GetCardHuaKind(b)
            return m > n

        end )
    end

    -- 花色相同,按大小排序
    local huaKind = -1
    for i = 1, #tab do
        local cardHuaKind = self:GetCardHuaKind(tab[i])
        if huaKind ~= cardHuaKind then
            local num = self:getHuaKindNumByCard(tab[i], tab)
            if num > 1 then
                -- 花色相同的tab
                local cardtab = {}
                for j = 1,num do
                    cardtab[j] = tab[i + j - 1]
                end
                self:sortBySize(cardtab)

                for j = 1,num do
                    tab[i + j - 1] = cardtab[j]
                end

            end
            huaKind = cardHuaKind
        end

    end

end
function GameLogic:getWangnumInfo(tab)
    local wangnums = 0
    local bhasXiaowang = false
    local bhasDawang =false
    for _,v in pairs(tab) do
        if v == 53 then 
            wangnums = wangnums + 1
            bhasXiaowang = true
        elseif v == 54 then
            wangnums = wangnums + 1
            bhasDawang = true
        end
    end
    return wangnums, bhasXiaowang, bhasDawang
end
-- 获取扑克牌点数,1的点数为14,小王的点数为15，大王的点数为16
function GameLogic:getCardPoint(card)
    if card == nil then
        return -1
    end
    if card == 0 then 
        return 0
    end
    if card == 53 then
        return 15  
    end
    if card == 54 then
        return 16
    end

    local point = card % 13
    if point == 0 then
        point = 13
    elseif point == 1 then
        point = 14
    end
     
    return point
end

-- 获取扑克牌点数,1的点数为1
function GameLogic:getCardPointByNormal(card,flag)
    if card == nil then
        return -1
    end

    local point = card % 13
    if point == 0 then
        point = 13
    elseif point == 1 then
        if flag == 1 then
            point = 14
        else 
            point = 1
        end
        
    end
     
    return point
end

-- 获取扑克花色
function GameLogic:GetCardHuaKind(card)
    if card == nil or card == 54 or card == 53 then
        return -1
    end

    return (math.ceil(card / 13) - 1)
end

-- 获取一张牌数量
function GameLogic:getCardNum(cardvalue,handcards) 
    local num = 0
    for i,v in pairs (handcards) do
        if self:getCardPoint(v) == self:getCardPoint(cardvalue) then
            num = num + 1
        end
    end

    return num
end

-- 获取此张牌同花色数量
function GameLogic:getHuaKindNumByCard(cardvalue,handcards) 
    local num = 0
    for i,v in pairs (handcards) do
        if self:GetCardHuaKind(v) == self:GetCardHuaKind(cardvalue) then
            num = num + 1
        end
    end

    return num
end

-- 获取一种花色数量
function GameLogic:getHuaKindNumByHK(huaKind,handcards) 
    local num = 0
    for i,v in pairs (handcards) do
        if self:GetCardHuaKind(v) == huaKind then
            num = num + 1
        end
    end
    return num
end

-- 获取一种花色的所有牌
function GameLogic:getCardsByHuaKind(huaKind, handcards)
    local cards = {}
    for i,v in pairs (handcards) do
        if self:GetCardHuaKind(v) == huaKind then
            table.insert(cards, v)
        end
    end
    return cards
end

-- 判断此牌点数是否在手牌中
function GameLogic:isInTable(cardPoint,handcards)
    if cardPoint < 1 or cardPoint > 14 then
        return false
    end

    for i=1,#handcards do
        if cardPoint == self:getCardPoint(handcards[i]) then
            return true
        end
        if self:getCardPoint(handcards[i]) == 14 and (cardPoint == 14 or cardPoint == 1) then
            return true
        end
    end

    return false
end

-- 判断此牌是否在手牌中
function GameLogic:isCardInTable(cardVal,handcards)
    for i=1,#handcards do
        if cardVal == handcards[i] then
            return true
        end
    end
    return false
end

-- 删除tab中点数为cardPoint的一张牌
function  GameLogic:removeOneCardByCardPoint(tab,cardPoint)
    for k,v in pairs(tab) do
        if cardPoint == self:getCardPoint(v) then
            table.remove(tab,k)
            break
        end
        if self:getCardPoint(v) == 14 and (cardPoint == 14 or cardPoint == 1) then
            table.remove(tab,k)
            break
        end
    end
end

--获取在tab中，从Cardpoint开始的顺子需要几张王才能组成
function GameLogic:GetShunziNeewangNums(Cardpoint, tab, bThreeCard)
    local needwangnums = 0
    if not self:isInTable(Cardpoint, tab) then
        needwangnums = needwangnums + 1
    end
    if not self:isInTable(Cardpoint + 1, tab) then
        needwangnums = needwangnums + 1
    end
    if not self:isInTable(Cardpoint + 2, tab) then
        needwangnums = needwangnums + 1
    end
    if bThreeCard then
        return needwangnums
    end
    if not self:isInTable(Cardpoint + 3, tab) then
        needwangnums = needwangnums + 1
    end
    if not self:isInTable(Cardpoint + 4, tab) then
        needwangnums = needwangnums + 1
    end
    return needwangnums
end


----获取在tab中，从CardPoint开始的顺子是否是同花顺
function GameLogic:IsShunziTonghua(CardPoint, tab, wangNums)
    for i = 0,3 do
       local cards = self:getCardsByHuaKind(i, tab)
       local tempNums = self:GetShunziNeewangNums(CardPoint, cards)
       if tempNums == wangNums then
           return true
       end
    end
    return false
end

function GameLogic:HasShunziInTab(tab, wangNums)
    if (#tab + wangNums) < 5  then
        return false
    end
    for i = 1,10 do
        local NeedWangNums = 0
        for j = 0,4 do 
            if not self:isInTable(i+j, tab) then
               NeedWangNums = NeedWangNums + 1
            end
        end
        if NeedWangNums <= wangNums then 
            return true
        end
    end
    return false
    
end

-- 得到元素item在tbl中的下标
function GameLogic:getIndexByTable(item,tbl)
    local index = 0
    for i,v in pairs(tbl) do
        if item == v then
            index = i
            break
        end
    end
    return index
end

-- 判断元素item是否在tbl中
function GameLogic:isContains(item,tbl)
    for i,v in pairs(tbl) do
        if item == v then
            return true, i
        end
    end
    return false
end

-- 往tab1中添加（flag = true）或删除（flag = false）tab2
function GameLogic:changeTab(tab1,tab2,flag)
    if tab1 == nil or tab2 == nil then
        return
    end

    if flag == true then
        for k,v in pairs(tab2) do
            table.insert(tab1,v)
        end
    else 
        for k,v in pairs(tab2) do
            for m,n in pairs(tab1) do
                if v == n then
                    table.remove(tab1,m)
                    break
                end
            end
        end

    end

end

-- 比较头道和中道大小
function GameLogic:compare_tou_zhong(touData, zhongData, daoCardType)
    local flag = false
    local bCompareHk = false
    if daoCardType[1] > daoCardType[2] then
        flag = true
        --print("toutype=" .. daoCardType[1] .. "   zhongtype=" .. daoCardType[2])
        if daoCardType[1] == CardType.CT_TIEZHI and daoCardType[2] >=  CardType.CT_SHUNZA  then
            flag = false
        end
    elseif daoCardType[1] < daoCardType[2] then
        flag = false
    elseif daoCardType[1] == daoCardType[2] then
        if daoCardType[1] == CardType.CT_WL then
            for i = 1, 3 do
                if self:getCardPoint(touData[i]) > self:getCardPoint(zhongData[i]) then
                    flag = true
                    break
                elseif self:getCardPoint(touData[i]) < self:getCardPoint(zhongData[i]) then
                    break
                end
                if i==3 then
                    bCompareHk = true
                end
            end
        elseif daoCardType[1] == CardType.CT_PAIRS then
            if self:getCardPoint(touData[1]) > self:getCardPoint(zhongData[1]) then
                flag = true
            elseif self:getCardPoint(touData[1]) == self:getCardPoint(zhongData[1]) then
                if self:getCardPoint(touData[3]) > self:getCardPoint(zhongData[3]) then
                    flag = true
                elseif self:getCardPoint(touData[3]) == self:getCardPoint(zhongData[3]) then
                     bCompareHk = true
                end
            end
        elseif daoCardType[1] == CardType.CT_THREE then
            if self:getCardPoint(touData[1]) > self:getCardPoint(zhongData[1]) then
                flag = true
            elseif self:getCardPoint(touData[1]) == self:getCardPoint(zhongData[1]) then
                 bCompareHk = true
            end
        end
    end
    if bCompareHk  then
        for i = 1, 3 do
            if self:GetCardHuaKind(touData[i])>self:GetCardHuaKind(zhongData[i]) then
                flag = true
                break
            end
        end
    end

    return flag
end

-- 比较中道和尾道大小
function GameLogic:compare_zhong_wei(zhongData, weiData, daoCardType)
    local flag = false
    local bCompareHk = false
    if daoCardType[2] > daoCardType[3] then
        flag = true
    elseif daoCardType[2] < daoCardType[3] then
        flag = false
    elseif daoCardType[2] == daoCardType[3] then
        if daoCardType[2] == CardType.CT_WL  then
            for i = 1, 5 do
                if self:getCardPoint(zhongData[i]) > self:getCardPoint(weiData[i]) then
                    flag = true
                    break
                elseif self:getCardPoint(zhongData[i]) < self:getCardPoint(weiData[i]) then
                    break
                end
                if i==5 then
                    bCompareHk = true
                end
            end
        elseif daoCardType[2] == CardType.CT_TONGHUA then
            local zhongCardParase = self:ParaseCards(zhongData)
            local weiCardParase = self:ParaseCards(weiData)
            local zhongCmpTab = {}
            local weiCmpTab = {}
            for i = 14,1,-1 do
                if #zhongCardParase.PointNumTab[i]>0 then
                    table.insert(zhongCmpTab, {point = i, cardNum = #zhongCardParase.PointNumTab[i]})
                end
                if #weiCardParase.PointNumTab[i]>0 then
                    table.insert(weiCmpTab, {point = i, cardNum = #weiCardParase.PointNumTab[i]})
                end
            end
            if #zhongCmpTab ~= #weiCmpTab then
                if #zhongCmpTab < #weiCmpTab then
                    flag = true
                end
            else
                function sortFunc(a, b)
                    if a.cardNum>b.cardNum or (a.cardNum==b.cardNum and a.point>b.point) then
                        return true
                    else
                        return false
                    end
                end
                table.sort(zhongCmpTab, sortFunc)
                zhongCmpTab[1].cardNum = zhongCmpTab[1].cardNum + #zhongCardParase.LaiziTab
                table.sort(weiCmpTab, sortFunc)
                weiCmpTab[1].cardNum = weiCmpTab[1].cardNum + #weiCardParase.LaiziTab
                local loopTime = #zhongCmpTab
                for i =1, loopTime do
                    if zhongCmpTab[i].cardNum > weiCmpTab[i].cardNum 
                    or (zhongCmpTab[i].cardNum == weiCmpTab[i].cardNum and zhongCmpTab[i].point > weiCmpTab[i].point) then
                        flag = true
                        break
                    elseif zhongCmpTab[i].cardNum < weiCmpTab[i].cardNum 
                    or (zhongCmpTab[i].cardNum == weiCmpTab[i].cardNum and zhongCmpTab[i].point < weiCmpTab[i].point) then
                        break
                    end
                end
                
            end            
        elseif daoCardType[2] == CardType.CT_PAIRS then
            if self:getCardPoint(zhongData[1]) > self:getCardPoint(weiData[1]) then
                flag = true
            elseif self:getCardPoint(zhongData[1]) == self:getCardPoint(weiData[1]) then
                for i = 3, 5 do
                    if self:getCardPoint(zhongData[i]) > self:getCardPoint(weiData[i]) then
                        flag = true
                        break
                    elseif self:getCardPoint(zhongData[i]) < self:getCardPoint(weiData[i]) then
                        break
                    end
                    if i==5 then
                        bCompareHk = true
                    end
                end
            end
        elseif daoCardType[2] == CardType.CT_TWO_PAIRS then
            if self:getCardPoint(zhongData[1]) > self:getCardPoint(weiData[1]) then
                flag = true
            elseif self:getCardPoint(zhongData[1]) == self:getCardPoint(weiData[1]) then
                if self:getCardPoint(zhongData[3]) > self:getCardPoint(weiData[3]) then
                    flag = true
                elseif self:getCardPoint(zhongData[3]) == self:getCardPoint(weiData[3]) then
                    if self:getCardPoint(zhongData[5]) > self:getCardPoint(weiData[5]) then
                        flag = true
                    elseif self:getCardPoint(zhongData[5]) == self:getCardPoint(weiData[5]) then 
                        if self:GetCardHuaKind(zhongData[1])>self:GetCardHuaKind(weiData[1]) then
                            flag = true
                        end
                    end
                end
            end
        elseif daoCardType[2] == CardType.CT_THREE or daoCardType[2] == CardType.CT_GOURD or daoCardType[2] == CardType.CT_TIEZHI then
            local cmpIndexTab = {}

            if  daoCardType[2] == CardType.CT_THREE then
                cmpIndexTab = {1,4,5}
            end
             if  daoCardType[2] == CardType.CT_GOURD then
                cmpIndexTab = {1,4}
            end
            if  daoCardType[2] == CardType.CT_TIEZHI then
                cmpIndexTab = {1,3}
            end
            bCompareHk = true
            for _,index in pairs(cmpIndexTab) do
                local zhongpoint = self:getCardPoint(zhongData[index])
                local weipoint = self:getCardPoint(weiData[index])
                 if zhongpoint > weipoint then
                    flag = true
                    bCompareHk = false
                    break
                elseif zhongpoint < weipoint then
                    bCompareHk = false
                    break
                end
            end
      
        elseif daoCardType[2] == CardType.CT_SHUNZA or daoCardType[2] == CardType.CT_TONGHUASZ then
            local zStartpoint = 0
            local wStartpoint = 0
            for key,val in pairs(zhongData) do
                if not self:IsLaizi(val) then
                    zStartpoint = self:getCardPoint(val) + key -1
                    break
                end
            end
            for key,val in pairs(weiData) do
                if not self:IsLaizi(val) then
                    wStartpoint = self:getCardPoint(val) + key -1
                    break
                end
            end      

            ---A2345为仅小于10JQKA的顺子的特殊处理
            if zStartpoint == 6 and self:IsLaizi(zhongData[1]) then
                zStartpoint = 5
            end 
            if wStartpoint == 6 and self:IsLaizi(weiData[1]) then
                wStartpoint = 5
            end 
            if zStartpoint == 5 and bA_5BigShunzi == true  then
                if wStartpoint ~= 5 and wStartpoint ~= 14 then
                    flag = true
                end
            elseif wStartpoint == 5 and bA_5BigShunzi == true then
                if zStartpoint ~= 5 and zStartpoint ~= 14 then
                    flag = false
                else
                    flag = true
                end
            ---A2345为仅小于10JQKA的顺子的特殊处理
            else 
                if zStartpoint > wStartpoint then
                    flag = true
                end
            end
            if zStartpoint == wStartpoint then
                bCompareHk = true
            end
        end
    end
    if bCompareHk  then
        for i = 1, 5 do
            if self:GetCardHuaKind(zhongData[i])>self:GetCardHuaKind(weiData[i]) then
                local zhongVal = self:GetCardHuaKind(zhongData[i])
                local weiVal = self:GetCardHuaKind(weiData[i])
                if zhongVal ~= -1 and weiVal ~= -1 then
                    flag = true
                    break
                end
            end
        end
    end

    return flag
end

--将王替换成牌型中最大的点数，同花牌型使用
function GameLogic:ReplaceWangwithNomalCard(tab)
    local wangcount = 0
    for k,v in pairs(tab) do
        if v == 15 or v == 16 then
           wangcount = wangcount + 1 
        end
    end 
    if wangcount > 0 then
        if tab[2] == 15 or tab[2] == 16 then
            table.remove(tab, 2)
        end
        if tab[1] == 15 or tab[1] == 16 then
            table.remove(tab, 1)
        end
        local val = 14
        local tempTab = clone(tab)
        for i = 1, 5 do
            if tempTab[i] ~= nil and tempTab[i] == val then
                val = val - 1
            else
                val = val - 1
                table.insert(tab, val)
                wangcount = wangcount - 1
                if wangcount <= 0 then
                    break
                end
            end
        end
        table.sort(tab, function (a,b)
            return a > b 
            end)
    end
end

function GameLogic:CombineCard(srcCardsTab ,dstCardsTab, deleteindex, selectNums)
    if #srcCardsTab == selectNums then
        local selectCards = {}
        selectCards = clone(srcCardsTab)
        return table.insert(dstCardsTab ,selectCards)
    elseif #srcCardsTab>selectNums then
        for i = deleteindex, #srcCardsTab do
            local deleteOneCardsTab = clone(srcCardsTab)
            table.remove(deleteOneCardsTab, i)
            self:CombineCard(deleteOneCardsTab, dstCardsTab, i, selectNums)
        end
    else
        return 
    end
end

function GameLogic:IsLaizi(card, notComtainTHSP)
    local ThsLaizi = ViewHelp.GetTHSPLaizis()
    if notComtainTHSP then
        ThsLaizi = {}
    end
    if ThsLaizi and #ThsLaizi > 0 then
        for _,v in pairs(ThsLaizi) do 
            if card == v then
                return true
            end
        end
    end

    local guiCards = ViewHelp.GetGuiCards()
    if guiCards and #guiCards > 0 then
        for _,v in pairs(guiCards) do 
            if card == v then
                return true
            end
        end
    end

    if card == 53 or card == 54 then
        return true
    end

    return false
end

function GameLogic:ParaseCards(cards, notComtainTHSP)
    local paraseStruct = {}
    paraseStruct.LaiziTab = {}
    paraseStruct.PointNumTab = {{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{}}
    paraseStruct.HuaKindTab = {{},{},{},{}}
    for _,cardVal in pairs(cards) do
        --癞子 
        if self:IsLaizi(cardVal, notComtainTHSP) then
            table.insert(paraseStruct.LaiziTab, cardVal)
        else
            local cardPoint = self:getCardPoint(cardVal)
            local huaKind = self:GetCardHuaKind(cardVal)
            table.insert(paraseStruct.PointNumTab[cardPoint], cardVal)
            table.insert(paraseStruct.HuaKindTab[huaKind+1], cardVal)
        end
    end
    return paraseStruct
end
function GameLogic:RemoveCards(srcCards,rmCards)
    local retCards = clone(srcCards)
    for k,v in pairs(rmCards) do
        for i = 1, #retCards do 
            if v==retCards[i] then
                table.remove(retCards, i)
            end
        end
    end
    return retCards
end
return GameLogic

--endregion
