--region *.lua
--Date
--此文件由[BabeLua]插件自动生成

module("ViewHelp", package.seeall)

local CGameManger = import("app.platform.game.gamemanager")

local PlayerInfoLayer = import("app.platform.game.".. GAME_ID ..".view.PlayerInfoLayer")

--当前的玩家信息层
local m_PlayerInfoLayer = nil
local Base_Pos = 
{
    userID = tonumber(CGameManger:get_instance():get_client_main():get_user_mgr():get_user_info().UserID),
    chair = -1,
    l_chair = -1
}

local AllServerChair = {0,0,0,0}

local table_voice_not = {}

local NTChair = 0
local myGameStation = -1

local mapaiNums = 0 

function initBasePosChair()
    Base_Pos.chair = -1
    Base_Pos.l_chair = -1
end

function initVoiceNotTable()
    table_voice_not = {}
end



function insertVoiceNotTable(userID)
    removeVoiceNotTable(userID)
    table.insert(table_voice_not, #table_voice_not+1)
end

function removeVoiceNotTable(userID)
   for i=1, #table_voice_not do
        if table_voice_not[i]==userID then
           table.remove(table_voice_not, i)
           break
        end
   end
end

function isElementOfVoiceNotTable(userID)
   for i=1, #table_voice_not do
        if table_voice_not[i]==userID then
            return true
        end
   end
   return false
end

--保存当前玩家的服务器椅子ID
function setBasePosChair(chair)
    if chair == nil then
        return
    end
    Base_Pos.l_chair = Base_Pos.chair
    Base_Pos.chair = chair

    
end


--根据服务器的椅子id  获取UI上的椅子位置
function getUIChairIndexByServerChair(server_chair)
    local ui_chair = 0

    if server_chair < 0 or server_chair > PLAYER_COUNT - 1 then
        return 255
    else
        local playerNum = getPlayerNums()
        --if playerNum >= 4 then
            ui_chair = (PLAYER_COUNT + tonumber(server_chair) - Base_Pos.chair)%PLAYER_COUNT+1
            if getSelfInfo().look == true  then
                 ui_chair = (PLAYER_COUNT + tonumber(server_chair) - 0)%PLAYER_COUNT+1
            end
--        elseif playerNum == 3 then
--            if server_chair == Base_Pos.chair then
--                ui_chair = 1
--            elseif isBehind(server_chair,Base_Pos.chair) then
--                ui_chair = 2
--            else
--                ui_chair = 4
--            end
--        elseif playerNum == 2 then
--            if server_chair == Base_Pos.chair then
--                ui_chair = 1
--            else
--                ui_chair = 3
--            end
--        end
    end

    return ui_chair
end

--UI椅子位置 获取对应的serverchair
function getServerChairByUIChair(uiChair)
    local chair = -1
    for i = 0,PLAYER_COUNT-1  do
        if uiChair == getUIChairIndexByServerChair(i) then
            chair = i
            break
        end
    end
    return chair
end

-- 设置所有玩家服务端椅子号
function setAllServerChair(allServerChair)
    if not GameSceneModule:getInstance():getGameScene():IsGoldRoom() then
        AllServerChair = allServerChair
    end
end

function getAllServerChair()
     return AllServerChair
end

-- 得到该服务器椅子号的下标索引
function getIndexByServerChair(server_chair)
    local index = 1
    for k,v in pairs(AllServerChair) do
        if server_chair == v then
            index = k
            break
        end
    end

    return index
end

-- 得到玩家个数
function getPlayerNums()
    local nums = 0
    for k,v in pairs(AllServerChair) do
        nums = nums + 1
    end

    return nums
end

-- 表AllServerChair中 server_chair 是否是self_chair 后面一个
function  isBehind(server_chair,self_chair)
    local flag = false
    for i=1,getPlayerNums() do
        local lastIndex = i + 1
        if lastIndex > getPlayerNums() then
            lastIndex = 1
        end
        if AllServerChair[i] == self_chair and AllServerChair[lastIndex] == server_chair then
            flag = true
            break
        end
    end
    
    return flag
end

function getClientChairID(server_chair)
    return server_chair
end

function getBasePosChair()
    if getSelfInfo().look == true  then
        return 0
    end
    return Base_Pos.chair
end


function getBasePosUserID()
    return Base_Pos.userID
end

function getSelfPlayer()
    return GameSceneModule:getInstance():getGameScene():getSelfInfo()
end

function getRoomManager()
    return CGameManger:get_instance():get_game_room_mgr()

end

--获取当前桌子配置
function getDeskConfig()
    return GameSceneModule:getInstance():getGameScene():getDeskInfo()
end

 
--获取庄家的椅子号
function getNTServerChair()
    return NTChair
end

--设置庄家的椅子号
function setNTServerChair( chair )
    NTChair = chair
end

--获取当前游戏状态
function getGameStation()
    return myGameStation
end

--设置当前游戏状态
function setGameStation(station)
    myGameStation = station
end

--获取自己的信息
function getSelfInfo()
    return GameSceneModule:getInstance():getGameScene():getSelfInfo()
end

--获取自己的玩家ID
function getSelfUserID()
    return GameSceneModule:getInstance():getGameScene():getSelfInfo().dwUserID
end

--获取当前桌子上的玩家缓存数据(总结算时被踢出房间需要缓存)
function getDeskPlayerCacheData()
    return GameSceneModule:getInstance():getGameScene().PlayersInfos
end

--获取桌子配置
function getDeskInfo()
    return GameSceneModule:getInstance():getGameScene():getDeskInfo()
end

--获取房主的UserUD
function getRoomMasterUserID()
    if GameSceneModule:getInstance():getGameScene():getGameType() == "tz1" then
        return 0
    end
    return GameSceneModule:getInstance():getGameScene():getDeskInfo().mastUserID
end

--显示玩家的信息层
function showPlayerInfoLayer(ui_root,userInfo)
    if m_PlayerInfoLayer then
        hidePlayerInfoLayer()
    end

    if userInfo == nil then
        return
    end
    m_PlayerInfoLayer = PlayerInfoLayer:create(userInfo)
    ui_root:addChild(m_PlayerInfoLayer,1)

end  

--隐藏玩家的信息层
function hidePlayerInfoLayer()

    if m_PlayerInfoLayer then
        m_PlayerInfoLayer:removeFromParent()
        m_PlayerInfoLayer = nil
    end
end

--马牌
local MapaiTab = {39,39,39}
function SetMapaiNums(num)
    mapaiNums = num
end

function SetMaPaiVal(maPaiList)
   dump(maPaiList)
    MapaiTab = maPaiList 
end

function GetMapaiTab()
    local mapaiTab = {}
    for i = 1, mapaiNums do 
        mapaiTab[i] = MapaiTab[i]
    end 
    return mapaiTab
end

function IsMapai(val)
    local mapaiTab = GetMapaiTab()
    for _,v in pairs(mapaiTab) do
        if v == val then
            return true
        end
    end
    return false
end

--癞子
local guiCards= {}
function SetGuiCards(cards)
    if cards then
        guiCards = clone(cards)
    else
        guiCards = {}
    end
end

function GetGuiCards()
    local retTab = clone(guiCards)
    return retTab
end

function IsGuiCards(val)
    if guiCards and #guiCards>0 then
        for _,v in pairs(guiCards) do
            if v == val then
                return true
            end
        end
    end
    return false
end

--同花顺+癞子
local bTHSP = false
local THSPLaizis = {}
function SetbTHSP(bsele)
    bTHSP = bsele
end


function SetTHSPLaizis(cards)
    if not bTHSP then
        return 
    end
    if cards then
        THSPLaizis = clone(cards)
    else
        THSPLaizis = {}
    end
end

function GetTHSPLaizis()
    local retTab = clone(THSPLaizis)
    return retTab
end

function IsTHSLaizi(value)
    if THSPLaizis and #THSPLaizis>0 then
        for _,v in pairs(THSPLaizis) do
            if v == value then
                return true
            end
        end
    end
    return false
end




--endregion
