--region *.lua
--Date
--游戏中每个玩家头像信息UI管理类
--此文件由[BabeLua]插件自动生成

--import("..GameDefine")

local GameMusic = import("..GameMusic")
local m_download = import("....common.download")

local PlayersHeadUIManager = class("PlayersHeadUIManager")

local qzType = {
    qz = 0,  -- 抢庄
    bq = 1,  -- 不抢
}

function PlayersHeadUIManager:ctor(ui_project, ui_root)

    self.ui_project = ui_project
    self.ui_root = ui_root
    self.curCountDountTime = 15
    -- 倒计时还有多少时间
    self.curCountDownIndex = nil
    -- 当前倒计时索引

    self.isGameStation = false
    -- 判断是否是断线重连

    self.PlayerHeads = { }
    self.HeadsOriginalPos = {}
    for i = 1, PLAYER_COUNT do
        self.PlayerHeads[i] = self.ui_project:getChildByName("Player_" .. i)
        self.PlayerHeads[i]:setVisible(false)
        self.HeadsOriginalPos[i] = cc.p(self.PlayerHeads[i]:getPosition())
        local Panel_player = self.PlayerHeads[i]:getChildByName("Panel_player")
        self.PlayerHeads[i].Image_ready = Panel_player:getChildByName("Image_ready")
        self.PlayerHeads[i].Image_ready:setVisible(false)

        self.PlayerHeads[i].Image_NT = Panel_player:getChildByName("Image_NT")
        self.PlayerHeads[i].Image_NT:setVisible(false)
        if i == 2 or i == 3 then
            local xPos = self.PlayerHeads[i].Image_ready:getPositionX() - 250
            self.PlayerHeads[i].Image_ready:setPositionX(xPos)
        end
        self.PlayerHeads[i].Image_head_wx = Panel_player:getChildByName("Image_head_wx")
        local Image_Bg = Panel_player:getChildByName("Image_Bg")
        Image_Bg:addClickEventListener( function()
            local PlayerInfos = GameSceneModule:getInstance():getGameScene().PlayersInfos
            for k, v in pairs(PlayerInfos) do
                local server_chair = v.bDeskStation
                local ui_chair = ViewHelp.getUIChairIndexByServerChair(server_chair)
                if ui_chair == i then
                    ViewHelp.showPlayerInfoLayer(self.ui_root, v)
                    break
                end
            end
        end )

        if i==5 or  i==7 then
            self.PlayerHeads[i]:setPositionX(self.PlayerHeads[i]:getPositionX() - self.ui_project:getPositionX()/1.8)
        elseif i==3 or i==6 then
            self.PlayerHeads[i]:setPositionX(self.PlayerHeads[i]:getPositionX() + self.ui_project:getPositionX()/1.8)
        end
--        local listener = cc.EventListenerTouchOneByOne:create()
--        listener:setSwallowTouches(false)
--        listener:registerScriptHandler( onTouchBegan, cc.Handler.EVENT_TOUCH_BEGAN)
--        local eventDispatcher = cc.Director:getInstance():getEventDispatcher()
--        eventDispatcher:addEventListenerWithSceneGraphPriority(listener, self.PlayerHeads[i].Image_head_wx)
       
        --self.PlayerHeads[i].Image_head_wx:setFlippedY(true)
        --self.PlayerHeads[i].Image_head_wx:setVisible(false)

--        local clipOuter = cc.ClippingNode:create()
--        local stencil = cc.Sprite:create("res/game_res/" .. GAME_ID .. "/ui_res/players/default_boy.png")
--        clipOuter:setStencil(stencil)
--        stencil:setAnchorPoint(cc.p(0,0))
--        clipOuter:setAlphaThreshold(0.05)
--        clipOuter:setAnchorPoint(cc.p(0,0)) 
--        clipOuter:setInverted(false)
--        local downloadImage = ccui.ImageView:create("res/game_res/" .. GAME_ID .. "/ui_res/players/default_boy.png")
--        downloadImage:ignoreContentAdaptWithSize(false)
--        downloadImage:setAnchorPoint(cc.p(0,0))
--        clipOuter:addChild(downloadImage)

--        self.PlayerHeads[i].Image_head_wx:addChild(clipOuter)
--        self.PlayerHeads[i].Image_head_wx.clipOuter = clipOuter
--        self.PlayerHeads[i].Image_head_wx.clipOuter.downloadImage = downloadImage

        self.PlayerHeads[i].Text_name = Panel_player:getChildByName("Text_name")
        self.PlayerHeads[i].Text_score = Panel_player:getChildByName("Text_score")
        self.PlayerHeads[i].Text_userid = Panel_player:getChildByName("Text_userid")
        self.PlayerHeads[i].Text_score:setString(tostring(0))


        self.PlayerHeads[i].Image_offline = Panel_player:getChildByName("Image_offline")
        self.PlayerHeads[i].Image_offline:setVisible(false)
--        if i ~= 1 then
--            self.PlayerHeads[i].Panel_hand_card = self.PlayerHeads[i]:getChildByName("Panel_hand_card")
--            self.PlayerHeads[i].Panel_hand_card:setVisible(false)
--        end
    end
    self:onCardColorChange()
    -- 显示已经进来的玩家
    self:showSitUsers()
end

function PlayersHeadUIManager:onCardColorChange()
    cclog("PlayersHeadUIManager:onCardColorChange")
    local CardNameTab = {"card_R.png","card_G.png","card_B.png"}
    local path = "res/game_res/"..GAME_ID.."/ui_res/baipai/card/"..CardNameTab[UserData:getCardColor()]
    for i = 2, PLAYER_COUNT do
        for j = 1,13 do
--            local card = self.PlayerHeads[i].Panel_hand_card:getChildByName("Image_"..j)
--            card:loadTexture(path)
        end
    end
end

function PlayersHeadUIManager:showHandCard(ui_chair, isShow)
--    if ui_chair <=1 or ui_chair > 4 then
--        return 
--    end
--    self.PlayerHeads[ui_chair].Panel_hand_card:setVisible(isShow)
end

function PlayersHeadUIManager:onBaipaiResult(resp_json)
    local ui_chair = ViewHelp.getUIChairIndexByServerChair(resp_json.chair)
    self:SetHeadBipaiPos(ui_chair)
    if ui_chair ~= 1 then
       self:showHandCard(ui_chair,false)
    end

    if resp_json.NTUser ~= nil and resp_json.NTUser ~= 255 then
        local ui_chair = ViewHelp.getUIChairIndexByServerChair(resp_json.NTUser)
        if self.PlayerHeads[ui_chair] ~= nil then
            self.PlayerHeads[ui_chair].Image_NT:setVisible(true)
        end
    end
end
-- 游戏开始
function PlayersHeadUIManager:onGameStart(resp_json)
    for i = 1, PLAYER_COUNT do
        print("PlayersHeadUIManager:onGameStart hide imageready")
        self:ShowReadImage(i, false)
        self.PlayerHeads[i].Image_NT:setVisible(false)
    end
    self:updatePlayerInfo(255)
    self:updateAllPlayerScore()

    local PlayersInfos = GameSceneModule:getInstance():getGameScene():getSitDownPlayerMap()
    for _,player_info in pairs(PlayersInfos) do
        local ui_chair = ViewHelp.getUIChairIndexByServerChair(player_info.bDeskStation)
        self:showHandCard(ui_chair,true)
    end

    if resp_json.UserPoints  then
         for _,playerinfo in pairs(PlayersInfos) do
            local uiChair = ViewHelp.getUIChairIndexByServerChair(playerinfo.bDeskStation)
            self.PlayerHeads[uiChair].Text_score:setText(self:scoreToStr(resp_json.UserPoints[playerinfo.bDeskStation+1]))
        end
    end
end

-- 抢庄结果
function  PlayersHeadUIManager:OnRobBanker(resp_json)
    print("--- PlayersHeadUIManager:OnRobBanker")
    if resp_json == nil then
        return
    end
    local ui_chair_index = ViewHelp.getUIChairIndexByServerChair(resp_json.chair)
    if resp_json.qzType == qzType.qz then
        self.PlayerHeads[ui_chair_index].Image_qz:loadTexture("res/game_res/" .. GAME_ID .. "/ui_res/players/qz.png")
    elseif  resp_json.qzType == qzType.bq then
        self.PlayerHeads[ui_chair_index].Image_qz:loadTexture("res/game_res/" .. GAME_ID .. "/ui_res/players/bq.png")
    end
    self.PlayerHeads[ui_chair_index].Image_qz:setVisible(true)

end

--[[
设置某一玩家的昵称和分数

@param chair_index 椅子位置
@param nickname 玩家昵称
@param score 玩家分数
@param isNT 是否是庄家
]]
function PlayersHeadUIManager:updatePlayerNameAndScore(chair_index,nickname,score,isNT)
    self.PlayerHeads[tonumber(chair_index)].Text_name:setString(api_get_ascll_sub_str_by_ui(nickname,8))
    
    --self.PlayerHeads[tonumber(chair_index)].Text_score:setText(self:scoreToStr(score))
    --self.PlayerHeads[tonumber(chair_index)].Image_NT:setVisible(isNT)
end

function PlayersHeadUIManager:scoreToStr(score)
    local moneyStr = ""
    --[[
    if score >=10000 or score <=-10000 then
        if score % 10000 ~= 0 and score/100 ~= 0 then
            moneyStr = string.format("%0.2f",score/10000).."w"
        else
            moneyStr = string.format("%0.0f",score/10000).."w"
        end
            
    else
        moneyStr = tostring(score)
    end
    --]]
    local gameSceneTmp =  GameSceneModule:getInstance():getGameScene()
    moneyStr = gameSceneTmp:getMoneyText(score)

    return moneyStr
end

--更新某个玩家分数
function PlayersHeadUIManager:updatePlayersScore(chair_index , score)

    self.PlayerHeads[tonumber(chair_index)].Text_score:setText(self:scoreToStr(score))
end

-- 抓牌
function PlayersHeadUIManager:onCatchCard(resp_json)
end


--更新所有玩家的头像  nt_chair 庄家的id
function PlayersHeadUIManager:updatePlayerInfo(nt_chair)
    print("update Player Info nt_chair = " .. nt_chair)

    for i = 1, PLAYER_COUNT do
        self:setHeadAtIndexVisible(i,false)
    end
    local PlayersInfos = GameSceneModule:getInstance():getGameScene():getSitDownPlayerMap()
    for _,player_info in pairs(PlayersInfos) do
        local ui_chair_index = ViewHelp.getUIChairIndexByServerChair(player_info.bDeskStation)
        self:setHeadAtIndexVisible(ui_chair_index,true)
        --self.PlayerHeads[ui_chair_index].Image_ready:setVisible(false)

        local isNt = false
        if nt_chair == player_info.bDeskStation then
            isNt = true
        end

        if player_info ~= nil then

            if player_info.status == 20 then
                self.PlayerHeads[ui_chair_index].Image_offline:setVisible(true)
            else
                self.PlayerHeads[ui_chair_index].Image_offline:setVisible(false)
            end
            -- 更新头像
            local downloadSprite = cc.Sprite:create()
            m_download:get_instance():set_head_image_and_auto_update(self.PlayerHeads[ui_chair_index].Image_head_wx ,player_info.avatarUrl ,  player_info.dwUserID, nil, player_info.HeadID )
            --m_download:get_instance():set_head_image_and_auto_update(self.PlayerHeads[ui_chair_index].Image_head_wx.clipOuter.downloadImage  ,player_info.avatarUrl , tostring(player_info.dwUserID) )
--            local maskedTexure = self:maskedSprite(downloadSprite, "res/game_res/" .. GAME_ID .. "/ui_res/players/default_boy.png")
--            if maskedTexure then
--                local maskedSpriteFrame = cc.SpriteFrame:createWithTexture(maskedTexure, cc.rect(0, 0, maskedTexure:getContentSize().width, maskedTexure:getContentSize().height))
--                self.PlayerHeads[ui_chair_index].Image_head_wx:setSpriteFrame(maskedSpriteFrame)
--            end

            self.PlayerHeads[ui_chair_index].Text_userid:setString("ID:" .. tostring(player_info.dwUserID))
            self:updatePlayerNameAndScore(ui_chair_index, player_info.nickName, player_info.dwMoney, isNt)
        end
    end


end

-- 更新所有玩家分数
function PlayersHeadUIManager:updateAllPlayerScore()
    local PlayersInfos = GameSceneModule:getInstance():getGameScene().PlayersInfos
    local curPlayerMap = GameSceneModule:getInstance():getGameScene():getSitDownPlayerMap()
    for _,player_info in pairs(PlayersInfos) do
        for _,v in pairs(curPlayerMap) do 
            if v.dwUserID == player_info.dwUserID and  v.bDeskStation == player_info.bDeskStation then
                local ui_chair_index = ViewHelp.getUIChairIndexByServerChair(player_info.bDeskStation)
                --self.PlayerHeads[ui_chair_index].Text_score:setText(self:scoreToStr(player_info.dwMoney))
            end
        end
    end
--    local request = { }
--    handleGameMessage.SendGameMessage(handleGameMessage.Table_GameMessage_ASSID.ASS_GM_AGREE_GAME, request)
--    GameSceneModule:getInstance():getGameScene().GameLayer.ResultUIManager:setInit()
end

--继续游戏处理
function PlayersHeadUIManager:onAgreeGame(resp_json)
    print("--- PlayersHeadUIManager:onAgreeGame start")
    local ui_chair_index = ViewHelp.getUIChairIndexByServerChair(resp_json.chair)
    self:ShowReadImage(ui_chair_index,true)
    if ui_chair_index == 1 then
        self:ResetHeadsPos()
    end


    print("--- PlayersHeadUIManager:onAgreeGame end")

end

function PlayersHeadUIManager:userCutover(resp_json)
    if resp_json ~= nil then
        local ui_chair_index = ViewHelp.getUIChairIndexByServerChair(resp_json.chair)
        self.PlayerHeads[ui_chair_index].Image_offline:setVisible(resp_json.isNetCut)
    end
end

function PlayersHeadUIManager:ShowReadImage(ui_chair_index,visiable)
    print("PlayersHeadUIManager:ShowReadImage "..ui_chair_index.."visiable "..tostring(visiable))
    self.PlayerHeads[ui_chair_index].Image_ready:setVisible(visiable)
end

--更新每个玩家的掉线状态
function PlayersHeadUIManager:showUserNetCutState(selfChair)
    
    local sitDownUsers = GameSceneModule:getInstance():getGameScene():getSitDownPlayerMap()
    for key, sitPlayer in pairs(sitDownUsers) do
        local ui_chair_index = ViewHelp.getUIChairIndexByServerChair(sitPlayer.bDeskStation)
        if sitPlayer.status == 20 then
            print("PlayersHeadUIManager 椅子号 ui_chair_index ",ui_chair_index)
            self.PlayerHeads[ui_chair_index].Image_offline:setVisible(true)
        end
    end
end

function PlayersHeadUIManager:onOtherPlayerOffline(user_info)

    if user_info.bDeskStation >= 0  and user_info.bDeskStation < PLAYER_COUNT then
        local ui_chair_index = ViewHelp.getUIChairIndexByServerChair(user_info.bDeskStation)
        self.PlayerHeads[ui_chair_index].Image_offline:setVisible(true)
    end
end

function PlayersHeadUIManager:onPlayerJoinDesk( user_info , desk_info , code )
    --重现连接回来
    print("PlayersHeadUIManager 有玩家加入桌子",code)
    if code == 0 or code == nil then
        if user_info.bDeskStation >= 0  and user_info.bDeskStation < PLAYER_COUNT then
            local ui_chair_index = ViewHelp.getUIChairIndexByServerChair(user_info.bDeskStation)
            print("PlayersHeadUIManager" , ui_chair_index,"号玩家隐藏头像离线标志")
            self.PlayerHeads[ui_chair_index].Image_offline:setVisible(false)
        end
    end
    
end

-- 隐藏或者隐藏第chair_index个已经坐下的玩家信息
function PlayersHeadUIManager:setHeadAtIndexVisible(chair_index, visible)
    if chair_index >= 1 and chair_index <= PLAYER_COUNT then
        self.PlayerHeads[chair_index]:setVisible(visible)
--        self.PlayerHeads[chair_index].Image_head_bg:setVisible(visible)
--        self.PlayerHeads[chair_index].Image_head_wx:setVisible(visible)
        --self.PlayerHeads[chair_index].Image_ready:setVisible(visible)
    end

end

-- 设置玩家头像
function PlayersHeadUIManager:setPlayerHeadImage(userInfo)
    local ui_chair_index = ViewHelp.getUIChairIndexByServerChair(userInfo.bDeskStation)
    self.PlayerHeads[ui_chair_index].Image_head_wx:loadTexture("res/game_res/" .. GAME_ID .. "/ui_res/players/default_boy.png")

    local downloadSprite = cc.Sprite:create()
    m_download:get_instance():set_head_image_and_auto_update(self.PlayerHeads[ui_chair_index].Image_head_wx  ,userInfo.avatarUrl , userInfo.dwUserID, nil, userInfo.HeadID )
    --m_download:get_instance():set_head_image_and_auto_update(self.PlayerHeads[ui_chair_index].Image_head_wx.clipOuter.downloadImage  ,userInfo.avatarUrl , tostring(userInfo.dwUserID) )
--    local maskedTexure = self:maskedSprite(downloadSprite, "res/game_res/" .. GAME_ID .. "/ui_res/players/default_boy.png")
--   if maskedTexure then
--        local maskedSpriteFrame = cc.SpriteFrame:createWithTexture(maskedTexure, cc.rect(0, 0, maskedTexure:getContentSize().width, maskedTexure:getContentSize().height))
--        self.PlayerHeads[ui_chair_index].Image_head_wx:setSpriteFrame(maskedSpriteFrame)
--    end
end

-- 有玩家离开桌子
function PlayersHeadUIManager:onPlayerLeaveDesk(user_info, desk_info, code)

    if code == 0 and user_info ~= nil and user_info.bDeskStation >= 0 then
        print("成功离开桌子")
        local ui_chair_index = ViewHelp.getUIChairIndexByServerChair(user_info.bDeskStation)
        self:setHeadAtIndexVisible(ui_chair_index, false)
        self.PlayerHeads[ui_chair_index].Image_ready:setVisible(false)
    elseif code == 1 then
        print("正在游戏不准离开桌子")
    elseif code == 2 then
        print("已经离开不需要再次离开")
    end
end

-- 有玩家站起来
function PlayersHeadUIManager:onPlayerStandUp(user_info, desk_info, code)
    print("PlayersHeadUIManager:onPlayerStandUp")
    --if  code == 0 and user_info ~= nil and user_info.bDeskStation >= 0 then
    if user_info ~= nil and user_info.bDeskStation >= 0 then
        local ui_chair_index = ViewHelp.getUIChairIndexByServerChair(user_info.bDeskStation)
        -- 如果自己已经坐下就不用显示坐下按钮
        --local playerInfo = ViewHelp.getRoomManager():get_player_info_by_chairID(ViewHelp.getBasePosChair())
        self:setHeadAtIndexVisible(ui_chair_index, false)
        self.PlayerHeads[ui_chair_index].Image_ready:setVisible(false)
    end
end

function PlayersHeadUIManager:HidePlayerInfo(ui_chair)
    if ui_chair>PLAYER_COUNT or ui_chair <=0 then
        return 
    end
    self:setHeadAtIndexVisible(ui_chair, false)
    self.PlayerHeads[ui_chair].Image_ready:setVisible(false)
end

-- 显示坐下的玩家
function PlayersHeadUIManager:showSitUsers()
    local selfChair = ViewHelp.getBasePosChair()
    if selfChair < 0 then
        return
    end
    for key, sitPlayer in pairs(GameSceneModule:getInstance():getGameScene():getSitDownPlayerMap()) do
        local ui_chair_index = ViewHelp.getUIChairIndexByServerChair(sitPlayer.bDeskStation)
        self:setHeadAtIndexVisible(ui_chair_index, true)
        self.PlayerHeads[ui_chair_index].Text_name:setString(api_get_ascll_sub_str_by_ui(sitPlayer.nickName, 8))
        self.PlayerHeads[ui_chair_index].Text_userid:setString("ID:" .. tostring(sitPlayer.dwUserID))
        self:setPlayerHeadImage(sitPlayer) 
        -- 房主可能离线
        if sitPlayer.dwUserID == ViewHelp.getRoomMasterUserID() then
            if sitPlayer.status == 20 then
                self.PlayerHeads[ui_chair_index].Image_offline:setVisible(true)
            end
        end
    end
end


-- 有玩家坐下
function PlayersHeadUIManager:onPlayerSit(user_info)
    -- 过滤不是自己桌子的玩家
   -- local desk_config = ViewHelp.getDeskConfig()
    --if desk_config.deskIndex == user_info.deskIndex then
        local ui_chair_index = ViewHelp.getUIChairIndexByServerChair(user_info.bDeskStation)
        self:setHeadAtIndexVisible(ui_chair_index, true)
        self.PlayerHeads[ui_chair_index].Text_name:setString(api_get_ascll_sub_str_by_ui(user_info.nickName, 8))
        self.PlayerHeads[ui_chair_index].Text_userid:setString("ID:" .. tostring(user_info.dwUserID))
        --self.PlayerHeads[ui_chair_index].Text_score:setString(tostring(user_info.dwMoney))
        self.PlayerHeads[ui_chair_index].Text_score:setString(tostring(0))
        -- 更新头像
        self:setPlayerHeadImage(user_info)
    --end
end

--断线重连处理
function PlayersHeadUIManager:onGameStation(resp_json)
    print("PlayersHeadUIManager:onGameStation")

    self.isGameStation = true
    self:updatePlayerInfo(255)
    if not GameSceneModule:getInstance():getGameScene():IsGoldRoom() and resp_json.TotalScore then
        local PlayersInfos = GameSceneModule:getInstance():getGameScene():getSitDownPlayerMap()
        for _,playerinfo in pairs(PlayersInfos) do
            local uiChair = ViewHelp.getUIChairIndexByServerChair(playerinfo.bDeskStation)
            self.PlayerHeads[uiChair].Text_score:setText(self:scoreToStr(resp_json.TotalScore[playerinfo.bDeskStation+1]))
        end
    end

    if resp_json.GSID == GameStation.GS_ROBBANKER then
        self:ui_open()
        for i = 1, #resp_json.robBankerResp do
            if resp_json.robBankerResp[i].bRobBanker then
                self:OnRobBanker(resp_json.robBankerResp[i])
            end
        end

        for i = 1, PLAYER_COUNT do
            self:ShowReadImage(i, false)
        end
        

    -- 玩家摆牌状态
    elseif resp_json.GSID == GameStation.GS_PLAYING then
        self:ui_open()

        for i = 1, PLAYER_COUNT do
            self:ShowReadImage(i, false)
        end

        local PlayersInfos = GameSceneModule:getInstance():getGameScene():getSitDownPlayerMap()
        for _,playerinfo in pairs(PlayersInfos) do
            if not resp_json.baiPaiOK[playerinfo.bDeskStation+1] then
                local uiChair = ViewHelp.getUIChairIndexByServerChair(playerinfo.bDeskStation)
                self:showHandCard(uiChair,true)
            end
        end
    elseif resp_json.GSID == GameStation.GS_WAIT_NEXT_ROUND then
        self:showUserNetCutState(resp_json.chair)
        print("PlayersHeadUIManager:onGameStation set imageready state")
        for i = 1, #resp_json.IsAgree do
            local ui_chair_index = ViewHelp.getUIChairIndexByServerChair(i - 1)
            self:ShowReadImage(ui_chair_index, resp_json.IsAgree[i])

            local server_chair = ViewHelp.getServerChairByUIChair(1)
            if not resp_json.IsAgree[server_chair + 1] then
                local request = { }
                handleGameMessage.SendGameMessage(handleGameMessage.Table_GameMessage_ASSID.ASS_GM_AGREE_GAME, request)
            end
        end
        self:ui_open()

    elseif resp_json.GSID == GameStation.GS_WAIT_SETGAME then

        print("PlayersHeadUIManager:onGameStation  GS_WAIT_SETGAME")
        for i = 1, PLAYER_COUNT do
            self:ShowReadImage(i, false)
        end
        local playerInfos = ViewHelp.getDeskPlayerCacheData()
        local sitDownUsersNum = #playerInfos
        -- 显示哪些玩家已经准备
        for k,v in pairs(playerInfos) do
            local server_chair = v.bDeskStation
            local ui_chair = ViewHelp.getUIChairIndexByServerChair(server_chair)
            if resp_json.IsAgree[server_chair + 1] == true then
                self:ShowReadImage(ui_chair, true)
            end

        end

        --local request = { }
        --handleGameMessage.SendGameMessage(handleGameMessage.Table_GameMessage_ASSID.ASS_GM_AGREE_GAME, request)
    end

    if resp_json.NTUser ~= nil and resp_json.NTUser ~= 255  then
        local ui_chair = ViewHelp.getUIChairIndexByServerChair(resp_json.NTUser)
        if self.PlayerHeads[ui_chair] ~= nil then
            self.PlayerHeads[ui_chair].Image_NT:setVisible(true)
        end
    end

    self.isGameStation = false

end

function PlayersHeadUIManager:ui_open()
    self.ui_project:setVisible(true)
end

function PlayersHeadUIManager:ui_close()
    self.ui_project:setVisible(false)
end

--对纹理进行裁剪  裁剪的图片textureSprite   裁剪的形状图片路径maskPath  
function PlayersHeadUIManager:maskedSprite(textureSprite, maskPath)    
    local textureSize = textureSprite:getContentSize()  
  
    local maskSprite = cc.Sprite:create(maskPath)  
    local maskSize = maskSprite:getContentSize()  
  
    local renderTexture = cc.RenderTexture:create(maskSize.width,maskSize.height)  
    maskSprite:setPosition(cc.p(maskSize.width/2,maskSize.height/2))
    textureSprite:setScaleX(maskSize.width/textureSize.width)
    textureSprite:setScaleY(maskSize.height/textureSize.height)  
    textureSprite:setPosition(cc.p(maskSize.width/2,maskSize.height/2))  
  
    maskSprite:setBlendFunc(cc.blendFunc(GL_ONE,GL_ZERO))  
    textureSprite:setBlendFunc(cc.blendFunc(GL_DST_ALPHA,GL_ZERO))  
  
    renderTexture:begin()  
    maskSprite:visit()  
    textureSprite:visit()  
    renderTexture:endToLua()  
  
    local texutre = renderTexture:getSprite():getTexture() 
    --retSprite:setFlippedY(true)  
    return texutre  
end 

function PlayersHeadUIManager:onGameResult(resp_json)
    for i =1 ,PLAYER_COUNT do
        self:SetHeadBipaiPos(i)
    end
end

function PlayersHeadUIManager:ResetHeadsPos()
--    for i =1 ,PLAYER_COUNT do
--        self.PlayerHeads[i]:setPosition(self.HeadsOriginalPos[i])
--    end
end

function PlayersHeadUIManager:SetHeadBipaiPos(ui_chair)
--    if ui_chair >  PLAYER_COUNT and ui_chair < 1 then
--        return
--    end
--    local posTab = {
--    cc.p(self.HeadsOriginalPos[1].x + 528, self.HeadsOriginalPos[1].y),
--    cc.p(self.HeadsOriginalPos[2].x - 150 ,self.HeadsOriginalPos[2].y - 140),
--    cc.p(self.HeadsOriginalPos[3].x + 30,self.HeadsOriginalPos[3].y),
--    cc.p(self.HeadsOriginalPos[4].x + 130 ,self.HeadsOriginalPos[4].y - 140)
--    }
    --self.PlayerHeads[ui_chair]:setPosition(posTab[ui_chair])
end

return PlayersHeadUIManager

--endregion
