--region *.lua
--Date
--此文件由[<PERSON><PERSON><PERSON>]插件自动生成
GameStation = {
    GS_WAIT_SETGAME     =   0,              --等待东家设置状态
    GS_WAIT_ARGEE       =   1,              --等待同意设置
    GS_ROBBANKER        =   25,             --等待抢庄状态
    GS_PLAYING			=	26,	            --各玩家进行打牌状态
    GS_RESULT           =   27,
    GS_WAIT_NEXT_ROUND	=	28	            --等待下一回合状态

}

CardType = {
    CT_WL                 = 0,   -- 乌龙
    CT_PAIRS              = 1,   -- 对子
    CT_TWO_PAIRS          = 2,   -- 两对
    CT_THREE              = 3,   -- 三条
    CT_SHUNZA             = 4,   -- 顺子
    CT_TONGHUA            = 5,   -- 同花
    CT_GOURD              = 6,   -- 葫芦
    CT_TIEZHI             = 7,   -- 铁支
    CT_TONGHUASZ          = 8,   -- 同花顺
    CT_WUTONG             = 9,    -- 五同
    CT_LIUTONG            = 10   -- 六同
}
CardTypeName = {
  "乌龙",       -- 1,
  "对子",       -- 2,
  "两对",       -- 3,
  "三条",       -- 4,
  "顺子",       -- 5,
  "同花",       -- 6,
  "葫芦",       -- 7,
  "铁支",       -- 8,
  "同花顺",     -- 9, 
  "五同",       --10
  "六同"
}
SpecCTName={
    "三同花",--1
    "三顺子",--2
    "六对半",--3
    "全大",--4
    "全小",--5
    "五队三条",--6
    "四套三条",--7
    "一点黑",--8
    "一点红",--9
    "六同",--10
    "三同花顺",--11
    "一条龙",--12
    "七同",--13
    "全黑",--14
    "全红",--15
    "至尊青龙",--16
    "半大",--17
    "半小",--18
}


SpecialCardType = {
    SCT_STH               = 1,   -- 三同花
    SCT_SSZ               = 2,   -- 三顺子

    SCT_LDB               = 3,   -- 六对半
    SCT_QD                = 4,  -- 全大
    SCT_QX                = 5,   -- 全小

    SCT_WDST              = 6,   -- 五队三条
    SCT_STST              = 7,   -- 四套三条

    SCT_ONEBLACK          = 8,   -- 一点黑
    SCT_ONERED            = 9,   -- 一点红


    SCT_LT                = 10, -- 六同
    SCT_STHS              = 11,  -- 三同花顺-不用
    SCT_YTL               = 12,  -- 一条龙
    SCT_QT                = 13,  -- 七同


    SCT_ALLBLACK          = 14,  -- 全黑
    SCT_ALLRED            = 15,  -- 全红
    SCT_QL                = 16,  -- 清龙

    SCT_HALF_BIG          = 17,  -- 半大
	SCT_HALF_SMALL        = 18,  -- 半小
}

--UI玩家方位 (上下左右)
Direct = {
    Down = 1,
    Right = 2,
    Up = 3,
    Left = 4
}

--表情显示位置
EMOJI_POSITIONS = {

    cc.p(491,72),
    cc.p(1066,355),
    cc.p(870,633),
    cc.p(222,355)
}

--聊天显示位置
CHAT_POSITIONS = {
    cc.p(461,97),
    cc.p(1089,381),
    cc.p(899,656),
    cc.p(185,380)
}

--快捷聊天内容
CHAT_CONTENTS =
{
    "快点啊，都等得我花都谢了！",
    "又断线了，网络怎么这么差啊！",
    "不要走，决战到天亮！",
    "你的牌打得也忒好啦！",
    "你是MM还是GG啊！",
    "和你合作真是太愉快了！",
    "大家好，很高兴见到各位！",
    "各位真是不好意思，我得离开一会！",
    "不要吵了，吵啥了，专心玩游戏吧！"
}

THSPlusType =
{
	THSPT_SIX_DU = 0,
	THSPT_SEVEN_DU = 1,
	THSPT_ALL_DU = 2,
};


--endregion
