--region *.lua
--Date
--此文件由[BabeLua]插件自动生成


import("..GameDefine")
local GameMusic = import("..GameMusic")
require("app/platform/game/"..GAME_ID.."/view/ViewHelp")

local ChatBox = import("...ChatBox")
local VoiceBox = import("...VoiceBox")
GameMusic = import("..GameMusic")

local PlayersScoreLayer = import("..."..GAME_ID..".view.PlayersScoreLayer")

local GameLayer = import('.GameLayer')
local GameVideoLayer = import('.GameVideoLayer')

local m_download = import("....common.download")
local GameSceneBase = import("...GameSceneBase")

local GameScene = class("GameScene" , GameSceneBase)
function GameScene:onCreate()
    self.super:registered_child(self)
    GameSceneModule:getInstance():init(self)
    local GameLayer = GameLayer:create(self)
    self.GameLayer = GameLayer
    self:addChild(GameLayer)
    self.PlayersInfos = {}--每个坐下玩家的玩家信息
    self:onBgColorChange()
    if g_server_obj:get_server_type() == "video" then
        self.GameVideoLayer = GameVideoLayer:create( self )
        self.GameVideoLayer:init_event()
        self:addChild( self.GameVideoLayer )
        self.GameVideoLayer:setZOrder( 99999 )
        local user_info = self:getSelfInfo()
        ViewHelp.setBasePosChair(tonumber(user_info.bDeskStation))
    end
    self:registerScriptHandler(handler(self,self.onNodeEvent))

    UserData:setCardColor(1)
    UserData:setBgColor(1)
end

function GameScene:DeskStation2View(v)
   -- cclog("v=============================="..tostring(v))

    local ui_chair = ViewHelp.getUIChairIndexByServerChair(v)
    cclog("GameScene:DeskStation2View uichair = "..tostring(ui_chair))
    return ui_chair
end


function GameScene:onBgColorChange()
    cclog("GameScene:onBgColorChange")
    self.GameLayer:onBgColorChange()
end

function GameScene:onCardColorChange()
    self.GameLayer.ResultUIManager:onCardColorChange()
    self.GameLayer.BaiPaiUIManager:onCardColorChange()
    self.GameLayer.PlayersHeadUIManager:onCardColorChange()
end

function GameScene:ChangeDesk()
    self.GameLayer.ResultUIManager:setInit()
    self.GameLayer.BaiPaiUIManager:stopCount()
    print("请求站起")
    g_server_obj:send_w_server_msg(102,1,1,true,nil)
    print("请求换桌")
    self:reqChangeDesk(true)
end

function GameScene:setClubMode(clubMode)
    self.clubMode = clubMode
end

function GameScene:getMoneyText(money)
    if money == nil  then
        return
    end
    local tmpstr = tostring(money)
    if self.clubMode == 2  then
        tmpstr = string.format("%.2f", money/1000)
        if (money % 1000) == 0  then
            tmpstr = string.format("%d", money/1000)
        end
    end

    return tmpstr
end

function GameScene:onNodeEvent(event)
    if event == "enter" then
        -- 返回键开启
        -- g_setMainLayerDelegate(self)
        if UserData:getMusic() == 1 then
           -- performWithDelay(self, function()
                 GameMusic:PlayBGMusic()
           -- end , 1) 
        end
        if self.callVedioBack ~= nil  then
            self.callVedioBack()
        end
    elseif event == "exit" then
        self:onDestroy()
    end
end

function GameScene:IsGoldRoom()
    if self:getGameType() == "tz1" then
        return true
    else
        return false
    end
end

function GameScene:HasDaqiang()
    return true
--    local ret = false
--    if self:IsGoldRoom() then
--    --金币场默认打枪
--        ret = true
--    else
--    --房卡场
--        local desk_config = self:getDeskInfo()
--        if desk_config.desk_select_config.Daqiang.id == 1 then
--            ret = true
--        else
--            ret = false
--        end
--    end
--    return ret
end

function GameScene:onDestroy()
    ViewHelp.setGameStation(GameStation.GS_WAIT_ARGEE)
    ViewHelp.setBasePosChair(-1)

    self.PlayersInfos = nil

    if handleGameMessage then
       handleGameMessage.resethandleGameMessage()
    end
    for k,v in pairs(package.loaded) do
        print("=============================>>",k)
        local findIndex = string.find(k,tostring(GAME_ID))

        if findIndex ~= nil then
            package.loaded[k]=nil
        end
    end
end

function GameScene:onGameResult(resp_json)
    -- 更新客户端缓存的玩家信息中的分数
   -- dump(resp_json.UserPoints)
    for _,info in pairs(self.PlayersInfos) do
        --info.dwMoney = info.dwMoney + resp_json.totalPoint[info.bDeskStation + 1]
        if resp_json.UserPoints  then
            info.dwMoney = resp_json.UserPoints[info.bDeskStation + 1]
        end
    end

end

function GameScene:onPaijuInfo(resp_json)
    print("GameScene:onPaijuInfo")
    self.PaijuInfo = resp_json

    local time = 1* #self.PlayersInfos*5
    time = time + resp_json.DisplayTime--self.GameLayer.ResultUIManager:getTime()
    if resp_json.AllAgreeDismissRoom then
        time = 0.5
    end

    performWithDelay(self, function()
        self.GameLayer:addChild(PlayersScoreLayer:create(self, resp_json), 3, 10)
    end , time)

end


--缓存已经坐下的玩家信息
function GameScene:cacheUserInfo()
    self.PlayersInfos = {}
    local sitDownPlayerMap = GameSceneModule:getInstance():getGameScene():getSitDownPlayerMap()
    --dump(sitDownPlayerMap)
    for key, sitPlayer in pairs(sitDownPlayerMap) do
        if not self:isInTable(sitPlayer,self.PlayersInfos) then
            table.insert(self.PlayersInfos,clone(sitPlayer))
        end
    end
    --dump(self.PlayersInfos)
end

function GameScene:isInTable(item,tab)
    for k,v in pairs(tab) do
        if item.bDeskStation == v.bDeskStation or item.dwUserID == v.dwUserID then
            return true
        end
    end
    return false
end

--重新设置聊天表情的显示位置
function GameScene:resetEmojiShowPositions()

    local chatPos = { }

--    emojiPos = EMOJI_POSITIONS
--    chatPos = CHAT_POSITIONS

    for i = 1, PLAYER_COUNT do 
        chatPos[i] = cc.p(self.GameLayer.PlayersHeadUIManager.PlayerHeads[i]:getPosition())
    end

    -- 初始化表情的位置
    self:initExpression(
    chatPos,
    1.0,
    15,
    2
    )

    -- 初始化文本聊天的位置
    self:initChatText(
    chatPos,
    { 4, 2, 2, 4, 4, 4, 4 },
    CHAT_CONTENTS,
    300,
    1.0,
    2
    )

end

-------------------------------------------平台消息---------------------------------------


--@desc 有玩家加入房间
function GameScene:playerJoinRoom( user_info )
    
end

--@desc  有玩家离开房间
-- @param code 1.您正在游戏中，不允许退出 2.您已经离开，不需要请求再次离开
function GameScene:playerLeaveRoom( user_info , code )
    print("GameScene:playerLeaveRoom code = " .. code)
    if code == 0 then
        print("玩家" .. user_info.dwUserID .. "离开房间")
    elseif code == 1 then
        api_show_Msg_Box("您正在游戏中，不允许退出")
    elseif code == 2 then
        api_show_Msg_Box("您已经离开，不需要请求再次离开")
    end
end


--@desc  加入桌子,玩家断线重连调用
--@param code 1.桌子号不属于本房间 5.桌子人数已满人 -1.其它原因
function GameScene:playerJoinDesk( user_info , desk_info , code )
    print("有玩家加入桌子",code)
    if user_info.dwUserID == ViewHelp.getSelfUserID() then
        --自己断线重连进入桌子
        ViewHelp.setBasePosChair(tonumber(user_info.bDeskStation))
        self.GameLayer.PlayersHeadUIManager:showSitUsers()
        self:resetEmojiShowPositions()
    end
    if user_info.bDeskStation < 0 or  user_info.bDeskStation > 8  then
        return
    end

    if user_info.look == true  then
        return
    end

    self.GameLayer.PlayersHeadUIManager:onPlayerJoinDesk( user_info , desk_info , code )
end

-- 有玩家离开桌子
--@desc  离开桌子
--@param code 1.正在游戏不准离开桌子 2.已经离开不需要再次离开
function GameScene:playerLeaveDesk( user_info , desk_info  , code )
    print("CGameScene:playerLeaveDesk------------code = " .. code)
    if user_info.bDeskStation < 0 or  user_info.bDeskStation > 8  then
        return
    end

    if user_info.look == true  then
        return
    end

    if code == 0 then
        print("玩家离开桌子")
        self.GameLayer.PlayersHeadUIManager:onPlayerLeaveDesk( user_info , desk_info , code)
    elseif code == 1 then
        api_show_Msg_Box("正在游戏不准离开桌子")
    elseif code == 2 then
        api_show_Msg_Box("已经离开不需要再次离开")
    elseif code == 50 then
        if self:IsGoldRoom() then  --金币场
            self.GameLayer.PlayersHeadUIManager:onPlayerLeaveDesk( user_info , desk_info , 0)
        end
    end
end


-- 有玩家坐下
--@param code 1.位置上已有玩家 2.分数不足 3.座位不正确 4.玩家未进房间 5.不允许坐下
-- 6.房间已解散 7.被房主禁了 8.桌子索引不属于本房间 9.桌子已满人 10.玩家已经坐下 -1.其它原因
function GameScene:playerSitDesk( user_info , desk_info , code  )

    cclog("GameScene:playerSitDesk code = " ..tostring(code))
   -- dump(user_info)
   -- dump(desk_info)
    if not user_info then
       return
    end

    if code == 0 or code == 50 then
        --如果是自己坐下就保存BasePosChair
        if self:getSelfInfo().dwUserID == tonumber(user_info.dwUserID) then
           print("------设置自己的椅子号 " .. user_info.bDeskStation)
           api_hide_loading()
           ViewHelp.setBasePosChair(tonumber(user_info.bDeskStation))
          
           self:resetEmojiShowPositions()

           if self.bLookerSitReq == true  then
               self:reqDeskRebind()
           end
            
           --handleGameMessage.SendGameMessage(handleGameMessage.Table_GameMessage_ASSID.ASS_GM_AGREE_GAME, request)
        end
        self.GameLayer.PlayersHeadUIManager:updatePlayerInfo(255)
    elseif code == 1 then
        api_show_Msg_Box("位置上已有玩家")
    elseif code == 2 then
        api_show_Msg_Box("分数不足")
    elseif code == 3 then
        api_show_Msg_Box("座位不正确")
    elseif code == 4 then
        api_show_Msg_Box("玩家未进房间")
    elseif code == 5 then
        api_show_Msg_Box("不允许坐下")
    elseif code == 6 then
        api_show_Msg_Box("房间已解散")
    elseif code == 7 then
        api_show_Msg_Box("被房主禁了")
    elseif code == 8 then
        api_show_Msg_Box("桌子索引不属于本房间")
    elseif code == 9 then
        api_show_Msg_Box("桌子已满人")
    elseif code == 10 then
        api_show_Msg_Box("玩家已经坐下")
    end

end

--@desc  站起
--@param code 1.玩家正在游戏中 2.玩家不在房间  -1.其它原因
function GameScene:playerStandUp( user_info , desk_info , code ) 
    print("GameScene:playerStandUp code = "..tostring(code))
  --  dump(user_info)
    --dump(desk_info)
    if user_info.bDeskNO ~= self:getSelfInfo().bDeskNO then--规避平台换桌BUG，B玩家从其它桌，换来A的桌，A会收到B站起的消息
        return 
    end

    if user_info.bDeskStation < 0 or  user_info.bDeskStation > 8  then
        return
    end

    if user_info.look == true  then
        return
    end

    if code == 0 or code == 50 then
        local ui_chair_index =  ViewHelp.getUIChairIndexByServerChair(user_info.bDeskStation)
        if ui_chair_index == 1 then
            for i =2,PLAYER_COUNT do
                self.GameLayer.PlayersHeadUIManager:HidePlayerInfo(i)
            end
        else
            self.GameLayer.PlayersHeadUIManager:HidePlayerInfo(ui_chair_index)
        end
       
        local GameStation = ViewHelp.getGameStation()
        print("GameStation == " .. GameStation)
        if GameStation <= 1  then
            if user_info.bDeskStation >= 0 and user_info.bDeskStation < PLAYER_COUNT then
                for i,player in pairs(self.PlayersInfos) do
                    print("PlayersInfos.bdesk",player.bDeskStation)
                    if player.bDeskStation == user_info.bDeskStation then
                        print("i= " , i)
                        table.remove(self.PlayersInfos,i)
                        --self.PlayersInfos[i+1] = nil
                        print("清除玩家缓存信息")
                    end
                end
            end

            if user_info.dwUserID == ViewHelp.getSelfUserID() then
                self:Exit_game()
            end
        end
    elseif code == 1 then
        api_show_Msg_Box("正在游戏中,站起失败")
    elseif code == 2 then
        api_show_Msg_Box("玩家不在房间")
    end

end

--@desc  托管
function GameScene:playerTrusteeship( user_info , desk_info , code ) 
end


--@desc  语音           有玩家发送了语音聊天信息
--@param code 1.房间禁止发话 2.不在房间 3.非法音频ID 4.重复使用音频ID -1.其它原因
function GameScene:playerRecorder( user_info , desk_info , recordInfo , code ) 
    if UserData:getAutoPlaySound() == 1 then
        lua_to_plat:playRecorder(recordInfo.voiceID)
    end
end

--@desc  语音           有玩家发送了语音聊天信息
--@param code 1.房间禁止发话 2.不在房间 3.非法音频ID 4.重复使用音频ID -1.其它原因
function GameScene:playerRecorderNew( user_info , desk_info , recordInfo , code )
    if user_info.bDeskStation >= 0 and recordInfo and recordInfo.voiceID and recordInfo.duration then
        --local ui_chair = self:getHelper():DeskStation2View( user_info.bDeskStation ) 
        local ui_chair = ViewHelp.getUIChairIndexByServerChair(user_info.bDeskStation)
        -- UserData:getAutoPlaySound() 在美术效果上设置反了， 这里统一用 0 为自动播放
        local voiceBox = VoiceBox:create( recordInfo.voiceID , recordInfo.duration , self.direction_list[ui_chair] , UserData:getAutoPlaySound() == 1 )
        voiceBox:setPosition( self.pos_chat_list[ui_chair] )
        voiceBox:setZOrder( self.chat_zorder )
        self:addChild(voiceBox)

    end
end

--@desc  播放快速文字音效
--@param index 快速文字的下标
function GameScene:playQuickTextSound( text_index , user_info )
    GameMusic:PlayEffect("msg_".. text_index ..".mp3",user_info.bDeskStation + 1)
end

-- 聊天           有玩家发送了文字聊天信息
function GameScene:playerChat( user_info , desk_info , chatInfo , code  ) 
    if user_info.bDeskStation >= 0 and chatInfo and chatInfo.content then
        --local ui_chair = self:getHelper():DeskStation2View( user_info.bDeskStation )
        local ui_chair = ViewHelp.getUIChairIndexByServerChair(user_info.bDeskStation)
        if chatInfo.content.expression and self.pos_expression_list and self.pos_expression_list[ui_chair] then
            --表情
            if chatInfo.content.expression > self.expression_count then            
                chatInfo.content.expression = 1                
            end
            local path = string.format("common/expression/expression_%02d/node.csb",chatInfo.content.expression)
            local game_armature = cc.CSLoader:createNode(path)
            local action = cc.CSLoader:createTimeline(path)
            action:gotoFrameAndPlay(0,false)
            game_armature:runAction(action)
            action:setLastFrameCallFunc(function()
                game_armature:removeFromParent()
            end)
            game_armature:setScale( self.expression_scale )
            game_armature:setPosition( self.pos_expression_list[ui_chair] )
            game_armature:setZOrder( self.expression_zorder )
            self:addChild(game_armature)
        elseif chatInfo.content.QuickText and self.pos_chat_list and self.pos_chat_list[ui_chair] then
            -- 快速文本 
            local chatBox = ChatBox:create( self.quick_text_list[chatInfo.content.QuickText] , 2.5 , self.direction_list[ui_chair] , self.max_text_width)
            chatBox:setPosition( self.pos_chat_list[ui_chair] )
            chatBox:setZOrder( self.chat_zorder )
            self:addChild(chatBox)
            self:playQuickTextSound(chatInfo.content.QuickText , user_info)
        elseif chatInfo.content.Text and self.pos_chat_list and self.pos_chat_list[ui_chair] then
            -- 文字聊天
            local chatBox = ChatBox:create( chatInfo.content.Text , 2.5 , self.direction_list[ui_chair] , self.max_text_width )
            chatBox:setPosition( self.pos_chat_list[ui_chair] )
            chatBox:setZOrder( self.chat_zorder )
            self:addChild( chatBox )
        end        
    end 
end

--@desc  被T           玩家被房主T出房间
--@param code 1.不能踢自己 2.不是房主不能T人 3.要踢出的用户不在房间中 4.你不在房间中不能操作 5.玩家已曾踢除 10.请求参数有误 -1.其它原因
function GameScene:playerByMasterBoot( user_info , desk_info , code )


    if code == 0 then
        print("GameScene 你已经被房主踢出房间")
--        api_show_Msg_Box("你已经被房主踢出房间！",function()
--            self:Exit_game()
--        end)
    elseif code == 1 then
        api_show_Msg_Box("不能踢自己！")
    elseif code == 2 then
        api_show_Msg_Box("不是房主不能T人！")
    elseif code == 3 then
        api_show_Msg_Box("要踢出的用户不在房间中！")
    elseif code == 4 then
        api_show_Msg_Box("你不在房间中不能操作！") 
    elseif code == 5 then
        api_show_Msg_Box("玩家已曾踢除！") 
    elseif code == 10 then
        api_show_Msg_Box("请求参数有误！") 
    end
    

end



--@desc  申请加分数结果返回
--@param code 0.发送成功，待房主给你回应 1.您不在房间中，不可以申请 2.房间已解散 3.错误内容类型码 10-参数错误 -1.其他原因
function GameScene:playeApplyAddPointBack( code )
end

--@desc  给玩家加分结果返回
--@param code 1.房主身份验证不通过; 2.您不是房主没权加分 3.桌子不存在; 4.该玩家没有进入过本桌子; 10.请求参数有误; -1.其它原因
--@param add_point 本次添加的分数
function GameScene:playeMasterAddPointBack( user_info , desk_info , code , add_point)
end

--@desc  桌子激活
--@param code 0.激活桌子成功 1.未进入房间 2.你不是房主无权限 3.桌子已曾激活 -1.其它原因
function GameScene:deskStart()
    
end

--@desc  聊天开关改变
--@param code 1.不是房主不能操作 2.不在房间中不能操作 3.不能重复操作 10.请求参数有误 -1.其它原因
function GameScene:changeChatSwitch( isOpen , code )
end

--@desc  进入开关改变
--@param code 1.不是房主不能操作 2.不在房间中不能操作 3.不能重复操作 10.请求参数有误 -1.其它原因
function GameScene:changeEnterDeskSwitch( isOpen , code )
end


-- 掉线通知
-- code 1.socket错误 2.心跳超时
function GameScene:onOffLineNotify( code , desc )
    print("----GameScene:onOffLineNotify  code = " .. code .. "  desc = " .. desc)
    
    if code == 1 then
        api_show_Msg_Box(" 你已经掉线 ！",function()
            self:Exit_game(5)
        end , true)
    elseif code == 2 then
        api_show_Msg_Box(" 你已经掉线 ！",function()
            self:Exit_game(5)
        end , true )
    elseif code == 3 then
        api_show_Msg_Box("你的账号在另一个移动端登录" , function()
            self:Exit_game()
        end , true )
    end

end


--@desc  桌子解散
--@param code 1.房主解散 2.对局已打满  3.桌子限期已到  4大家同意解散 
--@param user_info 有玩家请求解散时对应玩家的信息
function GameScene:onDeskDismiss( code , user_info )
    print("GameScene:onDeskDismiss")
    if code == 1 then
        api_show_Msg_Box("房主解散房间" , function()
            self:Exit_game()
        end)
    elseif code == 2 then
        self:removeDismissDeskTip()
--        api_show_Msg_Box("对局已打满",function()
--            self:Exit_game()
--        end)
    elseif code == 3 then
        api_show_Msg_Box("桌子限期已到",function()
            self:Exit_game()
        end)
    elseif code == 4 then
        self:removeDismissDeskTip()
        api_show_Msg_Box("超过半数玩家同意解散房间",function()
            
        end)
    end
end


--@desc  桌子解散
--@param code 1.有玩家申请解散房间 2.有玩家同意解散房间 3.有玩家拒绝解散房间
--@param user_info 对应玩家的信息
function GameScene:onDeskDismissAction( code , user_info )
    print("GameScene:onDeskDismissAction")
    if code == 1 then
        print("有玩家申请解散房间")

        if user_info.dwUserID ~= self:getSelfInfo().dwUserID then
            
             self.dismissDeskTip = api_show_Msg_Tip("是否同意解散房间",
             function()
                --同意解散房间
                self:reqDissmisDesk(2)
                self.dismissDeskTip:stopAllActions()
                self.dismissDeskTip:setVisible(false)
                self.dismissDeskTip = nil
             end,
             function()
                --不同意解散房间
                self:reqDissmisDesk(3)
                self.dismissDeskTip = nil
             end
             ,true)

        end

    elseif code == 2 then
        print("有玩家同意解散房间")

    elseif code == 3 then
        print("有玩家拒绝解散房间")
        if self.dismissDeskTip ~= nil then
            self.dismissDeskTip:removeFromParent()
        end

        if user_info.dwUserID ~= ViewHelp.getSelfUserID() then
            api_show_Msg_Box("玩家 ".. user_info.nickName .." 拒绝解散房间")
        end

    end
end

--@desc  房主请求解散失败时的返回
--@param code 0.解散命令已成功，但是要等待游戏结束 1.未在房间里没权解散 2.不是房主无权解散 3.桌子已经解散不需要重复解散 -1.其他原因 
function GameScene:onMasterDismissDeskFail( code  )
    print("GameScene:onMasterDismissDeskFail" .. code)
    if code == 0 then
        --api_show_Msg_Box("解散命令已成功，但是要等待游戏结束")
    elseif code == 1 then
        api_show_Msg_Box("未在房间里没权解散")
    elseif code == 2 then
        api_show_Msg_Box("不是房主无权解散")
    elseif code == 3 then
        api_show_Msg_Box("桌子已经解散不需要重复解散")
    end
end



--@desc  玩家 申请解散 失败时的返回
--@param code 1.在这之前已经有玩家申请了
function GameScene:onPlayerDismissDeskFail( code  )
    print("GameScene:onPlayerDismissDeskFail")
    if code == 1 then
        api_show_Msg_Box("已经有玩家申请了解散房间")
    end
end

--@desc  玩家 同意拒绝解散房间 失败时的返回
--@param code 1.没有玩家申请解散 2.参数错误 3.已经同意了，不用再次同意
function GameScene:onAgreePlayerDismissDeskFail( code  )
    print("GameScene:onAgreePlayerDismissDeskFail")

end

--@desc  玩家 拒绝解散房间 失败时的返回
--@param code 1.没有玩家申请解散 2.参数错误
function GameScene:onRefusePlayerDismissDeskFail( code  )
    print("GameScene:onRefusePlayerDismissDeskFail")
    if code == 1 then
        api_show_Msg_Box("拒绝解散房间失败,没有玩家申请解散")
    else
        api_show_Msg_Box("拒绝解散房间失败,参数错误")
    end
end


--@desc  收到申请信息
function GameScene:onHasApplyInfo()
end

--同桌游使用到的--用户同意
function GameScene:UserAgreeGame(code,data)
    
end

--大厅游戏结算消息
function GameScene:game_finish(code,data)
    
end

--用户金币变化
function GameScene:user_score_change(code,score_info)
    
end

--用户人数过少，需要重新排队
function GameScene:user_need_repeat_queue()
    
end

--@desc 其他玩家掉线通知
function GameScene:other_player_offline( user_info )
    --api_show_Msg_Box("【"..user_info.nickName .."】掉线")
    self.GameLayer.PlayersHeadUIManager:onOtherPlayerOffline(user_info)
end

function GameScene:play_game_video( game_data )
    if self.GameVideoLayer then
        self.GameVideoLayer:begin_video( game_data )
    end
end

--@desc 退出游戏的接口
--@param command_id 退出到大厅的操作命令ID 
--  nil 的话不需要做任何操作
--  1.打开某桌子的战绩界面 code 为对应的房间信息
--  2.打开提示框（只有一个确定按钮的） code 为提示的内容
function GameScene:Exit_game( command_id, code )
    GameSceneModule:getInstance():onDestroy()
    self.super:Exit_game( command_id, code )
end
--------------------------------------end------------------------------------------
function GameScene:check_ip_same( desk_max_people )
end
return GameScene
--endregion
