--region *.lua
--Date
--此文件由[BabeLua]插件自动生成

local GameMusic = import("..GameMusic")

local SettingLayer = class("SettingLayer",function() 
        return createCSB("game_res/"..GAME_ID.."/ui_csb/SettingLayer.csb", true) 
    end)
local SET_CLOSE = 0
local SET_OPEN = 1

function SettingLayer:ctor(ui_root)
    
    self.ui_root = ui_root

    local Panel_root = self:getChildByName("Panel_setting")

    local Image_bg = Panel_root:getChildByName("Image_bg")
    local button_close = Image_bg:getChildByName("Button_close")
    button_close:addClickEventListener(function() 
        GameMusic:playClickEffect()
        self:removeFromParent()
    end)


    local Panel_Sound = Image_bg:getChildByName("Panel_Sound")
    --音效开关
    local button_effect_off = Panel_Sound:getChildByName("Button_effect_off")
    local button_effect_on = Panel_Sound:getChildByName("Button_effect_on")
    self:ButtonToggle( button_effect_off , button_effect_on , UserData:getSound() , function(cur_state)
            UserData:setSound( cur_state )
        end )

    --背景音乐开关
    local button_music_off = Panel_Sound:getChildByName("Button_music_off")
    local button_music_on = Panel_Sound:getChildByName("Button_music_on")
    self:ButtonToggle( button_music_off , button_music_on , UserData:getMusic() , function(cur_state)
            UserData:setMusic( cur_state )
            printf("getMusicVolume-----%d",AudioEngine.getMusicVolume())
            if cur_state == 1 then            
                GameMusic:PlayBGMusic()
            else
                Music:stopMusic()
            end
        end)

    --离开
    local Button_leave = Image_bg:getChildByName("Button_leave")
    --房主显示解散房间
    local GameScene = GameSceneModule:getInstance():getGameScene()
    local chair = ViewHelp.getBasePosChair()
    local player_info = GameScene:getSelfInfo()
    local deskInfo = GameScene:getDeskInfo()
    if not GameScene:IsGoldRoom() and deskInfo.mastUserID  == player_info.dwUserID then
         print("set release room png")
         Button_leave:loadTextureNormal("game_res/"..GAME_ID.."/ui_res/setting/btn_jiesan.png")
    else
        local  curGameStation = ViewHelp.getGameStation();
        print("curGameStation    =    " .. curGameStation)
        if ViewHelp.getGameStation() <= GameStation.GS_WAIT_ARGEE or GameSceneModule:getInstance():getGameScene():getGameType() == "tz1" then
            --游戏未开始
            Button_leave:loadTextureNormal("game_res/"..GAME_ID.."/ui_res/setting/btn_likai.png")
        else
            --游戏已经开始
            Button_leave:loadTextureNormal("game_res/"..GAME_ID.."/ui_res/setting/btn_jiesan.png")
        end

        if player_info.look == true  then
             Button_leave:loadTextureNormal("game_res/"..GAME_ID.."/ui_res/setting/btn_likai.png")
        end

    end
    if GameScene:IsGoldRoom() then
        Button_leave:setVisible(false)
    end
    Button_leave:addClickEventListener(function() 
        print("Button_leave-----")
        --申请站起
        local GameScene = GameSceneModule:getInstance():getGameScene()
        local deskInfo = GameScene:getDeskInfo()
        local chair = ViewHelp.getBasePosChair()
        local player_info = GameScene:getSelfInfo()
        if player_info.look == true  then
            GameScene:Exit_game()
            return
        end
        --玩家离开游戏
        local  curGameStation = ViewHelp.getGameStation();
        print("curGameStation    =    " .. curGameStation)
        if ViewHelp.getGameStation() <= GameStation.GS_WAIT_ARGEE or GameSceneModule:getInstance():getGameScene():getGameType() == "tz1" then
            print("GameScene:reqDissmisDesk    game not start ")
            --游戏未开始 申请离开桌子
            if GameScene:IsGoldRoom() then 
                GameScene:Exit_game()
            else
                if  deskInfo.mastUserID  == player_info.dwUserID then
                    --房主申请解散房间
                    GameScene:reqDissmisDesk(0)
                else
                    local request = {}
                    handleGameMessage.SendGameMessage(handleGameMessage.Table_GameMessage_ASSID.ASS_QUIT_REQ,request)
                    GameSceneModule:getInstance():getGameScene():reqLeaveDesk()
                    GameScene:Exit_game()
                end
            end
        else
            print("GameScene:reqDissmisDesk    type = 1 ")
            GameScene:reqDissmisDesk(1)

            --请求解散房间
        end
        
        self:removeFromParent()
    end)

    --换桌布
    local Panel_Table =  Image_bg:getChildByName("Panel_Table")
    local tabbtnTab = {}
    local chooseimageTab = {}
    local tabbtnnames = {"Button_TableR", "Button_TableG", "Button_TableB"}
    for i = 1,3 do
        tabbtnTab[i] = Panel_Table:getChildByName(tabbtnnames[i])
        chooseimageTab[i] = tabbtnTab[i]:getChildByName("Image_choose")
        chooseimageTab[i]:setVisible(false)
    end
    chooseimageTab[UserData:getBgColor()]:setVisible(true)
    for i = 1,3 do
        tabbtnTab[i]:addClickEventListener(function()
            for j = 1,3 do
               chooseimageTab[j]:setVisible(false)
            end
            chooseimageTab[i]:setVisible(true)

            UserData:setBgColor(i)
            GameSceneModule:getInstance():getGameScene():onBgColorChange()
        end)
    end


    --换牌背
    local Panel_BackCard =  Image_bg:getChildByName("Panel_BackCard")
    local backbtnTab = {}
    local backchooseTab = {}
    local backbtnnames = {"Button_BackR", "Button_BackG", "Button_BackB"}
    for i = 1,3 do
        backbtnTab[i] = Panel_BackCard:getChildByName(backbtnnames[i])
        backchooseTab[i] = backbtnTab[i]:getChildByName("Image_choose")
        backchooseTab[i]:setVisible(false)
    end
    backchooseTab[UserData:getCardColor()]:setVisible(true)
    for i = 1,3 do
        backbtnTab[i]:addClickEventListener(function()
            for j = 1,3 do
               backchooseTab[j]:setVisible(false)
            end
            backchooseTab[i]:setVisible(true)

            UserData:setCardColor(i)
            GameSceneModule:getInstance():getGameScene():onCardColorChange()
        end)
    end


    self:registerScriptHandler(handler(self,self.onNodeEvent))
end

function SettingLayer:onNodeEvent(event)

    if event == "enter" then
        g_pushBombBox(self)
    elseif event == "exit" then
        g_popBombBox(self)
    end
end


function SettingLayer:ButtonToggle(Button_close, Button_open, cur_state, function_call)
    Button_close:addClickEventListener(function()
        function_call(SET_OPEN)
        Button_open:setVisible(true)
        Button_close:setVisible(false)
    end)
    Button_open:addClickEventListener(function()
        function_call(SET_CLOSE)
        Button_open:setVisible(false)
        Button_close:setVisible(true)
    end)
    if cur_state==SET_OPEN then
        Button_open:setVisible(true)
        Button_close:setVisible(false)
    end
    if cur_state==SET_CLOSE then
        Button_open:setVisible(false)
        Button_close:setVisible(true)
    end
end

return SettingLayer

--endregion
