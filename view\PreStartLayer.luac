--region *.lua
--Date
--此文件由[BabeLua]插件自动生成

local PreStartLayer = class( "PreStartLayer" , function()
    return CCNode:create()
end )

function PreStartLayer:ctor( resp_json, user_list, self_chair, agree_call, refuse_call)
    self.self_chair = self_chair
    local Layer = cc.CSLoader:createNode("game_res/"..GAME_ID.."/ui_csb/PreStartLayer.csb")
    self:addChild( Layer )
    self.Image_bg  = Layer:getChildByName("Image_bg")
    self.Panel_bg = self.Image_bg:getChildByName("Panel_bg")
    local apply_player_nickname = self.Panel_bg:getChildByName("Text_2")
    
    local Text_5 = self.Panel_bg:getChildByName("Text_5")
    Text_5:setString("（超过1分钟未做选择，则默认接受）")
    
    -- 已有玩家的座位列表 对应的下标
    self.user_info = {}
    local j = 1
    for key , var in pairs(user_list) do
        if var.bDeskStation == resp_json.ReqUser then
            apply_player_nickname:setString( api_get_ascll_sub_str_by_ui( var.nickName , 12 ) )
            local Text_3 = self.Panel_bg:getChildByName("Text_3")
            Text_3:setPositionX(  apply_player_nickname:getPositionX() + apply_player_nickname:getContentSize().width )
        else
            table.insert( self.user_info ,{
                chair =  var.bDeskStation ,
                nick  =  var.nickName , 
            } )
        end
    end

    table.sort( self.user_info , function( a , b )
        return a.chair < b.chair
    end)
   
    print( "apply_player_chair" , resp_json.ReqUser )    

    local index = 1
    for key , var in pairs( self.user_info ) do
        if index <=3 then
            local Panel_player = self.Panel_bg:getChildByName("Panel_player_"..index)
            Panel_player:setTag( var.chair )
            local Text_2 = Panel_player:getChildByName("Text_2")
            Text_2:setString( api_get_ascll_sub_str_by_ui( var.nick , 12 ))
            index = index + 1
        end
    end
    for i = index , 3 do
        local Panel_player_item = self.Panel_bg:getChildByName("Panel_player_"..i)
        Panel_player_item:removeFromParent()
    end
    
    self.Image_clock = self.Panel_bg:getChildByName("Image_clock")
    self.Text_clock = self.Image_clock:getChildByName("Text_clock")
    self.time = clone(resp_json.lastTime)
    self:registerScriptHandler(handler(self,self.onNodeEvent))

    self.Button_ok = self.Panel_bg:getChildByName("Button_ok")
    self.Button_ok:addClickEventListener( function()
        self.Button_ok:setTouchEnabled( false )
        performWithDelay( self , function()
            if self.Button_ok then
                self.Button_ok:setTouchEnabled( true )    
            end
        end , 3)
        agree_call()
    end)

    self.Button_cancel = self.Panel_bg:getChildByName("Button_cancel")
    self.Button_cancel:addClickEventListener( function()
        self.Button_cancel:setTouchEnabled( false )
        performWithDelay( self , function()
            if self.Button_cancel then
                self.Button_cancel:setTouchEnabled( true ) 
            end              
        end , 3)
        refuse_call()
    end)

    if resp_json.agreeList then
        for key , var in pairs(resp_json.agreeList) do
            if var == true then
                self:set_apply( key-1 )
                if (key-1) ==  self.self_chair then
                    self:set_self_action()
                end
            end
        end   
    end
end


function PreStartLayer:onNodeEvent(event)
    if event == "enter" then
        self:update()
        self.schedulerID = cc.Director:getInstance():getScheduler():scheduleScriptFunc( handler(self , self.update ) , 1.0 ,false)
    elseif event == "exit" then
        self:clear_scheuler()
    end
end

function PreStartLayer:clear_scheuler()
    if self.schedulerID then    
        cc.Director:getInstance():getScheduler():unscheduleScriptEntry( self.schedulerID )
        self.schedulerID = nil
    end
end

function PreStartLayer:update()
    if self.time <= 0 then
        self:clear_scheuler()
        self:setVisible( false )
    else
        self.Text_clock:setString( self.time )
        self.time = self.time - 1
    end
end


---------------------------------------------
function PreStartLayer:set_other_offline( chair )
    if chair and self.chair_map[chair] then
        local Text_4 = self.Panel_player[self.chair_map[chair]]:getChildByName("Text_4")
        Text_4:setString( "离线" )
    end
end

function PreStartLayer:set_self_action()
    if self.Button_ok then
        self.Button_ok:removeFromParent()
        self.Button_ok = nil
    end
    if self.Button_cancel then
        self.Button_cancel:removeFromParent()
        self.Button_cancel = nil
    end
    
    self.Image_clock:setPositionX( 0 )
end

function PreStartLayer:set_state( chair , info )
    if chair then
        local Panel_player = self.Panel_bg:getChildByTag( chair )
        if Panel_player then
            local Text_4 = Panel_player:getChildByName("Text_4")
            Text_4:setString( info )
        end        
    end
    if chair == self.self_chair then
        self:set_self_action()
    end  
end

function PreStartLayer:set_apply( chair )
    self:set_state( chair , "同意" )
end

function PreStartLayer:set_refuse( chair )
    self:set_state( chair , "拒接" ) 
end

return PreStartLayer
--endregion
