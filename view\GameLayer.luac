--region *.lua
--Date
--此文件由[BabeLua]插件自动生成

local BaseUIManager = import('.BaseUIManager')
local PlayersHeadUIManager = import('.PlayersHeadUIManager')
local BaiPaiUIManager = import('.BaiPaiUIManager')
local ResultUIManager = import('.ResultUIManager')
local RoundResultLayer = import('.RoundResultLayer')
local PlayersScoreLayer = import('.PlayersScoreLayer')

local RoundResultLayerTag = 1001

local GameLayer = class("GameLayer" ,function(gameScene)
    return createCSB("game_res/"..GAME_ID.."/ui_csb/GameLayer.csb", true)
end)

function GameLayer:ctor(parent)
    self.parent = parent
    local Image_root = self:getChildByName("Image_root")
    self.Image_root = Image_root
    self.BaseUIManager = BaseUIManager.new(Image_root:getChildByName("Panel_base"),self)  
    self.PlayersHeadUIManager = PlayersHeadUIManager.new(Image_root:getChildByName("Panel_players"),self)  
    self.BaiPaiUIManager = BaiPaiUIManager.new(Image_root:getChildByName("Panel_baipai"),self)
    self.ResultUIManager = ResultUIManager.new(Image_root:getChildByName("Panel_result"),self)
end

function GameLayer:onBgColorChange()
    cclog("GameLayer:onBgColorChange")
    local bgPath = {"ZhuoBu_R.png","ZhuoBu_G.png","ZhuoBu_B.png"}
    self.Image_root:loadTexture("game_res/"..GAME_ID.."/ui_res/base/"..bgPath[UserData:getBgColor()])
end

function GameLayer:ShowRoundResultLayer(json)
    self:HideResultLayer()
    local RoundResultLayer = RoundResultLayer:create(json)
    self.Image_root:addChild(RoundResultLayer,1001,RoundResultLayerTag)
end

function GameLayer:HideResultLayer()
    local RoundResultLayer = self.Image_root:getChildByTag(RoundResultLayerTag)
    if RoundResultLayer then
        RoundResultLayer:removeFromParent()
    end
end

function GameLayer:ShowPaijuLayer(json)
    local PlayersScoreLayer = PlayersScoreLayer:create(self,json)
    self.Image_root:addChild(PlayersScoreLayer)
end



return GameLayer


--endregion
