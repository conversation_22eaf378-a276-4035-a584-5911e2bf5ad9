--region *.lua
--Date
--此文件由[<PERSON><PERSON><PERSON>]插件自动生成

require("app/platform/game/"..GAME_ID.."/handleGameMessage")

local GameSceneModule = class("GameSceneModule")

function GameSceneModule:getInstance()
    if not self.GameSceneModule then
       self.GameSceneModule = self.new()
    end
    return self.GameSceneModule
end

function GameSceneModule:onDestroy()
    self.GameScene.GameLayer.BaiPaiUIManager:stopCount()
    ViewHelp.setAllServerChair({0,0,0,0})
    ViewHelp.setGameStation(GameStation.GS_WAIT_ARGEE)
    if self and self.GameSceneModule then
        self.GameSceneModule = nil
    end
end

function GameSceneModule:init(GameScene)
    self.GameScene = GameScene
    handleGameMessage.setGameMessageHandler(self)
end

function GameSceneModule:getGameScene()
    return self.GameScene
end


--游戏开始
function GameSceneModule:response_game_start(resp_json)
    print("------------------------------------------GameSceneModule:response_game_start recv game start")
    if resp_json.AllServerChair ~= nil then
        ViewHelp.setAllServerChair(resp_json.AllServerChair)
    end

    if resp_json.caiJinCardPoint  then
        ViewHelp.SetMaPaiVal(resp_json.caiJinCardPoint)
    end

    ViewHelp.setGameStation(GameStation.GS_PLAYING)
    if self.GameScene:IsGoldRoom() then
        self.GameScene:cacheUserInfo()
    else
        if resp_json.RoundTimes and resp_json.RoundTimes==1 then
            self.GameScene:cacheUserInfo()
        end
    end

    if resp_json.clubMode ~= nil then
        self.GameScene:setClubMode(resp_json.clubMode)
    end

    if g_server_obj and g_server_obj:get_server_type() == "video" then
        self.GameScene.GameLayer.ResultUIManager:setInit()
        self.GameScene.GameLayer:HideResultLayer()
        self.GameScene:cacheUserInfo()
    else
        self.GameScene.GameLayer.ResultUIManager:setInit()
        self.GameScene.GameLayer:HideResultLayer()
    end
    self.GameScene.GameLayer.BaseUIManager:onGameStart(resp_json)
    self.GameScene.GameLayer.PlayersHeadUIManager:onGameStart(resp_json)
    self.GameScene.GameLayer.BaiPaiUIManager:onGameStart(resp_json)
    self.GameScene.GameLayer.ResultUIManager:onGameStart(resp_json)
end

--提前开局请求
function GameSceneModule:response_game_prestart(resp_json)
    self.GameScene.GameLayer.BaseUIManager:onGamePreStart(resp_json)
end

--配牌
function GameSceneModule:response_cheat_info(resp_json)
    self.GameScene.GameLayer.BaiPaiUIManager:onCheatInfo(resp_json)
end

--玩家请求洗牌信息
function GameSceneModule:response_player_xi_pai_info(resp_json)
    if resp_json.bExistPlayerXiPai == true  then
        api_show_tips("请求洗牌失败，原因：已经有玩家选择洗牌了")
        return
    end

    if resp_json.bXiPai == true then
         api_show_tips("请求洗牌成功！！！")
    end

end 

-- 通知抢庄消息
function  GameSceneModule:response_robbanker_info(resp_json)
    print("--- GameSceneModule:response_robbanker_info")
    ViewHelp.setGameStation(GameStation.GS_ROBBANKER)
end

-- 抢庄结果
function  GameSceneModule:response_robbanker_result(resp_json)
    print("--- GameSceneModule:response_robbanker_result")
    self.GameScene.GameLayer.PlayersHeadUIManager:OnRobBanker(resp_json)
end

-- 定庄消息
function GameSceneModule:response_game_make_nt(resp_json)
    print("recieve make nt ")
    ViewHelp.setGameStation(GameStation.GS_PLAYING)
    
    self.GameScene:resetEmojiShowPositions()

    self.GameScene.GameLayer.PlayersHeadUIManager:onMakeNT(resp_json)
    print("GameSceneModule make nt  over")
end

-- 抓牌
function GameSceneModule:response_catch_card(resp_json)
    print("--- response_catch_card start")
    if resp_json.caiJinCardPoint  then
        ViewHelp.SetMaPaiVal(resp_json.caiJinCardPoint)
    end


    ViewHelp.setGameStation(GameStation.GS_PLAYING)
    --ViewHelp.SetGuiCards(resp_json.GuiPaiVal)
    self.GameScene.GameLayer.BaseUIManager:onCatchCard(resp_json)
    self.GameScene.GameLayer.BaiPaiUIManager:onCatchCard(resp_json)
    self.GameScene.GameLayer.PlayersHeadUIManager:onCatchCard(resp_json)
    print("--- response_catch_card end")

    
end

-- 摆牌结果
function GameSceneModule:response_baipai_result(resp_json)
    print("--- response_baipai_result start")
    self.GameScene.GameLayer.PlayersHeadUIManager:onBaipaiResult(resp_json)
    self.GameScene.GameLayer.BaiPaiUIManager:onBaipaiResult(resp_json)
    self.GameScene.GameLayer.ResultUIManager:onBaipaiResult(resp_json)
    print("--- response_baipai_result end")
end

-- 一回合算分
function GameSceneModule:response_point_result(resp_json)
    print("--- response_point_result start")
    ViewHelp.setGameStation(GameStation.GS_RESULT)
    self.GameScene.GameLayer.PlayersHeadUIManager:onGameResult(resp_json)
    self.GameScene.GameLayer.ResultUIManager:onGameResult(resp_json)
    self.GameScene:onGameResult(resp_json)

    print("--- response_point_result start")
end

-- 一回合结束
function GameSceneModule:response_round_finish(resp_json)
    print("--- response_round_finish start")
    ViewHelp.setGameStation(GameStation.GS_WAIT_NEXT_ROUND)
    --self:getInstance():getGameScene().GameLayer.BaseUIManager:updateStartBtn(resp_json)
    self.GameScene.GameLayer:ShowRoundResultLayer(resp_json)
    print("--- response_round_finish END")
end

--玩家同意游戏
function GameSceneModule:response_agree_game(resp_json)
    print("recv continue game")
    --self.GameScene:cacheUserInfo()

    local chair_index = ViewHelp.getBasePosChair()
    if resp_json.chair ~= chair_index then
        print("don't hanle other people continue")
        --return
    end
    if resp_json.chair == chair_index then
        self.GameScene.GameLayer.ResultUIManager:setInit()
        self.GameScene.GameLayer:HideResultLayer()
    end

    self.GameScene.GameLayer.PlayersHeadUIManager:onAgreeGame(resp_json)
--    self.GameScene:onAgreeGame()
--    self.GameScene.GameLayer.OutCardUIManager:onAgreeGame(resp_json)
--    self.GameScene.GameLayer.MenuManager:onAgreeGame(resp_json)
--    self.GameScene.GameLayer.PlayersHeadUIManager:onAgreeGame(resp_json)
--    self.GameScene.GameLayer.GameBaseUIManager:onAgreeGame(resp_json)
end

--总结算
function GameSceneModule:response_game_paiju_info(resp_json)
    print("recv paiju info")
    --收到牌局消息
    --self.GameScene:Exit_game()
    --self.GameScene:onPaijuInfo(resp_json)

end

-- 断线重连
function GameSceneModule:response_game_station(resp_json)
    print("recv game station")
    if resp_json == nil or resp_json.chair == nil then
        return
    end
    --更新自己的椅子号
    if resp_json.chair ~= nil then
        ViewHelp.setBasePosChair(resp_json.chair)
    end

    --缓存当前游戏状态
    if resp_json.GSID ~= nil then
        ViewHelp.setGameStation(resp_json.GSID)
    end

    if resp_json.clubMode ~= nil then
        self.GameScene:setClubMode(resp_json.clubMode)
    end

    --保存服务端椅子号
    if resp_json.AllServerChair ~= nil then
        ViewHelp.setAllServerChair(resp_json.AllServerChair)
    end

    if resp_json.caiJinCardPoint  then
        ViewHelp.SetMaPaiVal(resp_json.caiJinCardPoint)
    end

    --缓存坐下玩家的状态
    self.GameScene:cacheUserInfo()

--    --设置鬼牌
--    if resp_json.GSID == GameStation.GS_PLAYING then
--        ViewHelp.SetGuiCards(resp_json.GuiPaiVal)
--    end
    
    self.GameScene.GameLayer.PlayersHeadUIManager:onGameStation(resp_json)
    self.GameScene.GameLayer.BaseUIManager:onGameStation(resp_json)
    self.GameScene.GameLayer.BaiPaiUIManager:onGameStation(resp_json)
    self.GameScene.GameLayer.ResultUIManager:onGameStation(resp_json)
    self.GameScene:resetEmojiShowPositions()
end

return GameSceneModule

--endregion
