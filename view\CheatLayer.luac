--region *.lua
--Date
--此文件由[BabeLua]插件自动生成

local CheatLayer = class("CheatLayer",function() 
        return createCSB("game_res/"..GAME_ID.."/ui_csb/CheatLayer.csb", true) 
    end)


function CheatLayer:ctor(ui_root) 
    
    self.ui_root = ui_root
    local Button_Close = self:getChildByName("Button_Close")
    Button_Close:addClickEventListener( function()
        self:removeFromParent()
    end
    )
    local Button_Ok = self:getChildByName("Button_Ok")
    Button_Ok:addClickEventListener( function()
        if #self.selectedCardData ~= 13 then
            api_show_Msg_Tip("配牌数量不正确")
            return 
        end
        local request = {}
        request.cards = clone(self.selectedCardData)
        handleGameMessage.SendGameMessage(handleGameMessage.Table_GameMessage_ASSID.ASS_USER_CHEATER_REQ, request)
        self:removeFromParent()
    end
    )

    local Button_Cancel = self:getChildByName("Button_Cancel")
    Button_Cancel:addClickEventListener( function()
        self:removeFromParent()
    end
    )

    local Button_Reset = self:getChildByName("Button_Reset")
    Button_Reset:addClickEventListener( function()
        self.selectedCardData = {}
        self.Text_CardNums:setText(0)
        self.Panel_Selected:removeAllChildren()
    end
    )

    self.Panel_Card = self:getChildByName("Panel_Card")
    self.Panel_Selected = self:getChildByName("Panel_Selected")
    self.Text_CardNums = self:getChildByName("Text_CardNums")
    self.selectedCardData = {}
    self:InitCards()
end

function CheatLayer:InitCards()
    local st_x = 30
    local st_y = 320
    for cardval = 1,54 do
        local num = cardval % 13
        if num == 0 then
            num = 13
        end
        local huakind = (math.ceil(cardval / 13) -1)
        local path = "res/game_res/"..GAME_ID.."/ui_res/baipai/card/card_" ..huakind .. "_" .. num .. ".png"
        local card = ccui.Button:create(path)--ccui.ImageView:create(path)
        self.Panel_Card:addChild(card)
        card:setScale(0.4)
        card:addClickEventListener(function() 
            cclog("card:addClickEventListener")
            self:AddOneSelectedCard(cardval)
        end)
        local nodePoint = cc.p(st_x + num * 60, st_y  -huakind*70)--cc.p((num-3)*50,-huakind*70)
        local worldPoint = nodePoint--self.Panel_Card:convertToWorldSpace(nodePoint)
        card:setPosition(worldPoint)
        
    end
end

function CheatLayer:AddOneSelectedCard(cardVal)
    table.insert(self.selectedCardData, cardVal)
    self:UpdateSelectedCard()
end

function CheatLayer:UpdateSelectedCard()
    self.Panel_Selected:removeAllChildren()
    self.Text_CardNums:setText(#self.selectedCardData)
    for index,v in pairs(self.selectedCardData) do 
        local huakind = (math.ceil(v / 13) -1)
        local num = v % 13
        if num == 0 then
            num = 13
        end
        local path = "res/game_res/"..GAME_ID.."/ui_res/baipai/card/card_" ..huakind .. "_" .. num .. ".png"

        local card = ccui.Button:create(path)--ccui.ImageView:create(path)
        card:setScale(0.5)
        card:addClickEventListener(function() 
            self:RemoveOneSelectedCard(v)
        end)
        local nodePoint = cc.p((index)*50,50)
       -- local worldPoint = self.Panel_Selected:convertToWorldSpace(nodePoint)
        dump(worldPoint)
        card:setPosition(nodePoint)
        self.Panel_Selected:addChild(card)
    end
end

function CheatLayer:RemoveOneSelectedCard(val)
    for k,v in pairs(self.selectedCardData) do
        if v == val then
            table.remove(self.selectedCardData,k)
            break
        end
    end
    self:UpdateSelectedCard()
end

return CheatLayer

--endregion
