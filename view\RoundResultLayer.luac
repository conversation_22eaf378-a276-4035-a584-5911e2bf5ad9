--region *.lua
--Date
--此文件由[BabeLua]插件自动生成
local m_download = import("app.platform.common.download")
local RoundResultLayer = class("RoundResultLayer",function() return createCSB("ui_csb/ResultLayer.csb", true) end)
function RoundResultLayer:ctor(json)

   local Panel_Result = self:getChildByName("Panel_Result")
   self.thspCards = json.thspCards
   local Button_Ready = Panel_Result:getChildByName("Button_Ready")
   Button_Ready:addClickEventListener(function() 
        local request = { }
        self:removeFromParent()
        handleGameMessage.SendGameMessage(handleGameMessage.Table_GameMessage_ASSID.ASS_GM_AGREE_GAME, request)
        GameSceneModule:getInstance():getGameScene().GameLayer.ResultUIManager:setInit()
   end)

   local Button_XiPai = Panel_Result:getChildByName("Button_XiPai")
   Button_XiPai:addClickEventListener(function() 
        local request = {}
        request.bXiPaiReq = true
        handleGameMessage.SendGameMessage(handleGameMessage.Table_GameMessage_ASSID.ASS_PLAYER_DO_XIPAI, request)
        Button_XiPai:setVisible(false)
   end)
   if json.bCanXiPai == true and json.iFangKaNum ~= nil and json.iFangKaNum > 0 then
       Button_XiPai:setVisible(true)
       local iFangKaNum = json.iFangKaNum
       local AtlasLabel_1 = Button_XiPai:getChildByName("AtlasLabel_1")
       AtlasLabel_1:setString("/" .. iFangKaNum)
   else
       Button_XiPai:setVisible(false)
   end

   
   if json.paijuInfo then
       self.paijuInfo = json.paijuInfo
       Button_Ready:setVisible(false)
       local Button_ReviewGame = Panel_Result:getChildByName("Button_ReviewGame")
       Button_ReviewGame:setVisible(true)
       Button_ReviewGame:addClickEventListener(function() 
            GameSceneModule:getInstance():getGameScene().GameLayer:ShowPaijuLayer(self.paijuInfo )
            self:removeFromParent()
       end)
   end

   local  Text_warm =  Panel_Result:getChildByName("Text_warm")
   Text_warm:setString("")
   local gameSceneTmp =  GameSceneModule:getInstance():getGameScene()
   local PlayersInfos = GameSceneModule:getInstance():getGameScene().PlayersInfos
   if json.totalPlayerCount  then
       local playerList = gameSceneTmp:getSitDownPlayerMap()
       local tmpStr = ""
       if json.totalPlayerCount ~= #PlayersInfos  then
           tmpStr = string.format("错误:参与人数与结算人数不匹配！！！参与人数:%d, 结算人数:%d, ,在线人数:%d", json.totalPlayerCount, #PlayersInfos, #playerList)
           Text_warm:setString(tmpStr)
       else
         --  tmpStr = string.format("参与人数与结算人数匹配！！！ %d = %d ,在线人数:%d", json.totalPlayerCount, #PlayersInfos, #playerList)
          -- Text_warm:setString(tmpStr)

           if #PlayersInfos ~= #playerList  then
               -- gameSceneTmp:cacheUserInfo()
           end
       end
   end
   for i=1,#PlayersInfos do
       local userInfo = PlayersInfos[i]
       local Player = Panel_Result:getChildByName("Player_"..tostring(i))
       Player:setVisible(true)
       local PlayerNode = Player:getChildByName("Panel_Node")
       --玩家信息
       local Image_Head = PlayerNode:getChildByName("Image_Head")
       m_download:get_instance():set_head_image_and_auto_update( Image_Head ,userInfo.avatarUrl , userInfo.dwUserID, nil, userInfo.HeadID )
       local Text_NickName =  PlayerNode:getChildByName("Text_NickName")
       Text_NickName:setString(api_get_ascll_sub_str_by_ui(userInfo .nickName,10))

       --头道信息
       local Panel_TouD = PlayerNode:getChildByName("Panel_TouD")
       local Text_CardType_Tou = Panel_TouD:getChildByName("Text_CardType")
       Text_CardType_Tou:setText(CardTypeName[json.daoCardType[userInfo.bDeskStation+1][1]+1])
       for cardIndex = 1,3 do
           local card = Panel_TouD:getChildByName("Card_"..cardIndex)
           local Image_Card = card:getChildByName("Image_Card")
           if json.touData[userInfo.bDeskStation+1][cardIndex]<=0 then
               Image_Card:setVisible(false)
               Text_CardType_Tou:setVisible(false)
           else
           self:setOneCardValue(json.touData[userInfo.bDeskStation+1][cardIndex], Image_Card, userInfo.bDeskStation)
           end
       end

       --中道信息
       local Panel_ZhongD = PlayerNode:getChildByName("Panel_ZhongD")
       local Text_CardType_Zhong = Panel_ZhongD:getChildByName("Text_CardType")
       Text_CardType_Zhong:setText(CardTypeName[json.daoCardType[userInfo.bDeskStation+1][2]+1])
       for cardIndex = 1,5 do
           local card = Panel_ZhongD:getChildByName("Card_"..cardIndex)
           local Image_Card = card:getChildByName("Image_Card")
           if json.zhongData[userInfo.bDeskStation+1][cardIndex]<=0 then
               Image_Card:setVisible(false)
               Text_CardType_Zhong:setVisible(false)
           else
           self:setOneCardValue(json.zhongData[userInfo.bDeskStation+1][cardIndex], Image_Card, userInfo.bDeskStation)
            end
       end

       --尾道消息
       local Panel_WeiD = PlayerNode:getChildByName("Panel_WeiD")
       local Text_CardType_Wei = Panel_WeiD:getChildByName("Text_CardType")
       Text_CardType_Wei:setText(CardTypeName[json.daoCardType[userInfo.bDeskStation+1][3]+1])
       for cardIndex = 1,5 do
           local card = Panel_WeiD:getChildByName("Card_"..cardIndex)
           local Image_Card = card:getChildByName("Image_Card")
           if json.weiData[userInfo.bDeskStation+1][cardIndex]<=0 then
               Image_Card:setVisible(false)
               Text_CardType_Wei:setVisible(false)
           else
           self:setOneCardValue(json.weiData[userInfo.bDeskStation+1][cardIndex], Image_Card, userInfo.bDeskStation)
           end
       end

       local Text_SpecialCardType = PlayerNode:getChildByName("Text_SpecialCardType")
       if json.daoCardType[userInfo.bDeskStation+1][4] >0 then
            Text_SpecialCardType:setText(SpecCTName[json.daoCardType[userInfo.bDeskStation+1][4]])
            Text_SpecialCardType:setVisible(true)
            Text_CardType_Tou:setVisible(false)
            Text_CardType_Zhong:setVisible(false)
            Text_CardType_Wei:setVisible(false)
       else 
           Text_SpecialCardType:setVisible(false)
       end
       

       --总分
       local Text_Score = PlayerNode:getChildByName("Text_Score")
       Text_Score:setText("")--gameSceneTmp:getMoneyText(json.totalPoint[userInfo.bDeskStation+1]))

       local Text_Score_W = PlayerNode:getChildByName("Text_Score_W")
       local Text_Score_L = PlayerNode:getChildByName("Text_Score_L")
       Text_Score_W:setString("")
       Text_Score_L:setString("")
       if  json.totalPoint[userInfo.bDeskStation+1] == 0  then
           Text_Score_W:setString("0")
       elseif json.totalPoint[userInfo.bDeskStation+1] > 0  then
           Text_Score_W:setString("+" .. gameSceneTmp:getMoneyText(json.totalPoint[userInfo.bDeskStation+1]))
       else 
           Text_Score_L:setString(gameSceneTmp:getMoneyText(json.totalPoint[userInfo.bDeskStation+1]))
       end
       
       local Image_banker = PlayerNode:getChildByName("Image_banker")
       Image_banker:setVisible(false)
       if json.byNTUser ~= nil  and json.byNTUser >= 0  and json.byNTUser < 10 then
           if json.byNTUser == userInfo.bDeskStation then
               Image_banker:setVisible(true)
           end
       end      
   end

   --倒计时
   self.Text_CountDown = Panel_Result:getChildByName("Text_CountDown")
   self.CountDown = 20
   if json.AutoNextRound and json.paijuInfo==nil then
       if json.CountDown then
           self.CountDown = json.CountDown
       end
       self:StartCountDown()
   else
       self.Text_CountDown:setVisible(false)
   end
   self:registerScriptHandler(handler(self,self.onNodeEvent))
   self:showNoEnoungPlayers(json.NoEnougMoneyUserIdList)
end

function RoundResultLayer:onNodeEvent(event)
    if event == "exit" then
        self:StopCountDown()
    end
end

function RoundResultLayer:StopCountDown()
    if self.ScheduleID and self.ScheduleID ~= -1 then
        local scheduler = CCDirector:getInstance():getScheduler()
        scheduler:unscheduleScriptEntry(self.ScheduleID)
        self.ScheduleID = -1
    end
    self.Text_CountDown:setVisible(false)
end

function RoundResultLayer:StartCountDown()
    local scheduler = CCDirector:getInstance():getScheduler()
    self.Text_CountDown:setText("("..tostring(self.CountDown)..")")
    self.Text_CountDown:setVisible(true)
        
    self.ScheduleID = scheduler:scheduleScriptFunc(
        function ()
            self.CountDown = self.CountDown-1            
            if self.CountDown < 0 or tolua.isnull(self.Text_CountDown)  then
                scheduler:unscheduleScriptEntry(self.ScheduleID)
                self.ScheduleID = -1
                self.CountDown = 0
            else
                self.Text_CountDown:setText("("..tostring(self.CountDown)..")")
            end
        end,1,false)
end

-- 设置头中尾道中的一个牌值
function RoundResultLayer:setOneCardValue(value, card, deskstation)
    local Image_Kuang = card:getChildByName("Image_Kuang")
    local Image_MaTip = card:getChildByName("Image_MaTip")
    if ViewHelp.IsMapai(value) then
        Image_MaTip:setVisible(true)
        Image_Kuang:setVisible(true)
    else
        Image_MaTip:setVisible(false)
        Image_Kuang:setVisible(false)
    end

    --鬼牌癞子标识
    --[[
    local Image_Kuang = card:getChildByName("Image_Kuang")
    if ViewHelp.IsGuiCards(value) then
        Image_Kuang:setVisible(true)
    else
        Image_Kuang:setVisible(false)
    end
    --]]
    --同花順+癩子標識
    local Image_THSPTip = card:getChildByName("Image_THSPTip")
    local bthspCard = false
    if self.thspCards and self.thspCards[deskstation+1] and #self.thspCards[deskstation+1] then
        local cards = self.thspCards[deskstation+1]
        for _,v in pairs(cards) do
            if v == value then
                bthspCard = true
                break
            end
        end
    end
    Image_THSPTip:setVisible(bthspCard)


    local num = value % 13
    if num == 0 then
        num = 13
    end

    local path = "res/game_res/"..GAME_ID.."/ui_res/baipai/card/card_" ..(math.ceil(value / 13) -1) .. "_" .. num .. ".png"
    card:loadTexture(path)
end


function RoundResultLayer:showNoEnoungPlayers(playerIdList)
    print("GameResultLayer:showNoEnoungPlayers")
    if playerIdList == nil then
        print("GameResultLayer:showNoEnoungPlayers-------------nil")
        return
    end

    if g_server_obj:get_server_type() == "video" then
        print("GameResultLayer:showNoEnoungPlayers-------------vedio")
        return 
    end
    local tipStr = "玩家【"
    for i=1, #playerIdList do
        playInfo = GameSceneModule:getInstance():getGameScene():getPlayerInfoByUserID(playerIdList[i])
        if playerIdList ~= nil then
            tipStr = tostring(tipStr .."(" ..  playInfo.nickName .. ")  ")
        end
    end
    tipStr = tostring(tipStr .. "】由于积分不足， 房间已强制解散！！！")
    api_show_Msg_Box(tipStr,function()
            
    end)
end

return RoundResultLayer
--endregion
