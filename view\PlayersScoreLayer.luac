--region *.lua
--Date
--牌局信息层
--此文件由[BabeLua]插件自动生成

--import("..GameDefine")
local GameMusic = import("..GameMusic")
local m_download = import("....common.download")

local PlayersScoreLayer = class("PlayersScoreLayer",function() 
    return createCSB("game_res/"..GAME_ID.."/ui_csb/PlayersScoreLayer.csb", true) 
    end)

function PlayersScoreLayer:ctor(parent,PaijuInfo)
    print("PlayersScoreLayer:ctor")
    self.parent = parent
    local Image_zhezhao = self:getChildByName("Image_zhezhao")

    -- 设置事件吞噬
    local function onTouchBegan(touch,event)
        return true
    end
    local listener = cc.EventListenerTouchOneByOne:create()
    listener:setSwallowTouches(true)
    listener:registerScriptHandler(onTouchBegan,cc.Handler.EVENT_TOUCH_BEGAN)
    local eventDispatcher = cc.Director:getInstance():getEventDispatcher()
    eventDispatcher:addEventListenerWithSceneGraphPriority(listener, Image_zhezhao)

    local button_back = self:getChildByName("Button_back")
    local button_share = self:getChildByName("Button_share")
    local Button_again = self:getChildByName("Button_again")
    if g_config.has_share then
        button_share:setVisible(true)
    else
        button_share:setVisible(false)
        button_back:setPositionX(640)
    end    
    
    button_share:addClickEventListener(function() 
        print("share button touched")
        GameMusic:playClickEffect()
        print("share")
        cc.utils:captureScreen(function(succeed, outputFile)
            if succeed then
               GameSceneModule:getInstance():getGameScene():reqShareImageToWXSession(outputFile)
            end
        end, 
        "kwx_share.jpg")
        print("share button touched")
    end)

    button_back:addClickEventListener(function ()
        GameMusic:playClickEffect()
        GameSceneModule:getInstance():getGameScene():Exit_game()
    end)

    Button_again:addClickEventListener(function ()
        print("Button_again event")
        local cur_scene = GameSceneModule:getInstance():getGameScene()
        if cur_scene.ClubMoreGame  then
            print("cur_scene:ClubMoreGame  --" .. cur_scene:getDeskInfo().roomKey)
            cur_scene:ClubMoreGame(cur_scene:getDeskInfo().roomKey)
        end
        GameSceneModule:getInstance():getGameScene():Exit_game()
    end)
    Button_again:setVisible(false)

    -- 游戏人数
    self.playerNums = #GameSceneModule:getInstance():getGameScene().PlayersInfos
    
    self.PlayersScoreInfo = {}
    for i = 1,PLAYER_COUNT do
        self.PlayersScoreInfo[i] = self:getChildByName("Player_" .. i)
        --头像
        self.PlayersScoreInfo[i].Image_Head = self.PlayersScoreInfo[i]:getChildByName("Image_Head")
--        --房主
--        self.PlayersScoreInfo[i].Image_RoomMaster = self.PlayersScoreInfo[i].Image_Head:getChildByName("Image_RoomMaster")
--        self.PlayersScoreInfo[i].Image_RoomMaster:setVisible(false)
       
        --昵称
        self.PlayersScoreInfo[i].Text_NickName = self.PlayersScoreInfo[i]:getChildByName("Text_NickName")
        --ID
        self.PlayersScoreInfo[i].Text_ID = self.PlayersScoreInfo[i]:getChildByName("Text_ID")

        --打枪次数
        self.PlayersScoreInfo[i].Text_dq_value = self.PlayersScoreInfo[i]:getChildByName("Text_dq_value")
        --全垒打次数
        self.PlayersScoreInfo[i].Text_qld_value = self.PlayersScoreInfo[i]:getChildByName("Text_qld_value")
        --特殊牌型次数
        self.PlayersScoreInfo[i].Text_specCt_value = self.PlayersScoreInfo[i]:getChildByName("Text_specCt_value")
--        --参与局数次数
--        self.PlayersScoreInfo[i].Text_js = self.PlayersScoreInfo[i]:getChildByName("Text_js")
        
        --总分数
        self.PlayersScoreInfo[i].Text_TotalPoint_value = self.PlayersScoreInfo[i]:getChildByName("Text_TotalPoint_value")

        self.PlayersScoreInfo[i].Text_TotalPoint_value_W = self.PlayersScoreInfo[i]:getChildByName("Text_TotalPoint_value_W")

        self.PlayersScoreInfo[i].Text_TotalPoint_value_L = self.PlayersScoreInfo[i]:getChildByName("Text_TotalPoint_value_L")

        self.PlayersScoreInfo[i].Text_TotalPoint_value_W:setString("")
        self.PlayersScoreInfo[i].Text_TotalPoint_value_L:setString("")

        if i > self.playerNums then
            self.PlayersScoreInfo[i]:setVisible(false)
        end

    end

--    --调整每个玩家的位置
--    if self.playerNums == 2 then
--        self.PlayersScoreInfo[1]:setPositionX(265)
--        self.PlayersScoreInfo[2]:setPositionX(560)
--    elseif self.playerNums == 3 then
--        self.PlayersScoreInfo[1]:setPositionX(135)
--        self.PlayersScoreInfo[2]:setPositionX(400)
--        self.PlayersScoreInfo[3]:setPositionX(695)
--    end

    self:updateUserInfo()
    self:updatePlayersScoreInfo(PaijuInfo)

    --时间
    self.Text_time = self:getChildByName("Text_time")
    --日期
    self.Text_date = self:getChildByName("Text_date")
    self:updateDateAndTime()
    --房号和桌号
    self.Text_roomnum = self:getChildByName("Text_roomnum")
    if not GameSceneModule:getInstance():getGameScene():IsGoldRoom() then
        local desk_config =  ViewHelp.getDeskConfig()
        self.Text_roomnum:setText("房号：".. tostring(desk_config.roomKey))
    else
        self.Text_roomnum:setVisible(false)
    end

    self:registerScriptHandler(handler(self,self.onNodeEvent))
end

function PlayersScoreLayer:onNodeEvent(event)

    if event == "enter" then
        --g_pushBombBox(self)
    elseif event == "exit" then
        --g_popBombBox(self)
    end
end

--更新玩家信息
function PlayersScoreLayer:updateUserInfo()
    print("PlayersScoreLayer:updateUserInfo 11")
    for i = 0, self.playerNums - 1 do
        print("PlayersScoreLayer:updateUserInfo i= " .. i)

        -- 这个时候获取 可能玩家已经被踢出桌子 因为桌子已经解散  需要从缓存中读取玩家信息
        local player_info = GameSceneModule:getInstance():getGameScene().PlayersInfos[i + 1]

        -- 昵称
        self.PlayersScoreInfo[i + 1].Text_NickName:setString(api_get_ascll_sub_str_by_ui(player_info.nickName, 8))

        -- ID
        self.PlayersScoreInfo[i + 1].Text_ID:setString(player_info.dwUserID)

        -- 更新玩家头像
        m_download:get_instance():set_head_image_and_auto_update(
        self.PlayersScoreInfo[i + 1].Image_Head,
        player_info.avatarUrl,
        player_info.dwUserID,
        nil,
        player_info.HeadID)
--        -- 房主
--        if not GameSceneModule:getInstance():getGameScene():IsGoldRoom() then
--            local desk_config = ViewHelp.getDeskConfig()
--            if player_info.dwUserID == desk_config.mastUserID then
--                self.PlayersScoreInfo[i + 1].Image_RoomMaster:setVisible(true)
--            end
--        end
        

    end
end

--更新玩家牌局信息
function PlayersScoreLayer:updatePlayersScoreInfo(PaijuInfo)
    local gameSceneTmp =  GameSceneModule:getInstance():getGameScene()
    local player_info = GameSceneModule:getInstance():getGameScene().PlayersInfos
    for i = 1 , #player_info do 
        local index = player_info[i].bDeskStation + 1
        self.PlayersScoreInfo[i].Text_dq_value:setText(PaijuInfo.DaQiangTimes[index])
        self.PlayersScoreInfo[i].Text_qld_value:setText(PaijuInfo.QuanLeiDaTimes[index])
        self.PlayersScoreInfo[i].Text_specCt_value:setText(PaijuInfo.PaiXingTimes[index])
        --self.PlayersScoreInfo[i].Text_js:setText("参与局数：" .. PaijuInfo.JuShuTimes[index])
        self.PlayersScoreInfo[i].Text_TotalPoint_value:setText("")--gameSceneTmp:getMoneyText( PaijuInfo.TotalScore[index]))

        if PaijuInfo.TotalScore[index] == 0  then
            self.PlayersScoreInfo[i].Text_TotalPoint_value_W:setText("0")
        elseif PaijuInfo.TotalScore[index] > 0  then
            self.PlayersScoreInfo[i].Text_TotalPoint_value_W:setText("+" .. gameSceneTmp:getMoneyText( PaijuInfo.TotalScore[index]))
        else
            self.PlayersScoreInfo[i].Text_TotalPoint_value_L:setText(gameSceneTmp:getMoneyText( PaijuInfo.TotalScore[index]))
        end
    end
end

function PlayersScoreLayer:updateDateAndTime()
    --更新日期
    self.Text_date:setText(os.date("%Y-%m-%d", os.time()))

    --更新时间
    if self.schedulerID ~= nil then    
        cc.Director:getInstance():getScheduler():unscheduleScriptEntry( self.schedulerID )
        self.schedulerID = nil
    end
    self:update()
    self.schedulerID = cc.Director:getInstance():getScheduler():scheduleScriptFunc( handler(self , self.update ) , 1.0 ,false)

end

function PlayersScoreLayer:update()
    if self.Text_time ~= nil then
        self.Text_time:setText(os.date("%H:%M", os.time()))
    else
        --print("Text_time is nil ")
    end
end



return PlayersScoreLayer


--endregio
