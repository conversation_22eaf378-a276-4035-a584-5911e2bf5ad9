--region *.lua
--Date
--此文件由[BabeLua]插件自动生成

--import("..GameDefine")
local GameMusic = import("..GameMusic")
local SettingLayer = import(".SettingLayer")
local PreStartLayer = import(".PreStartLayer")
local BaseUIManager = class("BaseUIManager")
local GameLogic = import('..GameLogic')
local CheatLayer = import(".CheatLayer")


function BaseUIManager:ctor(ui_project, ui_root)
    self.ui_project = ui_project
    self.ui_root = ui_root

    local desk_config =  GameSceneModule:getInstance():getGameScene():getDeskInfo()

    --房号
    self.Text_roomkey = self.ui_project:getChildByName("Text_roomkey")
    self.Text_roomkey:setPositionX(self.Text_roomkey:getPositionX() - self.ui_project:getPositionX())
    
    --局数
    self.Text_round = self.ui_project:getChildByName("Text_round")
    self.Text_round:setPositionX(self.Text_round:getPositionX() - self.ui_project:getPositionX())

    local Text_Mode = self.ui_project:getChildByName("Text_Mode")
    self.Panel_Mapai = self.ui_project:getChildByName("Panel_Mapai")
    self.Panel_Mapai:setPositionX(self.Panel_Mapai:getPositionX() - self.ui_project:getPositionX())
    self.Panel_Mapai.Mapai = {}
    for mapaiIndex = 1, 3 do 
        self.Panel_Mapai.Mapai[mapaiIndex] = self.Panel_Mapai:getChildByName("Image_Mapai_"..mapaiIndex)
    end

    self.Panel_Laizi = self.ui_project:getChildByName("Panel_Laizi")
    self.Panel_Laizi.Laizi = {}
    for laiziIndex = 1, 4 do
        self.Panel_Laizi.Laizi[laiziIndex] = self.Panel_Laizi:getChildByName("Image_Laizi_"..laiziIndex)
        self.Panel_Laizi.Laizi[laiziIndex]:setVisible(false)
    end
    self.Panel_Laizi:setVisible(false)

    local CardNameTab = {"card_R.png","card_G.png","card_B.png"}
    local path = "res/game_res/"..GAME_ID.."/ui_res/baipai/card/"..CardNameTab[UserData:getCardColor()]

     if GameSceneModule:getInstance():getGameScene():getGameType() == "qz1" then
        if desk_config ~= nil  then
            self.Text_roomkey:setText("房号：".. tostring(desk_config.roomKey))
            local RoomConfig = ""
            if desk_config.desk_select_config.PlayerCount then
                RoomConfig = RoomConfig .. tostring(desk_config.desk_select_config.PlayerCount.desc).." "
            end

            if desk_config.desk_select_config.pay_mode then
                RoomConfig = RoomConfig .. tostring(desk_config.desk_select_config.pay_mode.desc).." "
            end

            if desk_config.desk_select_config.DaiGui then
                RoomConfig = RoomConfig .. tostring(desk_config.desk_select_config.DaiGui.desc).."鬼牌 "
            end


            if desk_config.desk_select_config.GamePlayingMode then
                RoomConfig = RoomConfig .. tostring(desk_config.desk_select_config.GamePlayingMode.desc).." "
            end

            if desk_config.desk_select_config.Chongpai then
                RoomConfig = RoomConfig .. tostring(desk_config.desk_select_config.Chongpai.desc).." "
            end

            if desk_config.desk_select_config.XiPai then
                RoomConfig = RoomConfig .. tostring(desk_config.desk_select_config.XiPai.desc).." "
            end

            if desk_config.desk_select_config.TuoGuan then
                RoomConfig = RoomConfig .. tostring(desk_config.desk_select_config.TuoGuan.desc).." "
            end

            if desk_config.desk_select_config.BankMode then
                RoomConfig = RoomConfig .. tostring(desk_config.desk_select_config.BankMode.desc).." "
            end

            if desk_config.desk_select_config.ColorMode then
                RoomConfig = RoomConfig .. tostring(desk_config.desk_select_config.ColorMode.desc).." "
            end

            if desk_config.desk_select_config.A_5ShunziMode then
                RoomConfig = RoomConfig .. tostring(desk_config.desk_select_config.A_5ShunziMode.desc).." "
            end

            if desk_config.desk_select_config.MaCardMode then
                RoomConfig = RoomConfig .. tostring(desk_config.desk_select_config.MaCardMode.desc).." "
            end

            if desk_config.desk_select_config.Rule then
                for _,item in pairs(desk_config.desk_select_config.Rule) do
                    RoomConfig = RoomConfig..tostring(item.desc).." "
                end
            end

            if desk_config.desk_select_config.AddExtraPoint then
                for _,item in pairs(desk_config.desk_select_config.AddExtraPoint) do
                    RoomConfig = RoomConfig..tostring(item.desc).." "
                end
            end

            if desk_config.desk_select_config.SpecialCardType then
                for _,item in pairs(desk_config.desk_select_config.SpecialCardType) do
                    RoomConfig = RoomConfig..tostring(item.desc).." "
                end
            end

            self.RoomConfig = RoomConfig
            Text_Mode:setText(RoomConfig)
            
        end

         ui_root.niaoNum = desk_config.desk_select_config.DaiGui.guiNums
     end


    --设置
    local Button_set = ui_project:getChildByName("Button_set")
    Button_set:addClickEventListener( function()
        ui_root:addChild(SettingLayer:create(self.ui_root))
    end
    )
    Button_set:setPositionX(Button_set:getPositionX() + self.ui_project:getPositionX())

    --配牌器
    self.Button_Cheat = ui_project:getChildByName("Button_Cheat")
    self.Button_Cheat:addClickEventListener( function()
        local cheatLayer = CheatLayer:create(self.ui_root)
        ui_root:addChild(cheatLayer)
        cheatLayer:setZOrder(2001)
    end
    )
    self.Button_Cheat:setVisible(false)
    self.Button_Cheat:setPositionX(self.Button_Cheat:getPositionX() - self.ui_project:getPositionX())

    -- 聊天按钮
    local Button_chat = ui_project:getChildByName("Button_chat")
    Button_chat:addClickEventListener( function()
        if ViewHelp.getSelfInfo().look == true  then
            return
        end
        GameSceneModule:getInstance():getGameScene():showChat()
    end
    )
    Button_chat:setPositionX(Button_chat:getPositionX() + self.ui_project:getPositionX())
    self.Button_chat = Button_chat
--    --开始
--    local Button_start = ui_project:getChildByName("Button_start")
--    self.Button_start = Button_start
--    --Button_start:setVisible(false)
--    Button_start:addClickEventListener(function()
--        GameMusic:playClickEffect()
--        self:startCallBack()
--    end
--    )

    --提前开始按钮
    self.Bytton_prestart = ui_project:getChildByName("Button_prestart")
    self.Bytton_prestart:addClickEventListener(function()
        GameMusic:playClickEffect()
        self:prestartCallBack()
    end
    )
    

    --返回

    local Button_back = self.ui_project:getChildByName("Button_back")
    self.Button_back = Button_back
    
    Button_back:addClickEventListener( function()
        GameMusic:playClickEffect()
        local curGameStation = ViewHelp.getGameStation();
        print("curGameStation    =    " .. curGameStation)
        if GameSceneModule:getInstance():getGameScene():IsGoldRoom() then
            local curGameStation = ViewHelp.getGameStation()
            if curGameStation <= GameStation.GS_WAIT_ARGEE or curGameStation == GameStation.GS_WAIT_NEXT_ROUND then
                return GameSceneModule:getInstance():getGameScene():Exit_game()  
            else
                return api_show_Msg_Box("游戏进行中，无法离开，请等待本局游戏结束")
            end
        end
        if ViewHelp.getGameStation() <= GameStation.GS_WAIT_ARGEE then
            -- 游戏未开始 返回到大厅
            if ViewHelp.getRoomMasterUserID() == ViewHelp.getSelfUserID() then
                local request = {}
                handleGameMessage.SendGameMessage(handleGameMessage.Table_GameMessage_ASSID.ASS_QUIT_REQ,request)
                GameSceneModule:getInstance():getGameScene():Exit_game()  
            else
                local request = {}
                handleGameMessage.SendGameMessage(handleGameMessage.Table_GameMessage_ASSID.ASS_QUIT_REQ,request)
                GameSceneModule:getInstance():getGameScene():reqLeaveDesk()
                GameSceneModule:getInstance():getGameScene():Exit_game()
            end
        else
            -- 游戏已经开始 弹出设置界面 提示解散或者离开桌子
            ui_root:addChild(SettingLayer:create(self.ui_root))
        end

    end
    )

     -- 语音(按住说话)
    local Button_voice = ui_project:getChildByName("Button_voice")
    Button_voice:addClickEventListener( function()
        print("click Button_voice button")
    end
    )
    Button_voice:setPositionX(Button_voice:getPositionX() + self.ui_project:getPositionX())
    Button_voice:setVisible(false)
    if not GameSceneModule:getInstance():getGameScene():IsGoldRoom()  then
        local m_QuickVoiceLayer = import("app/platform/common/QuickVoiceLayer")
        local scene = GameSceneModule:getInstance():getGameScene()
        QuickVoiceLayer = m_QuickVoiceLayer:create(scene:getSelfInfo().dwUserID, handler(scene, scene.reqRecordSound))
        QuickVoiceLayer:set_voice_image("game_res/" .. GAME_ID .. "/ui_res/base/yuyin.png")
        QuickVoiceLayer:set_voice_postion(Button_voice:getPosition())
        QuickVoiceLayer:setLocalZOrder(1)
        ui_project:addChild(QuickVoiceLayer)
        self.QuickVoiceLayer = QuickVoiceLayer
    end
         -- 房主解散房间
    local Button_dismiss_desk = ui_project:getChildByName("Button_dismiss_desk")
    self.Button_dismiss_desk = Button_dismiss_desk
    self.bCreateMore = false
    if ViewHelp.getRoomMasterUserID() == ViewHelp.getSelfUserID() then
        -- 房主
        Button_dismiss_desk:addClickEventListener( function()
            if self.bCreateMore == true  then
                local request = {}
                handleGameMessage.SendGameMessage(handleGameMessage.Table_GameMessage_ASSID.ASS_QUIT_REQ,request)
                GameSceneModule:getInstance():getGameScene():reqLeaveDesk()
            end
            print("房主解散房间")
            api_show_Msg_Tip(
            "是否立刻解散房间",
            function()
                -- 同意解散房间
                GameSceneModule:getInstance():getGameScene():reqDissmisDesk(0)
            end ,
            function()

            end
            , true)

        end )

    else
        -- 闲家
        Button_dismiss_desk:loadTextures("res/game_res/" .. GAME_ID .. "/ui_res/base/tuichu.png", "res/game_res/" .. GAME_ID .. "/ui_res/base/tuichu.png", "")
        Button_dismiss_desk:addClickEventListener( function()
            print("闲家申请离开房间")
            local request = {}
            handleGameMessage.SendGameMessage(handleGameMessage.Table_GameMessage_ASSID.ASS_QUIT_REQ,request)
            GameSceneModule:getInstance():getGameScene():reqLeaveDesk()
            GameSceneModule:getInstance():getGameScene():Exit_game() 

        end )
    end

    self.Button_start = ui_project:getChildByName("Button_start")
    
    self.Button_start:setVisible(false)

    
    self.Button_start:addClickEventListener(function()
        local request = { }
        self.Button_start:setVisible(false)
        handleGameMessage.SendGameMessage(handleGameMessage.Table_GameMessage_ASSID.ASS_GM_AGREE_GAME, request)
        GameSceneModule:getInstance():getGameScene().GameLayer.ResultUIManager:setInit()
    end)

    --换桌功能
--    self.Button_ChangeDesk = ui_project:getChildByName("Button_ChangeDesk")
--    self.Button_ChangeDesk:setVisible(false)
--    self.Button_ChangeDesk:addClickEventListener(function()
--        api_show_loading("正在换桌,请稍后...")
--        GameSceneModule:getInstance():getGameScene():ChangeDesk()
--    end)

    -- 邀请微信好友
    local Button_invite_wx = ui_project:getChildByName("Button_invite_wx")
    self.Button_invite_wx = Button_invite_wx


    self.Button_invite_wx:setVisible(false)
    
    Button_invite_wx:addClickEventListener( function()
        print("invite weixin friend")
        GameMusic:playClickEffect()
        local titleStr = "十三水"
--        if ConfigData and ConfigData.title then
--            titleStr = ConfigData.title
--        end
        GameSceneModule:getInstance():getGameScene():reqShareToWXSession(titleStr, "房号: " .. desk_config.roomKey .. "，玩法: " .. self.RoomConfig .."。快来和我一起玩吧!")
    end
    )

    
    local Button_CopyRoomkey = ui_project:getChildByName("Button_CopyRoomkey")
    self.Button_CopyRoomkey = Button_CopyRoomkey
    self.Button_CopyRoomkey:addClickEventListener(function()
        GameMusic:playClickEffect()
        local copyText = "十三水 ".."房号: " .. desk_config.roomKey .. "，玩法: " .. self.RoomConfig .."。快来和我一起玩吧!"
        cclog(tostring(copyText))
        lua_to_plat:copy_text(copyText)
        api_show_tips("复制房间号成功，请在聊天中黏贴")
     end
        )
     -- 临时设置 复制房间号按钮 为 邀请好友按钮的位置
     self.Button_CopyRoomkey:setPosition(self.Button_invite_wx:getPositionX(), self.Button_invite_wx:getPositionY())

    if g_config.has_share then
        Button_invite_wx:setVisible(true)
        self.Button_CopyRoomkey:setVisible(true)
    else
        Button_invite_wx:setVisible(false)
        self.Button_CopyRoomkey:setVisible(false)
        Button_dismiss_desk:setPositionX(640)
    end
    -- 时间
    self.Text_time = ui_project:getChildByName("Text_time")
    
    self:startCountDown()
    self.Text_time:registerScriptHandler(handler(self,self.onNodeEvent))

    self.Text_player_xi_tip = ui_project:getChildByName("Text_player_xi_tip")
    self.Text_player_xi_tip:setString("")

    self.Button_sit = ui_project:getChildByName("Button_sit")
    self.Button_sit:addClickEventListener(function()
        if GameSceneModule:getInstance():getGameScene().reqSitdownDesk  then
            GameSceneModule:getInstance():getGameScene().bLookerSitReq = true
            GameSceneModule:getInstance():getGameScene():reqSitdownDesk( 0 , true )
        end
    end)
    self.Image_visit = ui_project:getChildByName("Image_visit")

    --金币场
    if GameSceneModule:getInstance():getGameScene():IsGoldRoom() then
        self.Text_roomkey:setVisible(false)
        self.Text_round:setVisible(false)
         Button_invite_wx:setVisible(false)
         self.Button_CopyRoomkey:setVisible(false)
         Button_dismiss_desk:setVisible(false)
         Button_chat:setVisible(false)
         self.Bytton_prestart:setVisible(false)
    end


    --self:SetConfigPos()
    self.Button_invite_wx:setVisible(false)
    self.Button_CopyRoomkey:setVisible(false)
    self.Button_dismiss_desk:setVisible(false)
    if g_server_obj:get_server_type() == "video" then
        self:SetMapai(0)
    end

    

end

function BaseUIManager:updateLookerButtonState()
    self.Button_sit:setVisible(false)
    self.Image_visit:setVisible(false)
    

    if ViewHelp.getSelfInfo().look == true  then
        self.Button_sit:setVisible(true)
        self.Image_visit:setVisible(true)
        if self.Button_start:isVisible() == false then
             self.Button_sit:setVisible(false)
        end
        self.Button_dismiss_desk:setVisible(false)
        self.Button_start:setVisible(false)
        self.Bytton_prestart:setVisible(false)
    else

    end
end

function BaseUIManager:SetConfigPos()
    if ConfigData then
        if ConfigData.roomKeyTxtPos then
           self.Text_roomkey:setPosition(cc.p(ConfigData.roomKeyTxtPos.x, ConfigData.roomKeyTxtPos.y))
        end
        if ConfigData.exitBtnPos then
            self.Button_back:setPosition(cc.p(ConfigData.exitBtnPos.x, ConfigData.exitBtnPos.y))
        end
        if ConfigData.roundTxtPos then
           self.Text_round:setPosition(cc.p(ConfigData.roundTxtPos.x, ConfigData.roundTxtPos.y))
        end
        if ConfigData.timeTxtPos then
           self.Text_time:setPosition(cc.p(ConfigData.timeTxtPos.x, ConfigData.timeTxtPos.y))
        end

        
    end
end

function BaseUIManager:onNodeEvent(event)
    if event == "enter" then
        print("--- BaseUIManager:onNodeEvent:enter")
    elseif event == "exit" then
        print("--- BaseUIManager:onNodeEvent:exit")
        self:stopCountDown()
    end
end

-- 刷新时间
function BaseUIManager:startCountDown()
    self:update()
    self.schedulerID = cc.Director:getInstance():getScheduler():scheduleScriptFunc( handler(self , self.update ) , 1 ,false)
end

function BaseUIManager:update()
    if self.Text_time ~= nil then
        self.Text_time:setText(os.date("%H:%M", os.time()))
    end
end

-- 停止刷新
function BaseUIManager:stopCountDown()
    if self.schedulerID ~= nil then    
        cc.Director:getInstance():getScheduler():unscheduleScriptEntry( self.schedulerID )
        self.schedulerID = nil
    end
end
function BaseUIManager:onCatchCard(resp_json)
    --self:SetLaizi()
    --隐藏洗牌动画
    if self.AniNodeXipai ~= nil then 
        self.AniNodeXipai:removeFromParent()
        self.AniNodeXipai = nil
    end
    self.Text_player_xi_tip:setString("") 
end

function BaseUIManager:SetLaizi()
    cclog("BaseUIManager:SetLaizi")
    local laizis = ViewHelp.GetGuiCards()
    dump(laizis)
    if laizis== nil or #laizis<=0 then
        self.Panel_Laizi:setVisible(false)
        return 
    else
        self.Panel_Laizi:setVisible(true)
    end
    for i = 1,4 do 
        if laizis[i] then
            local num = laizis[i] % 13
            if num == 0 then
                num = 13
            end
            local path = "res/game_res/"..GAME_ID.."/ui_res/baipai/deskCard/card_" ..(math.ceil(laizis[i] / 13) -1) .. "_" .. num .. ".png"
            self.Panel_Laizi.Laizi[i]:loadTexture(path)
            self.Panel_Laizi.Laizi[i]:setVisible(true)
        else
            self.Panel_Laizi.Laizi[i]:setVisible(false)
        end
    end

end

function BaseUIManager:SetMapai(mapaiNum)
    if mapaiNum <= 0 then
        self.Panel_Mapai:setVisible(false)
        return 
    end

    local maCardList = ViewHelp.GetMapaiTab()
    dump(maCardList)
    self.Panel_Mapai:setVisible(true)
    for i = 1, 3 do 
        if i <= mapaiNum then
            self.Panel_Mapai.Mapai[i]:setVisible(true)
            local MaPaiVal = maCardList[i]
            local num = MaPaiVal % 13
            if num == 0 then
                num = 13
            end
            local cardPath = "ui_res/baipai/deskCard/card_" ..(math.ceil(MaPaiVal / 13) -1) .. "_" .. num .. ".png"
            self.Panel_Mapai.Mapai[i]:loadTexture(cardPath)
        else
            self.Panel_Mapai.Mapai[i]:setVisible(false)
        end
    end
--    local num = MaPaiVal % 13
--    if num == 0 then
--        num = 13
--    end
--    local path = "res/game_res/"..GAME_ID.."/ui_res/baipai/deskCard/card_" ..(math.ceil(MaPaiVal / 13) -1) .. "_" .. num .. ".png"
--    local MapaiStr = "马牌:"
--    self.Text_Mapai:setText(MapaiStr)
--    self.Text_Mapai:setVisible(true)
end

-- 游戏开始
function BaseUIManager:onGameStart(resp_json)
    print("-------------- BaseUIManager:onGameStart")
    if self.PreStartLayer then
        self.PreStartLayer:removeFromParent()
        self.PreStartLayer = nil
    end
    GameMusic:playStartEffect()

    self.Button_invite_wx:setVisible(false)
    self.Button_CopyRoomkey:setVisible(false)
    self.Button_dismiss_desk:setVisible(false)
    self.Button_start:setVisible(false)
    self.Bytton_prestart:setVisible(false)
    self.Button_sit:setVisible(false)
    --self.Button_ChangeDesk:setVisible(false)
    self:updateRound(resp_json.RoundTimes, resp_json.AllRoundTimes)

    if resp_json.mapaiNums   then
        ViewHelp.SetMapaiNums(resp_json.mapaiNums)
    end

    --播放洗牌动画
    if resp_json.XiPai then
        self:ShowXipai(resp_json.iXiPaiUserId)
    end



end

-- 房主开始游戏
function BaseUIManager:startCallBack()
    print("--- BaseUIManager:startCallBack")
--    if ViewHelp.getGameStation() == GameStation.GS_WAIT_SETGAME then
--        if #GameSceneModule:getInstance():getGameScene().PlayersInfos >= 2 then
--            local request = { }
--            handleGameMessage.SendGameMessage(handleGameMessage.Table_GameMessage_ASSID.ASS_GAME_AUTO_BEGIN, request)
--        else
--            api_show_tips("玩家人数不足")
--        end

--    end

    local request = { }
    handleGameMessage.SendGameMessage(handleGameMessage.Table_GameMessage_ASSID.ASS_GM_AGREE_GAME, request)
    self.Button_start:setVisible(false)
    self.Bytton_prestart:setPositionX(640)
    --self.Bytton_prestart:setVisible(false)
end

--请求提前开局
function BaseUIManager:prestartCallBack()
    local SitDownPlayerMap = GameSceneModule:getInstance():getGameScene():getSitDownPlayerMap()
    if #SitDownPlayerMap >= 1 then
         if ViewHelp.getRoomMasterUserID() == ViewHelp.getSelfUserID() then
                -- 房主
                api_show_Msg_Tip(
                    "确认马上开始游戏",
                    function()
                        self:request_prestart(0)
                    end ,
                    function()

                    end
                , true)
        end
    else
        api_show_tips("需要两人以上才能开始游戏！")
    end
end


-- 更新局数
function BaseUIManager:updateRound(curRound,AllRound)
    self.Text_round:setText("局数：".. curRound .. "/" .. AllRound)
end

--断线重连处理
function BaseUIManager:onGameStation(resp_json)
    print("--- BaseUIManager:onGameStation start GameStation = "..tostring(resp_json.GSID))
    self:updateRound(resp_json.iRoundTimes, resp_json.iAllRoundTimes)
    
    if resp_json.CheatOpen and resp_json.CheatOpen==1 then
        self.Button_Cheat:setVisible(true)
    end

    if resp_json.bCreateMore ~= nil then
        self.bCreateMore = resp_json.bCreateMore
    end

    if self.Button_chat and resp_json.bVisibleChat ~= nil  then
        self.Button_chat:setVisible(resp_json.bVisibleChat)
    end

    if self.QuickVoiceLayer and resp_json.bVisibleVoice ~= nil  then
        --self.QuickVoiceLayer:setVisible(resp_json.bVisibleVoice)
        if resp_json.bVisibleVoice == true  then
            self.QuickVoiceLayer:setVisible(true)
            if ViewHelp.getSelfInfo().look == true  then
                self.QuickVoiceLayer:setVisible(false)
            end
        else
            self.QuickVoiceLayer:removeFromParent()
            self.QuickVoiceLayer = nil
        end
    end

    ViewHelp.SetMapaiNums(resp_json.mapaiNums)
    self:SetMapai(resp_json.mapaiNums)
    ViewHelp.SetbTHSP(resp_json.bTHSP)
    if resp_json.GSID == GameStation.GS_WAIT_SETGAME then
        self.Button_start:setVisible(true)
        self.Button_dismiss_desk:setVisible(true)
        self:ScanWaigua()
        if g_config.has_share then
            self.Button_invite_wx:setVisible(false)
            --self.Button_CopyRoomkey:setVisible(true)
        else
            self.Button_invite_wx:setVisible(false)
            self.Button_CopyRoomkey:setVisible(false)
            self.Button_dismiss_desk:setPositionX(640)
        end
        local playerInfos = ViewHelp.getDeskPlayerCacheData()
        for k,v in pairs(playerInfos) do
            if resp_json.IsAgree[resp_json.chair + 1] == true then
                self.Button_start:setVisible(false)
                
            end
        end
        if resp_json.bQueryingPrestart == true then
            self.Bytton_prestart:setVisible(false)
            local SitDownPlayerMap = GameSceneModule:getInstance():getGameScene():getSitDownPlayerMap()
            self.PreStartLayer = PreStartLayer:create(  resp_json ,SitDownPlayerMap,  ViewHelp:getSelfInfo().bDeskStation ,      
                    function()
                        --同意提前开局
                        self:request_prestart(1)
                    end , function()
                        --不同意提前开局
                        self:request_prestart(2)
                    end)
            self.ui_root:addChild(self.PreStartLayer)
        end
    elseif resp_json.GSID == GameStation.GS_ROBBANKER then
        self.Button_invite_wx:setVisible(false)
        self.Button_CopyRoomkey:setVisible(false)
        self.Button_dismiss_desk:setVisible(false)
        self.Button_start:setVisible(false)
        self.Bytton_prestart:setVisible(false)
        self:updateRound(resp_json.iRoundTimes, resp_json.iAllRoundTimes)
    elseif resp_json.GSID == GameStation.GS_PLAYING then
        self:SetLaizi()
        self.Button_invite_wx:setVisible(false)
        self.Button_CopyRoomkey:setVisible(false)
        self.Button_dismiss_desk:setVisible(false)
        self.Button_start:setVisible(false)
        self.Bytton_prestart:setVisible(false)
        self:updateRound(resp_json.iRoundTimes, resp_json.iAllRoundTimes)

    elseif resp_json.GSID == GameStation.GS_WAIT_NEXT_ROUND 
    or resp_json.GSID == GameStation.GS_RESULT  then
        self.Button_invite_wx:setVisible(false)
        self.Button_CopyRoomkey:setVisible(false)
        self.Button_dismiss_desk:setVisible(false)
        self.Button_start:setVisible(false)
        self.Bytton_prestart:setVisible(false)
        self:updateRound(resp_json.iRoundTimes, resp_json.iAllRoundTimes)
    end

    if self.bCreateMore == true  then
        self.Button_dismiss_desk:loadTextures("res/game_res/" .. GAME_ID .. "/ui_res/base/tuichu.png", "res/game_res/" .. GAME_ID .. "/ui_res/base/tuichu.png", "")
    end
    self:updateLookerButtonState()
    print("--- BaseUIManager:onGameStation end")
end
--提前开局信息
function BaseUIManager:onGamePreStart(resp_json)
    local SitDownPlayerMap = GameSceneModule:getInstance():getGameScene():getSitDownPlayerMap()
    --if self.PreStartLayer == nil then 
    if resp_json.ReqID ==  0 and self.PreStartLayer == nil then
        self.Bytton_prestart:setVisible(false)
        self.PreStartLayer = PreStartLayer:create(  resp_json ,SitDownPlayerMap,  ViewHelp:getSelfInfo().bDeskStation ,      
                function()
                    --同意提前开局
                    self:request_prestart(1)
                end , function()
                    --不同意提前开局
                    self:request_prestart(2)
                end)
        self.ui_root:addChild(self.PreStartLayer)
    elseif resp_json.ReqID == 1 then
        self.PreStartLayer:set_apply(resp_json.ReqUser)
    elseif resp_json.ReqID == 2 then
        if self.PreStartLayer then
            self.PreStartLayer:removeFromParent()
            self.PreStartLayer = nil
        --    self.Bytton_prestart:setVisible(true)
        end    
    end
    
end
function BaseUIManager:updateStartBtn(resp_json)
    cclog("BaseUIManager:updateStartBtn")
    if GameSceneModule:getInstance():getGameScene():getGameType() == "qz1" then
        --if resp_json.iRoundTimes < resp_json.iAllRoundTimes then
            self.Button_start:setVisible(true)
        --end
    else
        self.Button_start:setVisible(true)
        --self.Button_ChangeDesk:setVisible(true)
    end
end
function BaseUIManager:request_prestart(id)
    local request = {}
    request.ReqId = id     --id: 0,发起请求   1，同意提前开局   2，不同意提前开局
    handleGameMessage.SendGameMessage(handleGameMessage.Table_GameMessage_ASSID.ASS_GAME_PRESTART_REQ, request)
    --self.Bytton_prestart:setVisible(false)
end

function BaseUIManager:ScanWaigua()
    --[[
    local AniNode = cc.CSLoader:createNode("game_res/"..GAME_ID.."/armature/waiguacheck/waiguacheck.csb")
    local AniAction = cc.CSLoader:createTimeline("game_res/"..GAME_ID.."/armature/waiguacheck/waiguacheck.csb")
    AniNode:runAction(AniAction)
    AniAction:gotoFrameAndPlay(0, false)
    AniNode:setPosition(cc.p(640, 360))
    self.ui_root:addChild(AniNode)
    AniAction:setLastFrameCallFunc( function()
        AniNode:removeFromParent()
    end )
    ]]
end

function BaseUIManager:ShowXipai(iXiPaiUserId)
    
--    self.AniNodeXipai = cc.CSLoader:createNode("game_res/"..GAME_ID.."/armature/xipai/xipai.csb")
--    local AniAction = cc.CSLoader:createTimeline("game_res/"..GAME_ID.."/armature/xipai/xipai.csb")
--    self.AniNodeXipai:runAction(AniAction)
--    AniAction:gotoFrameAndPlay(0, false)
--    self.AniNodeXipai:setPosition(cc.p(640, 360))
--    self.ui_project:addChild(self.AniNodeXipai)
--    AniAction:setLastFrameCallFunc( function()
--        self.AniNodeXipai:removeFromParent()
--        self.AniNodeXipai = nil
--    end )

     
      self.AniNodeXipai = cc.CSLoader:createNode("game_res/"..GAME_ID.."/armature/xipai/XiPaiAniNode.csb")
      local AniAction = cc.CSLoader:createTimeline("game_res/"..GAME_ID.."/armature/xipai/XiPaiAniNode.csb")
      AniAction:gotoFrameAndPlay(0, false)
      self.AniNodeXipai:setPosition(cc.p(640, 360))
      self.AniNodeXipai:runAction(AniAction)
    
      self.ui_project:addChild(self.AniNodeXipai)
      AniAction:setLastFrameCallFunc( function()
          self.AniNodeXipai:removeFromParent()
          self.AniNodeXipai = nil
     end )
     if iXiPaiUserId ~= nil and iXiPaiUserId > 0 then
         local userInfo = GameSceneModule:getInstance():getGameScene():getPlayerInfoByUserID(iXiPaiUserId)
         if userInfo ~= nil  then
             local u_nickName = api_get_ascll_sub_str_by_ui(userInfo.nickName,18)
             self.Text_player_xi_tip:setString("【" .. u_nickName .. "】正在洗牌" )
         end
     end
     
end

return BaseUIManager

--endregion
