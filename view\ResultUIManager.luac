--region *.lua
--Date
--此文件由[BabeLua]插件自动生成

--import("..GameDefine")
local GameMusic = import("..GameMusic")
local ResultUIManager = class("ResultUIManager")
local m_download = import("....common.download")
local GameLogic = import('..GameLogic')
local SHOW_ONEDAO_TIME = 2  -- 显示每一道的间隔时间
local EACH_DAO_TIME = 0.5
local ACT_SHOOT = 0.7 --打枪
local ACT_REDWAVE = 1.5 --红波浪
local total_score_position_y ={
    77.5,
    371.5,
    664,
    381

}

local cardType_position_y = {
    193,
    414,
    625,
    412
}

function ResultUIManager:ctor(ui_project, ui_root)
    self.ui_project = ui_project
    self.ui_root = ui_root

    self.time = 0
    self.shootSpeed = 80

    -- 是否可以点击开始游戏
    self.isBegin = false

    --牌型动画节点
    self.paixingAnimate1 = {}--头道
    self.paixingAnimate2 = {}--中道
    self.paixingAnimate3 = {}--尾道
    self.paixingAnimate4 = {}--特殊牌型

    -- 当前显示头中尾的下标
    self.daoIndex = 1

    -- 头中尾分和总分
    self.touPoint = { }
    self.zhongPoint = { }
    self.weiPoint = { }
    self.totalPoint = { }

    --当前显示的分数
    self.displayPoint = {0,0,0,0} 

    self.thspCards = {{},{},{},{},{},{},{}}

    -- 头 中 尾道摆牌数据
    self.touData = { }
    self.zhongData = { }
    self.weiData = { }

    -- 头中尾道牌型
    self.daoCardType = { }

    -- 头中尾道牌
    self.image_card_tou = { }
    self.image_card_zhong = { }
    self.image_card_wei = { }
    self.bshowingCard = false --正在比牌
    
    --结算面板：
    self.Panel_Resume = self.ui_project:getChildByName("Panel_Resume")
    self.Panel_Resume:setVisible(false)

    self.playerResults = { }
    self.Panel_result = {}
    for i = 1, PLAYER_COUNT do
        self.image_card_tou[i] = { }
        self.image_card_zhong[i] = { }
        self.image_card_wei[i] = { }

        self.playerResults[i] = { }
        self.playerResults[i].Panel_text = { }
        self.playerResults[i].Panel_hand_card = { }

        local Panel_result = self.ui_project:getChildByName("ResultCard_" .. i)
        if i==5 or  i==7 then
            Panel_result:setPositionX(Panel_result:getPositionX() - self.ui_project:getPositionX()/1.8)
        elseif i==3 or i==6 then
            Panel_result:setPositionX(Panel_result:getPositionX() + self.ui_project:getPositionX()/1.8)
        end
        Panel_result:setScale(1.2)
        self.Panel_result[i] = Panel_result
        local Panel_ResultCard = Panel_result:getChildByName("Panel_ResultCard")
        self.Panel_result[i].Panel_Tou = Panel_ResultCard:getChildByName("Panel_Tou")
        for j = 1, 3 do
            local image = self.Panel_result[i].Panel_Tou:getChildByName("Image_Card_" .. j):getChildByName("Image_Card")
            self:setOneCardValue(0, image, i-1)
            image:setVisible(false)
            table.insert(self.image_card_tou[i], image)
        end
        self.Panel_result[i].Panel_Zhong = Panel_ResultCard:getChildByName("Panel_Zhong")
        for j = 1, 5 do
            local image = self.Panel_result[i].Panel_Zhong:getChildByName("Image_Card_" .. j):getChildByName("Image_Card")
            self:setOneCardValue(0, image, i-1)
            image:setVisible(false)
            table.insert(self.image_card_zhong[i], image)
        end
        self.Panel_result[i].Panel_Wei = Panel_ResultCard:getChildByName("Panel_Wei")
        for j = 1, 5 do
            local image = self.Panel_result[i].Panel_Wei:getChildByName("Image_Card_" .. j):getChildByName("Image_Card")
            self:setOneCardValue(0, image, i-1)
            image:setVisible(false)
            table.insert(self.image_card_wei[i], image)
        end

        local Panel_text = Panel_result:getChildByName("Panel_text")
        for j = 1, 4 do
            self.playerResults[i].Panel_text[j] = Panel_text:getChildByName("Text_" .. j)
            self.playerResults[i].Panel_text[j]:setVisible(false)
        end

--        if i ~= 1 then
            self.playerResults[i].Image_Finish = Panel_result:getChildByName("Image_Finish")
            self.playerResults[i].Image_Finish:setVisible(false)

--            local Panel_hand_card = Panel_result:getChildByName("Panel_hand_card")
--            for j = 1, 13 do
--                self.playerResults[i].Panel_hand_card[j] = Panel_hand_card:getChildByName("Image_" .. j)
--                self.playerResults[i].Panel_hand_card[j]:setVisible(false)
--            end
--        end

    end

--    local listener = cc.EventListenerTouchOneByOne:create()
--    listener:setSwallowTouches(false)
--    listener:registerScriptHandler(handler(self, self.onTouchBegan), cc.Handler.EVENT_TOUCH_BEGAN)
--    local eventDispatcher = cc.Director:getInstance():getEventDispatcher()
--    eventDispatcher:addEventListenerWithSceneGraphPriority(listener, ui_project)

    self:ui_close()
end

-- 开始触摸
function ResultUIManager:onTouchBegan(touch,event)
    print("--- ResultUIManager:onTouchBegan")
    if self.isBegin == false then
        return false
    else 
        local location = touch:getLocation()
        if (location.x > 1150 and location.x < 1280) and ((location.y > 30 and location.y < 267 ) or (location.y > 610 and location.y < 715 )) 
        or ((location.x > 14 and location.x < 115) and (location.y > 620 and location.y < 722 ) )then
           --屏蔽按键位置
            return false
        end
        self:setInit()
        local request = { }
        handleGameMessage.SendGameMessage(handleGameMessage.Table_GameMessage_ASSID.ASS_GM_AGREE_GAME, request)
        --self.isBegin = false
    end

end

function ResultUIManager:onCardColorChange()
    if self.bshowingCard then
        return 
    end
    local CardNameTab = {"card_R.png","card_G.png","card_B.png"}
    local path = "res/game_res/"..GAME_ID.."/ui_res/baipai/card/"..CardNameTab[UserData:getCardColor()]
    for i=1,PLAYER_COUNT do
        for k,v in pairs(self.image_card_tou[i]) do
            v:loadTexture(path)
        end
        for k,v in pairs(self.image_card_zhong[i]) do
            v:loadTexture(path)
        end
        for k,v in pairs(self.image_card_wei[i]) do
            v:loadTexture(path)
        end
    end
end


-- 游戏开始，初始化该类游戏数据
function ResultUIManager:onGameStart(resp_json)
    print("--- ResultUIManager:onGameStart")
    self.bshowingCard = false
    self.specialAnimate = {}
    self.paixingAnimate= {}
    self.daoIndex = 1
    self.time = 0

    self.touPoint = {}
    self.zhongPoint = {}
    self.weiPoint = {}
    self.totalPoint = {}

    self.touData = { }
    self.zhongData = { }
    self.weiData = { }

    self.thspCards = {{},{},{},{},{},{},{}}
    --self.Image_select:setVisible(false)
    self.Panel_Resume:setVisible(false)
    self.daoCardType = { }

    local PlayersInfos = GameSceneModule:getInstance():getGameScene().PlayersInfos
    for i=1,#PlayersInfos do
        local player_info = PlayersInfos[i]
        local ui_chair = ViewHelp.getUIChairIndexByServerChair(player_info.bDeskStation)
        --self.playerResults[ui_chair].Image_card_type:setPositionY(cardType_position_y[ui_chair])
        --self:showHandCard(ui_chair,true)
    end
    local CardNameTab = {"card_R.png","card_G.png","card_B.png"}
    local path = "res/game_res/"..GAME_ID.."/ui_res/baipai/card/"..CardNameTab[UserData:getCardColor()]
    for i=1,PLAYER_COUNT do
        for k,v in pairs(self.image_card_tou[i]) do
            self:setOneCardValue(0, v, i-1)
        end
        for k,v in pairs(self.image_card_zhong[i]) do
            self:setOneCardValue(0, v, i-1)
        end
        for k,v in pairs(self.image_card_wei[i]) do
            self:setOneCardValue(0, v, i-1)
        end
    end
    self:CleanPxAnimation()
--    self:updatePlayerInfo()
--    for i = 1, PLAYER_COUNT do
--        local ui_chair = ViewHelp.getUIChairIndexByServerChair(i - 1)
--        if ui_chair ~= 1 then
--            for j = 1, 13 do
--                self.playerResults[ui_chair].Panel_hand_card[j]:setVisible(true)
--            end
--        end
--    end

end

function ResultUIManager:SetResumePanelScore(pointTab)
--    for i = 1,#pointTab  do
--        local panel_play = self.Panel_Resume:getChildByName("Panel_player_"..i)
--        local Text_score = panel_play:getChildByName("Text_score")
--        Text_score:setText(pointTab[i])
--    end
end
-- 摆牌结果
function ResultUIManager:onBaipaiResult(resp_json)
    print("--- ResultUIManager:onBaipaiResult start")
    if resp_json.chair == nil then
        return
    end

    local ui_chair = ViewHelp.getUIChairIndexByServerChair(resp_json.chair)
    self.touData[resp_json.chair] = resp_json.touData
    self.zhongData[resp_json.chair] = resp_json.zhongData
    self.weiData[resp_json.chair] = resp_json.weiData
    self.daoCardType[resp_json.chair] = resp_json.daoCardType

    if ui_chair == 1 then
        self:ui_open()
        self:showHandCard(1, false)
--        self:showOneDaoCard(self.touData[1],self.image_card_tou[1])
--        self:showOneDaoCard(self.zhongData[1],self.image_card_zhong[1])
--        self:showOneDaoCard(self.weiData[1],self.image_card_wei[1])

    else
       self:showHandCard(ui_chair,false)
    end

    for i=1,#resp_json.baiPaiOK do
        if resp_json.baiPaiOK[i] then
            local uiChair = ViewHelp.getUIChairIndexByServerChair(i - 1)
            self:showHandCard(uiChair,false)
        end
    end



    print("--- ResultUIManager:onBaipaiResult end")
end

-- 亮牌结算
function ResultUIManager:onGameResult(resp_json)
    print("--- ResultUIManager:onGameResult start")
    if resp_json == nil then
        return
    end

--    local str = tostring('{"DisplayTime":4,"FanpaiSeq":[[4,6,3,0,5,2,1],[4,0,6,2,3,5],[0,2,5,6,4,3]],"QLDPlayer":-1,"Shoot":[{"byShootPlayers":[0,0,0,0,0,0,0]},{"byShootPlayers":[0,0,0,0,0,0,0]},{"byShootPlayers":[1,0,0,0,0,0,0]},{"byShootPlayers":[0,0,0,0,1,0,1]},{"byShootPlayers":[0,0,0,0,0,0,0]},{"byShootPlayers":[1,0,0,0,0,0,0]},{"byShootPlayers":[0,0,0,0,0,0,0]}],"bQuickCompare":false,"daoCardType":[[1,1,1,0],[0,0,0,1],[1,3,4,0],[0,4,9,0],[0,1,8,0],[1,6,7,0],[0,1,8,0]],"iAllRoundTimes":18,"iRoundTimes":16,"thspCards":[[0],[0],[0],[0],[0],[0],[0]],"totalPoint":[-44,36,-21,69,-18,2,-24],"touData":[[7,20,14],[1,25,12],[40,14,52],[40,26,20],[12,11,10],[27,1,9],[51,24,49]],"touPoint":[1,0,5,-1,-4,3,-4],"weiData":[[26,13,5,17,2],[44,18,18,43,4],[25,37,36,22,34],[47,47,21,8,53],[7,54,5,4,3],[23,49,23,54,33],[45,44,53,42,41]],"weiPoint":[-25,0,-23,50,10,-12,0],"zhongData":[[48,35,38,50,21],[50,24,48,22,45],[2,15,15,46,17],[39,51,11,10,9],[19,32,46,43,41],[3,16,42,6,19],[52,13,8,6,16]],"zhongPoint":[-4,0,0,2,-6,10,-2]}')
--    resp_json = json.decode(str)
    --开始比牌音效
    GameMusic:playStartThanCardEffect(1)

    local function compareCard()
        self:ui_open()

        self.touPoint = resp_json.touPoint
        self.zhongPoint = resp_json.zhongPoint
        self.weiPoint = resp_json.weiPoint
        self.totalPoint = resp_json.totalPoint

        self.touData = resp_json.touData
        self.zhongData = resp_json.zhongData
        self.weiData = resp_json.weiData
        self.daoCardType = resp_json.daoCardType
        self.thspCards = resp_json.thspCards
        for i=1,PLAYER_COUNT do
            self.playerResults[i].Image_Finish:setVisible(false)
        end
        if resp_json.bQuickCompare then
            self:QuickShowAllCard(resp_json)
        else
            self:showAllCard(resp_json)
        end
        self:SetResumePanelScore(resp_json.totalPoint)
        print("--- ResultUIManager:onGameResult end")
    end

    performWithDelay(self.ui_project, compareCard, 1)

end

-- 显示每一道牌
function ResultUIManager:QuickShowAllCard(resp_json)
    print("--- ResultUIManager:QuickShowAllCard index = " .. self.daoIndex)
    if ViewHelp:getGameStation() ~= GameStation.GS_RESULT then
        return 
    end
    local PlayersInfos = GameSceneModule:getInstance():getGameScene().PlayersInfos
    if self.daoIndex == 5 then
        dump(resp_json)
        -- 判断每个玩家打枪
        for i=1,#PlayersInfos do
            local player_info_i = PlayersInfos[i]
            for j=1,#PlayersInfos do
                local player_info_j = PlayersInfos[j]
                if resp_json.Shoot[player_info_i.bDeskStation+1].byShootPlayers[player_info_j.bDeskStation+1] == 1 then
                    local shoot_ui_chair = ViewHelp.getUIChairIndexByServerChair(player_info_i.bDeskStation)
                    local byShoot_ui_chair = ViewHelp.getUIChairIndexByServerChair(player_info_j.bDeskStation)
                    --GameMusic:playShootSpeak(shoot_ui_chair)
                    performWithDelay(self.ui_project,
                    function()
                        self:shootAnimate(shoot_ui_chair, byShoot_ui_chair)
                    end ,
                    self.time
                    )
                    self.time = self.time + 1.25
                end
            end
        end

        -- 全垒打
        cclog("全垒打:"..tostring(resp_json.QLDPlayer).."PLAYER_COUNT = "..tostring(PLAYER_COUNT))
        if resp_json.QLDPlayer >= 0 and resp_json.QLDPlayer <= PLAYER_COUNT - 1 then
            performWithDelay(self.ui_project,
            function()
                self:qldAnimate(resp_json.QLDPlayer)
            end ,
            self.time
            )
            self.time = self.time + 1.7
        end

        performWithDelay(self.ui_project,
        function()
            self:addPointAnimate(resp_json)
            GameSceneModule:getInstance():getGameScene().GameLayer.PlayersHeadUIManager:updateAllPlayerScore()
        end ,
        self.time
        )

        return
    end

    if self.daoIndex == 1 then
        for i = 1, #PlayersInfos do
            local player_info = PlayersInfos[i]
            local server_chair = player_info.bDeskStation
            local ui_chair = ViewHelp.getUIChairIndexByServerChair(server_chair)
            GameMusic:playFanPaiEffect()
            self:showOneDaoCard(self.touData[server_chair + 1], self.image_card_tou[ui_chair], server_chair)
            self:showCardType(server_chair, ui_chair, 1)

            if self.daoCardType[server_chair + 1][4] > 0 then
                GameMusic:playFanPaiEffect()
                self:showOneDaoCard(self.zhongData[server_chair + 1], self.image_card_zhong[ui_chair], server_chair)
                self:showOneDaoCard(self.weiData[server_chair + 1], self.image_card_wei[ui_chair], server_chair)
                self:showSpecialCardType(server_chair, ui_chair)
            end
        end
    elseif self.daoIndex == 2 then
        for i = 1, #PlayersInfos do
            local player_info = PlayersInfos[i]
            local server_chair = player_info.bDeskStation
            local ui_chair = ViewHelp.getUIChairIndexByServerChair(server_chair)

            GameMusic:playFanPaiEffect()
            self:showOneDaoCard(self.zhongData[server_chair + 1], self.image_card_zhong[ui_chair], server_chair)
            self:showCardType(server_chair, ui_chair, 2)
        end
    elseif self.daoIndex == 3 then
        for i = 1, #PlayersInfos do
            local player_info = PlayersInfos[i]
            local server_chair = player_info.bDeskStation
            local ui_chair = ViewHelp.getUIChairIndexByServerChair(server_chair)

            GameMusic:playFanPaiEffect()
            self:showOneDaoCard(self.weiData[server_chair + 1], self.image_card_wei[ui_chair], server_chair)
            self:showCardType(server_chair, ui_chair, 3)
        end
    elseif self.daoIndex == 4 then
        --return 
        for i = 1, #PlayersInfos do
            local player_info = PlayersInfos[i]
            local server_chair = player_info.bDeskStation
            local ui_chair = ViewHelp.getUIChairIndexByServerChair(server_chair)
            self:showCardType(server_chair, ui_chair, 4)
            if i == #PlayersInfos then
                self:showPoint(4)
            end
        end
    end

   self.daoIndex = self.daoIndex + 1
   local delaytime = 1
   if self.daoIndex == 5 then
       delaytime = 0.5
   end
    performWithDelay(self.ui_project,
    function()
        self:QuickShowAllCard(resp_json)
    end ,
    delaytime
    )

end

-- 显示每一道牌
function ResultUIManager:showAllCard(resp_json)
    print("--- ResultUIManager:showAllCard index = " .. self.daoIndex)
    if ViewHelp:getGameStation() ~= GameStation.GS_RESULT then
        return 
    end
    self.bshowingCard = true
    local PlayersInfos = GameSceneModule:getInstance():getGameScene().PlayersInfos
    --dump(resp_json)
    if self.daoIndex == 5 then
        -- 判断每个玩家打枪
        for i=1,#PlayersInfos do
            local player_info_i = PlayersInfos[i]
            for j=1,#PlayersInfos do
                local player_info_j = PlayersInfos[j]
                if resp_json.Shoot[player_info_i.bDeskStation+1].byShootPlayers[player_info_j.bDeskStation+1] == 1 then
                    local shoot_ui_chair = ViewHelp.getUIChairIndexByServerChair(player_info_i.bDeskStation)
                    local byShoot_ui_chair = ViewHelp.getUIChairIndexByServerChair(player_info_j.bDeskStation)
                    --GameMusic:playShootSpeak(shoot_ui_chair)
                    performWithDelay(self.ui_project,
                    function()
                        self:shootAnimate(shoot_ui_chair, byShoot_ui_chair)
--                        local point = self:GetShootRedwaveChangePoint(player_info_i.bDeskStation, player_info_j.bDeskStation, ACT_SHOOT)
--                        self:ChangePoint(shoot_ui_chair, byShoot_ui_chair, point)
                    end ,
                    self.time
                    )
                    self.time = self.time + 1.25
                end
            end
        end

        -- 全垒打
        cclog("全垒打:"..tostring(resp_json.QLDPlayer).."PLAYER_COUNT = "..tostring(PLAYER_COUNT))
        if resp_json.QLDPlayer >= 0 and resp_json.QLDPlayer <= PLAYER_COUNT - 1 then
            performWithDelay(self.ui_project,
            function()
--                for _,v in pairs(PlayersInfos) do
--                    if v.bDeskStation ~= resp_json.QLDPlayer then
--                        local from_ui_chair = ViewHelp.getUIChairIndexByServerChair(resp_json.QLDPlayer)
--                        local to_ui_chair = ViewHelp.getUIChairIndexByServerChair(v.bDeskStation)
--                        local point = self:GetShootRedwaveChangePoint(resp_json.QLDPlayer, v.bDeskStation, ACT_REDWAVE)
--                        self:ChangePoint(from_ui_chair, to_ui_chair, point)
--                    end
--                end
                self:qldAnimate(resp_json.QLDPlayer)
            end ,
            self.time
            )
            self.time = self.time + 1.25
        end

        performWithDelay(self.ui_project,
        function()
            self:addPointAnimate(resp_json)
            --self.Panel_Resume:setVisible(true)
            --GameSceneModule:getInstance():getGameScene().GameLayer.BaseUIManager:updateStartBtn(resp_json)
            GameSceneModule:getInstance():getGameScene().GameLayer.PlayersHeadUIManager:updateAllPlayerScore()
            --GameSceneModule:getInstance():getGameScene().GameLayer.PlayersHeadUIManager:updatePlayerInfo(255)
        end ,
        self.time
        )

        return
    end

    if self.daoIndex == 1 then
        local function showTouDao(svrChair, uiChair)
            GameMusic:playFanPaiEffect()
            self:showOneDaoCard(self.touData[svrChair + 1], self.image_card_tou[uiChair],svrChair)
            self:showCardType(svrChair, uiChair, 1)
            if self.daoCardType[svrChair + 1][4] > 0 then
                self:showOneDaoCard(self.zhongData[svrChair + 1], self.image_card_zhong[uiChair],svrChair)
                self:showOneDaoCard(self.weiData[svrChair + 1], self.image_card_wei[uiChair],svrChair)
                self:showSpecialCardType(svrChair, uiChair)
            end
        end      
        for i = 1, #resp_json.FanpaiSeq[1] do
            local server_chair = resp_json.FanpaiSeq[1][i]
            local ui_chair = ViewHelp.getUIChairIndexByServerChair(server_chair)
            performWithDelay(self.ui_project, function()
                showTouDao(server_chair, ui_chair)
                if i == #resp_json.FanpaiSeq[1] then
                    performWithDelay(self.ui_project, function() self:showPoint(1) end, 0.15)
                end
            end, EACH_DAO_TIME * (i - 1))
        end
    elseif self.daoIndex == 2 then
        local function showZhongDao(svrChair, uiChair)
            GameMusic:playFanPaiEffect()
            self:showOneDaoCard(self.zhongData[svrChair + 1], self.image_card_zhong[uiChair],svrChair)
            self:showCardType(svrChair, uiChair, 2)
        end
        for i = 1, #resp_json.FanpaiSeq[2] do
            local server_chair = resp_json.FanpaiSeq[2][i]
            local ui_chair = ViewHelp.getUIChairIndexByServerChair(server_chair)

            performWithDelay(self.ui_project, function()
                showZhongDao(server_chair, ui_chair)
                if i == #resp_json.FanpaiSeq[2] then
                    performWithDelay(self.ui_project, function() self:showPoint(2) end, 0.15)
                end
            end, EACH_DAO_TIME * (i - 1))
        end
    elseif self.daoIndex == 3 then
        local function showWeiDao(svrChair, uiChair)
            GameMusic:playFanPaiEffect()
            self:showOneDaoCard(self.weiData[svrChair + 1], self.image_card_wei[uiChair], svrChair)
            self:showCardType(svrChair, uiChair, 3)
        end
        for i = 1, #resp_json.FanpaiSeq[3] do
            local server_chair = resp_json.FanpaiSeq[3][i]
            local ui_chair = ViewHelp.getUIChairIndexByServerChair(server_chair)

            performWithDelay(self.ui_project, function()
                showWeiDao(server_chair, ui_chair)
                if i == #resp_json.FanpaiSeq[3]then
                    performWithDelay(self.ui_project, function() self:showPoint(3) end, 0.15)
                end
            end, EACH_DAO_TIME * (i - 1))
        end
    elseif self.daoIndex == 4 then
        --return 
        for i = 1, #PlayersInfos do
            local player_info = PlayersInfos[i]
            local server_chair = player_info.bDeskStation
            local ui_chair = ViewHelp.getUIChairIndexByServerChair(server_chair)
            self:showCardType(server_chair, ui_chair, 4)
            if i == #PlayersInfos then
                self:showPoint(4)
            end
        end
    end

    self.daoIndex = self.daoIndex + 1

    local delaytime = SHOW_ONEDAO_TIME/4 * #PlayersInfos
    if self.daoIndex == 5 then
        delaytime = 0.5
    end

    performWithDelay(self.ui_project,
    function()
        self:showAllCard(resp_json)
    end ,
    delaytime
    )

end

-- 打枪动画 shoot_ui_chair 打枪玩家 byShoot_ui_chair 被打枪玩家
function ResultUIManager:shootAnimate(shoot_ui_chair, byShoot_ui_chair)
    print("--- ResultUIManager:shootAnimate start")
    if ViewHelp:getGameStation() ~= GameStation.GS_RESULT then
        return 
    end
    GameMusic:playShootSpeak(shoot_ui_chair)
    GameMusic:playShootEffect()

    local image = ccui.ImageView:create()
    image:setPosition(0,0)
    self.ui_root:addChild(image)

    local shoot_node = nil
    local shoot_action = nil
    shoot_node = cc.CSLoader:createNode("game_res/"..GAME_ID.."/armature/daqiang1/daqiang.csb")
    shoot_action = cc.CSLoader:createTimeline("game_res/"..GAME_ID.."/armature/daqiang1/daqiang.csb")


    

    shoot_node:runAction(shoot_action)
    shoot_action:gotoFrameAndPlay(0, false)
    shoot_action:setTimeSpeed(self.shootSpeed / 100)

    --shoot_node:setPosition(cc.p(640, 360))
    image:addChild(shoot_node)
    shoot_action:setLastFrameCallFunc( function()
        image:removeFromParent()
    end )

    --[[
    if shoot_ui_chair == 1 then
        if byShoot_ui_chair == 5 or byShoot_ui_chair == 6 or byShoot_ui_chair == 7 then
            image:setFlippedX(true)
        end
        local RotationTab = {0,20,0,-30,20,5,-10}
       -- image:setPosition(cc.p(614, 106))
        image:setRotation(RotationTab[byShoot_ui_chair])
    elseif shoot_ui_chair == 2 then
        image:setFlippedX(true)
       -- image:setPosition(cc.p(1043, 210)) 
        local RotationTab = {-50,0,80,30,10,-10,-30}
        image:setRotation(RotationTab[byShoot_ui_chair])
    elseif shoot_ui_chair == 3 then
        image:setFlippedX(true)
        --image:setPosition(cc.p(1042, 413)) 
        local RotationTab = {-70,-160,0,0,-10,-30,-50}
        image:setRotation(RotationTab[byShoot_ui_chair])
    elseif shoot_ui_chair == 4 then
       -- image:setPosition(cc.p(701, 558))
        if byShoot_ui_chair~= 2 and byShoot_ui_chair~=3 then
            image:setFlippedX(true)
        end
        local RotationTab = {-100,80,60,0,-20,-60,-80}
        image:setRotation(RotationTab[byShoot_ui_chair])
    elseif shoot_ui_chair == 5 then
        --image:setPosition(cc.p(333, 584))
        if byShoot_ui_chair== 6 or byShoot_ui_chair==7 then
            image:setFlippedX(true)
        end
        local RotationTab = {100,70,50,30,0,-80,-100}
        image:setRotation(RotationTab[byShoot_ui_chair])
     elseif shoot_ui_chair == 6 then
       -- image:setPosition(cc.p(222.63, 429.51))
        local RotationTab = {90,50,30,10,-10,0,150}
        image:setRotation(RotationTab[byShoot_ui_chair])
     elseif shoot_ui_chair == 7 then
        --image:setPosition(cc.p(232, 208))
        local RotationTab = {60,20,0,-10,-30,-70,0}
        image:setRotation(RotationTab[byShoot_ui_chair])
    end
    --]]
    local StartPos = cc.p(self.Panel_result[shoot_ui_chair]:getPositionX() + self.ui_project:getPositionX(), self.Panel_result[shoot_ui_chair]:getPositionY())
    image:setPosition(StartPos)
    local endPos = cc.p(self.Panel_result[byShoot_ui_chair]:getPositionX() + self.ui_project:getPositionX(), self.Panel_result[byShoot_ui_chair]:getPositionY())
    if StartPos.x > endPos.x then
        image:setFlippedX(true)
        local dis_x =  StartPos.x - endPos.x
        local dis_y = endPos.y - StartPos.y
        local dis_xy = math.sqrt(dis_x * dis_x + dis_y * dis_y)
        local cos_ret = dis_x / dis_xy
        print("***************************rotataion = " .. cos_ret)
        local rotataion = math.acos(dis_x / dis_xy)
        rotataion = math.deg(rotataion)
        rotataion = rotataion + 18
        if dis_y < 0 then
            rotataion = 0 - rotataion
        end
        image:setRotation(rotataion)
        print("***************************rotataion = " .. rotataion)
    else
        local dis_x = endPos.x - StartPos.x
        local dis_y = endPos.y - StartPos.y
        local dis_xy = math.sqrt(dis_x * dis_x + dis_y * dis_y)
        local cos_ret = dis_x / dis_xy
        print("***************************rotataion = " .. cos_ret)
        local rotataion = math.acos(dis_x / dis_xy)
        rotataion = math.deg(rotataion)
        rotataion = rotataion + 18
        if dis_y > 0  then
            rotataion = 0 - rotataion
        end
        image:setRotation(rotataion)
        print("***************************rotataion = " .. rotataion)
    end
    self:daKongAnimate(byShoot_ui_chair)

    print("--- ResultUIManager:shootAnimate end")
end

-- 全垒打动画
function ResultUIManager:qldAnimate(qld_server_chair)
    print("--- ResultUIManager:qldAnimate start")
    if ViewHelp:getGameStation() ~= GameStation.GS_RESULT then
        return 
    end
    
    GameMusic:playQLDEffect()

--    --local qld_node = cc.CSLoader:createNode("game_res/"..GAME_ID.."/armature/redwave/redwave.csb")
    local qld_node = cc.CSLoader:createNode("game_res/"..GAME_ID.."/armature/quanleida1/qld.csb")
    self.qld_node = qld_node
--    --local qld_action = cc.CSLoader:createTimeline("game_res/"..GAME_ID.."/armature/redwave/redwave.csb")
    local qld_action = cc.CSLoader:createTimeline("game_res/"..GAME_ID.."/armature/quanleida1/qld.csb")
    qld_node:runAction(qld_action)
    --qld_action:setTimeSpeed(self.shootSpeed / 100)
    qld_action:gotoFrameAndPlay(0, false)
--    qld_node:setPosition(cc.p(640, 360))
    self.ui_root:addChild(qld_node)
    qld_action:setLastFrameCallFunc( function()
        self.qld_node:removeFromParent()
        GameMusic:playQLDSpeak(ViewHelp.getUIChairIndexByServerChair(qld_server_chair))
        local qld_node1 = cc.CSLoader:createNode("game_res/"..GAME_ID.."/armature/quanleida/qld_ani1.csb")
        self.qld_node1 = qld_node1
        local qld_action1 = cc.CSLoader:createTimeline("game_res/"..GAME_ID.."/armature/quanleida/qld_ani1.csb")
        qld_node1:runAction(qld_action1)
        qld_action1:gotoFrameAndPlay(0, false)
        qld_node1:setPosition(cc.p(640, 360))
        self.ui_root:addChild(qld_node1)
        qld_action1:setLastFrameCallFunc( function()
            self.qld_node1:removeFromParent()
        end )
    end )
--    local posTab = {}
--    local gamescene = GameSceneModule:getInstance():getGameScene()
--    local PlayerLayer =  gamescene.GameLayer.PlayersHeadUIManager.PlayerHeads
--    for i = 1,7 do
--        posTab[i]=cc.p(PlayerLayer[i]:getPosition())
--    end

    local ratationTab = {190+180,120+180,90+180,0+180,-40+180,-90+180,-120+180}


    local qld_ui_chair = ViewHelp.getUIChairIndexByServerChair(qld_server_chair)
    qld_node:setPosition( cc.p(self.Panel_result[qld_ui_chair]:getPosition()))
    qld_node:setRotation(ratationTab[qld_ui_chair])



    
--    if qld_ui_chair == 1 then
--        qld_node:setPosition(cc.p(640, 11))
--        --qld_node:setRotation(-67)
--    elseif qld_ui_chair == 2 then
--        qld_node:setPosition(cc.p(989, 249))
--       -- qld_node:setRotation(-160)
--    elseif qld_ui_chair == 3 then
--        qld_node:setPosition(cc.p(640, 459))
--        --qld_node:setRotation(111)
--    elseif qld_ui_chair == 4 then
--        qld_node:setPosition(cc.p(247, 248))
--        --qld_node:setRotation(20.5)
--    end
--    local qld_ui_chair = ViewHelp.getUIChairIndexByServerChair(qld_server_chair)
--    if qld_ui_chair == 1 then
--        qld_node:setPosition(cc.p(410, 222))
--        qld_node:setRotation(-67)
--    elseif qld_ui_chair == 2 then
--        qld_node:setPosition(cc.p(882, 130))
--        qld_node:setRotation(-160)
--    elseif qld_ui_chair == 3 then
--        qld_node:setPosition(cc.p(867, 507))
--        qld_node:setRotation(111)
--    elseif qld_ui_chair == 4 then
--        qld_node:setPosition(cc.p(341, 588))
--        qld_node:setRotation(20.5)
--    end

    local PlayersInfos = GameSceneModule:getInstance():getGameScene().PlayersInfos
    for i=1,#PlayersInfos do
        local player_info = PlayersInfos[i]
        local server_chair = player_info.bDeskStation
        local ui_chair = ViewHelp.getUIChairIndexByServerChair(server_chair)
        if qld_ui_chair ~= ui_chair then
            self:daKongAnimate(ui_chair, true)
        end
    end

    print("--- ResultUIManager:qldAnimate end")

end

-- 打孔动画
function ResultUIManager:daKongAnimate(ui_chair, bQldDakong)
    print("--- ResultUIManager:daKongAnimate start")
    local path = ""
    if bQldDakong then
        path = "game_res/"..GAME_ID.."/armature/dakong1/dakong_qld.csb"
    else
        path = "game_res/"..GAME_ID.."/armature/dakong1/dakong_qiang.csb"
    end
    local dk_node = cc.CSLoader:createNode(path)
    --self.dk_node = dk_node
    local dk_action = cc.CSLoader:createTimeline(path)
    dk_node:runAction(dk_action)
    dk_action:gotoFrameAndPlay(0, false)
    
    self.ui_root:addChild(dk_node)
    dk_action:setLastFrameCallFunc( function()
        dk_node:removeFromParent()
    end )
    local pos = cc.p(self.Panel_result[ui_chair]:getPositionX()+self.ui_project:getPositionX(), self.Panel_result[ui_chair]:getPositionY())
    dk_node:setPosition(pos)
    print("--- ResultUIManager:daKongAnimate end")
end

-- 显示特殊牌型
function ResultUIManager:showSpecialCardType(server_chair,ui_chair)
    print("--- ResultUIManager:showSpecialCardType")


    local text = ""
    local s_card_type = self.daoCardType[server_chair+1][4]
    print("**********************************s_card_type =" .. s_card_type)
    if s_card_type <= 0 then
        return
    end

    local filename = {
   -- "santonghua",
   -- "sanshunzi",
   -- "liuduiban",
   -- "wuduisantiao",
   -- "sitaosantiao",
   -- "liutong",
   -- "santonghuashun",
   -- "yitiaolong",
   -- "qitong",
   -- "zhizunqinglong"
      [SpecialCardType.SCT_STH] =  "santonghua",
      [SpecialCardType.SCT_SSZ] =  "sanshunzi",
      [SpecialCardType.SCT_LDB] =  "liuduiban",
      [SpecialCardType.SCT_QD] =  "quanda",
      [SpecialCardType.SCT_QX] =  "quanxiao",
      [SpecialCardType.SCT_WDST] =  "wuduisantiao",
      [SpecialCardType.SCT_STST] =  "sitaosantiao",
      [SpecialCardType.SCT_ONEBLACK] =  "oneblack",
      [SpecialCardType.SCT_ONERED] =  "onered",
      [SpecialCardType.SCT_LT] =  "liutong",
      [SpecialCardType.SCT_STHS] =  "santonghuashun",
      [SpecialCardType.SCT_YTL] =  "yitiaolong",
      [SpecialCardType.SCT_QT] =  "qitong",
      [SpecialCardType.SCT_ALLBLACK] =  "allblack",
      [SpecialCardType.SCT_ALLRED] =  "allred",
      [SpecialCardType.SCT_QL] =  "zhizunqinglong",
      [SpecialCardType.SCT_HALF_BIG] =  "banda",
      [SpecialCardType.SCT_HALF_SMALL] =  "banxiao",
    }

--    if s_card_type == 1 then
--        text = "三同花"
--        path = "santonghua.csb"
--    elseif s_card_type == 2 then
--        text = "三顺子"
--        path = "sanshunzi.csb"
--    elseif s_card_type == 3 then
--        text = "六对半"
--        path = "liuduiban.csb"
--    elseif s_card_type == 4 then
--        text = "四套三条"
--        path = "staosantiao.csb"
--    elseif s_card_type == 5 then
--        text = "凑一色"
--        path = "couyise.csb"
--    elseif s_card_type == 6 then
--        text = "全小"
--        path = "quanxiao.csb"
--    elseif s_card_type == 7 then
--        text = "全大"
--        path = "quanda.csb"
--    elseif s_card_type == 8 then
--        text = "三分天下"
--        path = "sfentianxia.csb"
--    elseif s_card_type == 9 then
--        text = "三同花顺"
--        path = "stonghuashun.csb"
--    elseif s_card_type == 10 then
--        text = "一条龙"
--        path = "yitiaolong.csb"
--    elseif s_card_type == 11 then
--        text = "清龙"
--        path = "qinglong.csb"
--    end

    --self.playerResults[ui_chair].Text_special:setText(text)
    --self.playerResults[ui_chair].Image_special:setVisible(true)
    if s_card_type == 9 or s_card_type == 10 then
        --[[
        local DrangonPath = "game_res/"..GAME_ID.."/armature/teshupaixing/drangon/long.csb"
        local DrangonNode = cc.CSLoader:createNode(DrangonPath)
        self.ui_project:addChild(DrangonNode)
        DrangonNode:setPosition(cc.p(640,360))
        local DrangonAction = cc.CSLoader:createTimeline(DrangonPath)
        DrangonNode:runAction(DrangonAction)
        DrangonAction:gotoFrameAndPlay(0, false)
        DrangonAction:setLastFrameCallFunc( function()
                DrangonNode:removeFromParent()
            end )
            ]]
    end


    local path = "game_res/"..GAME_ID.."/armature/teshupaixing/" .. filename[s_card_type]..".csb"
    local spe_node = cc.CSLoader:createNode(path)

    

    self.Panel_result[ui_chair]:addChild(spe_node)
    local spe_action = cc.CSLoader:createTimeline(path)
    spe_node:runAction(spe_action)
    spe_action:gotoFrameAndPlay(0, false)
    spe_node:setPosition(cc.p(-25, -200))
    table.insert(self.paixingAnimate4,spe_node)

    GameMusic:playSpecialCardTypeEffect(s_card_type, ui_chair)
end

-- 显示每一道牌型
function ResultUIManager:showCardType(server_chair,ui_chair, daoInx)
    print("--- ResultUIManager:showCardType")
    local card_type = 0
    if daoInx < 4 then
        card_type = self.daoCardType[server_chair  + 1][daoInx]
    end
    local fileName = {"wulong",
                    "duizi",
                    "liangdui",
                    "santiao",
                    "shunzi",
                    "tonghua",
                    "hulu",
                    "zhadan",
                    "tonghuashun",
                    "wutong",
                    "liuTong"}
      local path = "game_res/"..GAME_ID.."/armature/putongpaixing/" .. fileName[card_type+1]..".csb"

    local paixing_node = cc.CSLoader:createNode(path)
    self.paixing_node = paixing_node
    local spe_action = cc.CSLoader:createTimeline(path)
    if daoInx ~= 4 and self.daoCardType[server_chair+1][4] <=0 then
        paixing_node:runAction(spe_action)
        spe_action:gotoFrameAndPlay(0, false)
    end

    self.Panel_result[ui_chair]:addChild(paixing_node)

    local point = 0
    if daoInx == 1 then
        table.insert(self.paixingAnimate1,paixing_node)
        point = self.touPoint[server_chair + 1]
        paixing_node:setPosition(cc.p(-25, -140))
        if self.daoCardType[server_chair+1][4] <=0 then
            if card_type == CardType.CT_THREE then
                local file = "cardType/chongsan.mp3"
                local wangNum = 0
                for _,cardVal in pairs(self.touData[server_chair + 1]) do
                    if cardVal == 53 or cardVal == 54 then
                        wangNum = wangNum + 1
                    end
                end
                if wangNum == 3 then
                    file = "cardType/chongsanwang.mp3"
                elseif wangNum == 2 then
                    file = "cardType/chongshuangwang.mp3"
                end
                GameMusic:PlayEffect(file, ui_chair)
            else 
                GameMusic:playCardTypeEffect(card_type, ui_chair)
            end
        end
    elseif daoInx == 2 then
        if #self.paixingAnimate1 > 0 then
            for k,v in pairs(self.paixingAnimate1) do
                v:removeFromParentAndCleanup(true)
                v = nil
            end
        end
        self.paixingAnimate1 = {}
        table.insert(self.paixingAnimate2,paixing_node)
        point = self.zhongPoint[server_chair + 1]
        paixing_node:setPosition(cc.p(-25, -202))
        if self.daoCardType[server_chair+1][4] <=0 then 
            if card_type == CardType.CT_GOURD then
                local file = "cardType/midGroud.mp3"
                GameMusic:PlayEffect(file, ui_chair)
            else
                GameMusic:playCardTypeEffect(card_type, ui_chair)
            end   
        end
    elseif daoInx == 3 then
        if #self.paixingAnimate2 > 0 then
            for k,v in pairs(self.paixingAnimate2) do
                v:removeFromParentAndCleanup(true)
                v = nil
            end
        end
        self.paixingAnimate2 = {}
        table.insert(self.paixingAnimate3,paixing_node)
        point = self.weiPoint[server_chair + 1]
        paixing_node:setPosition(cc.p(-25, -264))
        if self.daoCardType[server_chair+1][4] <=0 then 
            GameMusic:playCardTypeEffect(card_type, ui_chair)
        end
    elseif daoInx == 4 then
        point = self.totalPoint[server_chair + 1]
        if #self.paixingAnimate3 > 0 then
            for k,v in pairs(self.paixingAnimate3) do
                v:removeFromParentAndCleanup(true)
                v = nil
            end
        end
        self.paixingAnimate3 = {}
        --self.playerResults[ui_chair].Image_card_type:setVisible(false)
        for i=1,3 do
            --self.playerResults[ui_chair].Panel_text[i]:setVisible(false)
        end

    end
    local text = ""
    if point == 0 then
        --self.playerResults[ui_chair].Panel_text[daoInx]:setTextColor(cc.c3b(255,255,255))

        text =  tostring(point)
        
        self.playerResults[ui_chair].Panel_text[daoInx]:setProperty(text,
                     "game_res/"..GAME_ID.."/ui_res/result/num_win.png",
                     28,
                     44,
                     '+')
    elseif point > 0 then

        text =  self:PointToDisplayStr(point)
        text = tostring("+" .. text)
        self.playerResults[ui_chair].Panel_text[daoInx]:setProperty(text ,
                     "game_res/"..GAME_ID.."/ui_res/result/num_win.png",
                     28,
                     44,
                     '+')
    elseif point < 0 then

        text = self:PointToDisplayStr(point)
        self.playerResults[ui_chair].Panel_text[daoInx]:setProperty(text,
                     "game_res/"..GAME_ID.."/ui_res/result/num_lose.png",
                     28,
                     44,
                     '+')
    end
    self.playerResults[ui_chair].Panel_text[daoInx]:setVisible(true)
    --self.playerResults[ui_chair].Panel_text[daoInx]:setText(tostring(text))
end

function ResultUIManager:PointToDisplayStr(point)
    local text = ""
   
    local gameSceneTmp =  GameSceneModule:getInstance():getGameScene()
    text = gameSceneTmp:getMoneyText(point)
    return text
end

function ResultUIManager:showPoint(daoInx)
    local PlayersInfos = GameSceneModule:getInstance():getGameScene().PlayersInfos
    for _,v in pairs(PlayersInfos) do
        local server_chair = v.bDeskStation
        local ui_chair = ViewHelp.getUIChairIndexByServerChair(server_chair)
        local point = 0
        if daoInx == 1 then
            point = self.touPoint[server_chair + 1]
        elseif daoInx == 2 then
            point = self.zhongPoint[server_chair + 1]
        elseif daoInx == 3 then
            point = self.weiPoint[server_chair + 1]
        elseif daoInx == 4 then
            --point = self.touPoint[server_chair + 1]+self.zhongPoint[server_chair + 1]+self.weiPoint[server_chair + 1]
            point = self.totalPoint[server_chair + 1]
            --self.displayPoint[ui_chair] = point
        end
        local text = self:PointToDisplayStr(point)
        local fontpath = "game_res/"..GAME_ID.."/ui_res/result/num_lose.png"
        if point >= 0 then
            fontpath = "game_res/"..GAME_ID.."/ui_res/result/num_win.png"
        end

        if point > 0  then
            text = tostring("+" .. text)
        end
        self.playerResults[ui_chair].Panel_text[daoInx]:setProperty(text,fontpath,28,44,'+')
        self.playerResults[ui_chair].Panel_text[daoInx]:setVisible(true)
    end 
end
-- 显示一道的牌
function ResultUIManager:showOneDaoCard(daoData,daoCard,svrChair)
    if daoData == nil or daoCard == nil then
        return
    end

    for i=1,#daoCard do
        self:turnCardToFront(daoData[i],daoCard[i],svrChair)
        --self:setOneCardValue(daoData[i],daoCard[i],svrChair)
    end

end

-- 翻转到正面
function ResultUIManager:turnCardToFront(cardVal,cardObj,svrChair)
 
    local time = 0.15
    local turnAction1 = cc.OrbitCamera:create(time,10,0,0,90,0,0)
    local turnAction2 = cc.CallFunc:create(
        function()
            cardObj:runAction(cc.Sequence:create(
                                    cc.Spawn:create( 
                                        cc.CallFunc:create(
                                            function ()
                                                self:setOneCardValue(cardVal,cardObj,svrChair)
                                            end),
                                        cc.OrbitCamera:create(time,10,0,270,90,0,0),
                                        cc.ScaleTo:create(0.2,1.5,1.5)),
                                    cc.DelayTime:create(0.25),
                                    cc.ScaleTo:create(time,1.0,1.0)
                          ))
        end
    )
    local sequence = cc.Sequence:create(turnAction1,turnAction2)
    cardObj:runAction(sequence)
end

-- 设置头中尾道中的一个牌值
function ResultUIManager:setOneCardValue(value, card, svrChair)
    local Image_MaTip = card:getChildByName("Image_MaTip")
    local Image_Kuang = card:getChildByName("Image_Kuang")
    local Image_THSPTip = card:getChildByName("Image_THSPTip")
    if value == 0 then
        --0 为牌背
        Image_MaTip:setVisible(false)
        Image_Kuang:setVisible(false)
        Image_THSPTip:setVisible(false)
        local CardNameTab = {"card_R.png","card_G.png","card_B.png"}
        local path = "res/game_res/"..GAME_ID.."/ui_res/baipai/card/"..CardNameTab[UserData:getCardColor()]
        card:loadTexture(path)
        return
    end

    if ViewHelp.IsMapai(value) then
        Image_MaTip:setVisible(true)
        Image_Kuang:setVisible(true)
    else
        Image_MaTip:setVisible(false)
        Image_Kuang:setVisible(false)
    end

    --鬼牌癞子标识
    --[[
    if ViewHelp.IsGuiCards(value) then
        Image_Kuang:setVisible(true)
    else
        Image_Kuang:setVisible(false)
    end
    --]]
    --同花順+癩子標識
    
    local bthspCard = false
    if self.thspCards and self.thspCards[svrChair+1] and #self.thspCards[svrChair+1] then
        local cards = self.thspCards[svrChair+1]
        for _,v in pairs(cards) do
            if v == value then
                bthspCard = true
                break
            end
        end
    end
    Image_THSPTip:setVisible(bthspCard)


    local num = value % 13
    if num == 0 then
        num = 13
    end

    --local path = "res/game_res/"..GAME_ID.."/ui_res/baipai/deskCard/card_" ..(math.ceil(value / 13) -1) .. "_" .. num .. ".png"
    local path = "res/game_res/"..GAME_ID.."/ui_res/baipai/card/card_" ..(math.ceil(value / 13) -1) .. "_" .. num .. ".png"
    card:loadTexture(path)
end

-- 显示玩家摆牌，隐藏手牌
function ResultUIManager:showHandCard(ui_chair, isShow)

    for k,v in pairs(self.image_card_tou[ui_chair]) do
        v:setVisible(not isShow)
    end
    for k,v in pairs(self.image_card_zhong[ui_chair]) do
        v:setVisible(not isShow)
    end
    for k,v in pairs(self.image_card_wei[ui_chair]) do
        v:setVisible(not isShow)
    end

--    if ui_chair == 1 then
--        return
--    end

    self.playerResults[ui_chair].Image_Finish:setVisible(not isShow)

--    for i = 1, 13 do
--        self.playerResults[ui_chair].Panel_hand_card[i]:setVisible(isShow)
--    end

end

function ResultUIManager:CleanPxAnimation()
    if #self.paixingAnimate1 > 0 then
        for k,v in pairs(self.paixingAnimate1) do
            v:removeFromParentAndCleanup(true)
            v = nil
        end
    end
    self.paixingAnimate1 = {}

    if #self.paixingAnimate2 > 0 then
        for k,v in pairs(self.paixingAnimate2) do
            v:removeFromParentAndCleanup(true)
            v = nil
        end
    end
    self.paixingAnimate2 = {}

    if #self.paixingAnimate3 > 0 then
        for k,v in pairs(self.paixingAnimate3) do
            v:removeFromParentAndCleanup(true)
            v = nil
        end
    end
    self.paixingAnimate3 = {}

    if #self.paixingAnimate4 > 0 then
        for k,v in pairs(self.paixingAnimate4) do
            v:removeFromParentAndCleanup(true)
            v = nil
        end
    end
    self.paixingAnimate4 = {}
end
-- 加分动画
function ResultUIManager:addPointAnimate(resp_json)
    if ViewHelp:getGameStation() ~= GameStation.GS_RESULT then
        return 
    end
    -- 播放赢了输了音效
    local server_chair = ViewHelp.getServerChairByUIChair(1)
    local score = self.totalPoint[server_chair + 1]
    if score > 0 then
        GameMusic:playWinEffect()
    elseif score < 0 then
        GameMusic:playFailureEffect()
    end


    self:CleanPxAnimation()
    
    local server_chair = ViewHelp.getBasePosChair()
    local point = self.totalPoint[server_chair + 1]
    local pointstr = self:PointToDisplayStr(point)
    local imageBg = ccui.ImageView:create("res/game_res/"..GAME_ID.."/ui_res/result/totalPointBg.png")
    local fontpath = "game_res/"..GAME_ID.."/ui_res/result/num_lose.png"
    if point >= 0 then
        fontpath = "game_res/"..GAME_ID.."/ui_res/result/num_win.png"
    end

    if point > 0 then
        pointstr = tostring("+" .. pointstr)
    end
    local pointLabel = CCLabelAtlas:create(pointstr, fontpath, 28, 44, string.byte('+'))
    imageBg:addChild(pointLabel)
    imageBg:setPosition(cc.p(640,360))
    pointLabel:setPosition(imageBg:convertToNodeSpace(cc.p(640,360)))
    self.ui_project:addChild(imageBg)
    --pointLabel:setScale(0.8)
    pointLabel:setAnchorPoint(cc.p(0.5, 0.5))
    imageBg:setScale(0.5)
    local scaleto = cc.ScaleTo:create(0.5,1)
    local fadein = cc.FadeIn:create(0.5)
    local spawn = cc.Spawn:create(scaleto, fadein)
    local delay = cc.DelayTime:create(3)
    local fadeOut = cc.FadeOut:create(1)
    local callback = cc.CallFunc:create(function()
         imageBg:removeFromParent()
         end)
    local sequence = cc.Sequence:create(spawn,delay,fadeOut,callback)
    imageBg:runAction(sequence)
--    local PlayersInfos = GameSceneModule:getInstance():getGameScene().PlayersInfos
--    for i=1,#PlayersInfos do
--        local player_info = PlayersInfos[i]
--        local server_chair = player_info.bDeskStation
--        local ui_chair = ViewHelp.getUIChairIndexByServerChair(server_chair)
----        for j = 1, 4 do
----            self.playerResults[ui_chair].Panel_text[j]:setVisible(false)
----            self.playerResults[ui_chair].Image_special:setVisible(false)
----        end

----        for k, v in pairs(self.image_card_tou[ui_chair]) do
----            v:setVisible(false)
----        end
----        for k, v in pairs(self.image_card_zhong[ui_chair]) do
----            v:setVisible(false)
----        end
----        for k, v in pairs(self.image_card_wei[ui_chair]) do
----            v:setVisible(false)
----        end

--        local point = 0
--        local text = ""
--        point = self.totalPoint[server_chair + 1]
--        if point == 0 then
--            self.playerResults[ui_chair].Text_allsocre:setTextColor(cc.c3b(255, 255, 255))
--            text = "+" .. point
--        elseif point > 0 then
--            self.playerResults[ui_chair].Text_allsocre:setTextColor(cc.c3b(255, 255, 0))
--            text = "+" .. point
--        elseif point < 0 then
--            self.playerResults[ui_chair].Text_allsocre:setTextColor(cc.c3b(255, 0, 0))
--            text = point
--        end
--        self.playerResults[ui_chair].Text_allsocre:setText(tostring(text))

--        --self.playerResults[ui_chair].Text_allsocre:setVisible(true)
--        self.playerResults[ui_chair].Text_allsocre:setPositionY(-200)
--        local position_x = self.playerResults[ui_chair].Text_allsocre:getPositionX()
--        local moveTo = cc.MoveTo:create(0.5, cc.p(position_x, total_score_position_y[ui_chair]))
--        local easeOut = cc.EaseOut:create(moveTo, 0.5)
--        --self.playerResults[ui_chair].Text_allsocre:runAction(easeOut)

--        local callback = function()
--            self.playerResults[ui_chair].Text_allsocre:setVisible(false)
--            --self:ui_close()

--            GameSceneModule:getInstance():getGameScene().GameLayer.PlayersHeadUIManager:updateAllPlayerScore()

--            if resp_json.iRoundTimes < resp_json.iAllRoundTimes then
--                for j = 1, 4 do
--                    -- GameSceneModule:getInstance():getGameScene().GameLayer.PlayersHeadUIManager:ShowReadImage(j, true)
--                end
--            end

--        end
--        performWithDelay(self.ui_project, callback, 2.5)

--        local callback2 = function()
--           -- api_show_tips("点击任意地方开始游戏！")
--            self.isBegin = true
--            --local request = { }
--            --handleGameMessage.SendGameMessage(handleGameMessage.Table_GameMessage_ASSID.ASS_GM_AGREE_GAME, request)
--        end
--        if resp_json.iRoundTimes < resp_json.iAllRoundTimes then
--            performWithDelay(self.ui_project, callback2, 2)
--        end


--    end

end

-- 界面初始化
function ResultUIManager:setInit()
    self.ui_project:stopAllActions()
    self.isBegin = false
    local PlayersInfos = GameSceneModule:getInstance():getGameScene().PlayersInfos
    self:updatePlayerInfo()
    self.Panel_Resume:setVisible(false)
    for i = 1, #PlayersInfos do
        local player_info = PlayersInfos[i]
        local server_chair = player_info.bDeskStation
        local ui_chair = ViewHelp.getUIChairIndexByServerChair(server_chair)
        for j = 1, 4 do
            self.playerResults[ui_chair].Panel_text[j]:setVisible(false)
            --self.playerResults[ui_chair].Image_special:setVisible(false)
        end

        for k, v in pairs(self.image_card_tou[ui_chair]) do
            v:setVisible(false)
        end
        for k, v in pairs(self.image_card_zhong[ui_chair]) do
            v:setVisible(false)
        end
        for k, v in pairs(self.image_card_wei[ui_chair]) do
            v:setVisible(false)
        end
    end

    self:ui_close()

end

function ResultUIManager:getTime()
    return self.time
end

--断线重连处理
function ResultUIManager:onGameStation(resp_json)
    print("--- ResultUIManager:onGameStation start")
    self.time = 0
    self:updatePlayerInfo()

    if resp_json.ShootSpeed ~= nil then
        self.shootSpeed = resp_json.ShootSpeed
    end

    if resp_json.GSID == GameStation.GS_PLAYING then
        local PlayersInfos = GameSceneModule:getInstance():getGameScene().PlayersInfos
        for i = 1, #PlayersInfos do
            local player_info = PlayersInfos[i]
            local ui_chair = ViewHelp.getUIChairIndexByServerChair(player_info.bDeskStation)
            --self.playerResults[ui_chair].Image_card_type:setPositionY(cardType_position_y[ui_chair])
            --self:showHandCard(ui_chair, true)
        end
        if resp_json.BaiPaiOK then
            self:ui_open()
            self:onBaipaiResult(resp_json)
        end
    elseif resp_json.GSID == GameStation.GS_RESULT then
        
        self.touPoint = resp_json.touPoint
        self.zhongPoint = resp_json.zhongPoint
        self.weiPoint = resp_json.weiPoint
        self.totalPoint = resp_json.totalPoint

        self.touData = resp_json.touData
        self.zhongData = resp_json.zhongData
        self.weiData = resp_json.weiData
        self.daoCardType = resp_json.daoCardType
        self.thspCards = resp_json.thspCards
        local PlayersInfos = GameSceneModule:getInstance():getGameScene().PlayersInfos
        for _, playerInfo in pairs(PlayersInfos) do
            local server_chair = playerInfo.bDeskStation
            local ui_chair = ViewHelp.getUIChairIndexByServerChair(server_chair)
            self:showOneDaoCard(self.weiData[server_chair + 1], self.image_card_wei[ui_chair], server_chair)
            self:showOneDaoCard(self.zhongData[server_chair + 1], self.image_card_zhong[ui_chair], server_chair)
            self:showOneDaoCard(self.touData[server_chair + 1], self.image_card_tou[ui_chair], server_chair)
            for k, v in pairs(self.image_card_tou[ui_chair]) do
                v:setVisible(true)
            end
            for k, v in pairs(self.image_card_zhong[ui_chair]) do
                v:setVisible(true)
            end
            for k, v in pairs(self.image_card_wei[ui_chair]) do
                v:setVisible(true)
            end
            self:showCardType(server_chair, ui_chair, 1)
            self:showCardType(server_chair, ui_chair, 2)
            self:showCardType(server_chair, ui_chair, 3)
            self:showCardType(server_chair, ui_chair, 4)
        end
        --self:showPoint(4)
        self:ui_open()
        GameSceneModule:getInstance():getGameScene().GameLayer.PlayersHeadUIManager:updateAllPlayerScore()
    elseif resp_json.GSID == GameStation.GS_WAIT_NEXT_ROUND then

    end

    print("--- ResultUIManager:onGameStation end")
end

function ResultUIManager:ui_open()
    self.ui_project:setVisible(true)
end

function ResultUIManager:ui_close()
    self.ui_project:setVisible(false)
end

function ResultUIManager:updatePlayerInfo()
--    for i = 1, 4 do
--       -- self:setHeadAtIndexVisible(i,false)
--       local panel_play = self.Panel_Resume:getChildByName("Panel_player_"..i)
--       panel_play:setVisible(false)
--    end
--    local PlayersInfos = GameSceneModule:getInstance():getGameScene().PlayersInfos
--    local userNums = #PlayersInfos
--    local pos ={}
--    if userNums == 2 then
--        pos= {494, 691,0,0}
--    elseif userNums == 3 then
--        pos = {466, 588, 712,0}
--    elseif userNums == 4 then 
--        pos = {438.19, 540.27, 642.36,744.44}
--    end

--    for i = 1, userNums do
--        local player_info = PlayersInfos[i]
--        local panel_play = self.Panel_Resume:getChildByName("Panel_player_"..i)
--        panel_play:setVisible(true)
--        if userNums <=4 and userNums > 1 then
--            panel_play:setPositionX(pos[i])
--        end
--        if player_info ~= nil then
--            -- 更新头像

--            local Image_head_wx = panel_play:getChildByName("Image_head_wx")
--            local Text_name = panel_play:getChildByName("Text_name")
--            Text_name:setText(api_get_ascll_sub_str_by_ui(player_info.nickName,8))
--            m_download:get_instance():set_head_image_and_auto_update(Image_head_wx, player_info.avatarUrl, tostring(player_info.dwUserID))
--        end
--    end
end

--打枪或全垒打后分数变化
function ResultUIManager:ChangePoint(win_ui_chair, lose_ui_chair, point)
    --self.playerResults[ui_chair].Panel_text[daoInx]
    local function changeAni(object)
        object:stopAllActions()
        local delayBefore = cc.DelayTime:create(0.2)
        local scaleto1 = cc.ScaleTo:create(0.25,1)
        local scaleto2 = cc.ScaleTo:create(0.5,0.8)
        local delay = cc.DelayTime:create(0.5)
        local callback = cc.CallFunc:create(function()
                end)
        local sequence = cc.Sequence:create(delayBefore,scaleto1,delay,scaleto2,callback)
        object:runAction(sequence)
    end

    local function SetPointString(ui_chair, point)
        local pointStr = self:PointToDisplayStr(point)
        if point > 0  then
            pointStr = tostring("+" .. pointStr)
        end
        local fontpath = "game_res/"..GAME_ID.."/ui_res/result/num_lose.png"
        if point >= 0 then
            fontpath = "game_res/"..GAME_ID.."/ui_res/result/num_win.png"
        end
        self.playerResults[ui_chair].Panel_text[4]:setProperty(pointStr,fontpath,28,44,'+')
        changeAni(self.playerResults[ui_chair].Panel_text[4])
    end
    self.displayPoint[win_ui_chair] = self.displayPoint[win_ui_chair] + point
    SetPointString(win_ui_chair, self.displayPoint[win_ui_chair])

    self.displayPoint[lose_ui_chair] = self.displayPoint[lose_ui_chair] - point
    SetPointString(lose_ui_chair, self.displayPoint[lose_ui_chair])

end
function ResultUIManager:GetShootRedwaveChangePoint(fromServerChair, toServerChair, actType)
    local point = 0
    local touDaoType = self.daoCardType[fromServerChair  + 1][1]
    local zhongDaoType = self.daoCardType[fromServerChair  + 1][2]
    local weiDaoType = self.daoCardType[fromServerChair  + 1][3]
    if actType == ACT_SHOOT then
       --打枪变化的分数
       -- self.daoCardType[server_chair  + 1][daoInx]
       point = 3
       if touDaoType == CardType.CT_THREE then
           point = point + 1
       end
       if zhongDaoType == CardType.CT_TONGHUASZ or zhongDaoType == CardType.CT_TIEZHI or zhongDaoType == CardType.CT_GOURD then
           point = point + 1
       end
       if weiDaoType == CardType.CT_TONGHUASZ or weiDaoType == CardType.CT_TIEZHI then
           point = point + 1
       end
    elseif actType == ACT_REDWAVE then
        --红波浪变化的分数
        local bGoldRoom = GameSceneModule:getInstance():getGameScene():IsGoldRoom()
        local PlayersInfos = GameSceneModule:getInstance():getGameScene().PlayersInfos
        local PlayerCount = #PlayersInfos
        if GameSceneModule:getInstance():getGameScene():HasDaqiang() then
            --有打枪，红波浪直接在打枪的基础上加
            if PlayerCount == 4 then
                point = 7
            elseif PlayerCount == 3 then
                point = 4
            end
        else
            if PlayerCount == 4 then
                point = 10
            elseif PlayerCount == 3 then
                point = 7
            end
            if touDaoType == CardType.CT_THREE then
                point = point + 1
            end
            if zhongDaoType == CardType.CT_TONGHUASZ or zhongDaoType == CardType.CT_TIEZHI or zhongDaoType == CardType.CT_GOURD then
                point = point + 1
            end
            if weiDaoType == CardType.CT_TONGHUASZ or weiDaoType == CardType.CT_TIEZHI then
                point = point + 1
            end
        end
        
        
    end

--    if self:IsUserHasMapai(fromServerChair) or self:IsUserHasMapai(toServerChair) then
--        point = point * 2
--    end
    return point
end

function ResultUIManager:IsUserHasMapai(serverChair)
    local mapai = ViewHelp.GetMapai()
    --头道
    for _,v in pairs(self.touData[serverChair+1]) do
        if mapai == v then
            return true
        end
    end
    --中道
    for _,v in pairs(self.zhongData[serverChair+1]) do
        if mapai == v then
            return true
        end
    end
    --尾道
    for _,v in pairs(self.weiData[serverChair+1]) do
        if mapai == v then
            return true
        end
    end
    return false
end

return ResultUIManager

--endregion
