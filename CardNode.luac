--region *.lua
--Date
--此文件由[BabeLua]插件自动生成

CARD_POSITION_Y_UP = 140               -- 点击时牌的y坐标
CARD_POSITION_Y_DOWN = 110             -- 原来的y坐标

local CardNode = class("CardNode",function ()
    return cc.CSLoader:createNode("ui_csb/CardNode.csb")
    --return ccui.ImageView:create()
end)

function CardNode:ctor(parent,HandCard)
    print("--- CardNode:ctor")
    self.parent = parent
    self.HandCard = HandCard

    self.isUp = false
    self.Value = 0

    self.isAction = false

    --self:loadTexture("res/game_res/"..GAME_ID.."/ui_res/baipai/card/card_0_1.png")
    self:setScale(1.1)
    self:setVisible(false)
    parent:addChild(self)

    self.Image_Card = self:getChildByName("Image_Card")
    self.Image_WangCover = self.Image_Card:getChildByName("Image_WangCover")
    self.Image_Kuang = self.Image_Card:getChildByName("Image_Kuang")
    self.Image_MaTip = self.Image_Card:getChildByName("Image_MaTip")
    self.Image_THSPTip = self.Image_Card:getChildByName("Image_THSPTip")
--    --阴影层
--    self.shadeImage = ccui.ImageView:create("res/game_res/"..GAME_ID.."/ui_res/baipai/card/shade.png")
--    self.shadeImage:setScale(0.67)
--    self.shadeImage:setPosition(50,67)
--    self:addChild(self.shadeImage)
--    self.shadeImage:setVisible(false)


    local touch_listener = cc.EventListenerTouchOneByOne:create()
    touch_listener:setSwallowTouches(true) 
    touch_listener:registerScriptHandler(handler(self, self.onTouchBegan), cc.Handler.EVENT_TOUCH_BEGAN)  
    touch_listener:registerScriptHandler(handler(self, self.onTouchMoved), cc.Handler.EVENT_TOUCH_MOVED)
    touch_listener:registerScriptHandler(handler(self, self.onTouchEnded), cc.Handler.EVENT_TOUCH_ENDED)  
    local eventDispatcher = cc.Director:getInstance():getEventDispatcher()      
    eventDispatcher:addEventListenerWithSceneGraphPriority(touch_listener, self.Image_Card) 

end

function CardNode:onTouchBegan(touch, event)
    if self:isVisible() == false then
        return false
    end
    --print("CardNode:onTouchBegan")

    -- 判断是否可以点击
--    local GameScene = GameSceneModule:getInstance():getGameScene()
--    if GameScene.GameLayer.HandCardUIManager:isCanTouch() == false then
--        return false
--    end

    self.isAction = false

    local len = 37
    if self == self.HandCard[#self.HandCard] then
        len = 0
    end
    local p = self.Image_Card:convertToNodeSpace(touch:getLocation())
    local rect = cc.rect(2, 0, self.Image_Card:getContentSize().width - len, self.Image_Card:getContentSize().height)
    if cc.rectContainsPoint(rect, p) then
        self.isAction = true
        self.start_touch_pos = touch:getLocation()
        return true
    end
    
end

function CardNode:onTouchMoved(touch, event)
    local p = self.Image_Card:convertToNodeSpace(touch:getLocation())
    self:isSelected(self.start_touch_pos,touch:getLocation())
    
end

function CardNode:onTouchEnded(touch, event)
    local handCard = self.HandCard
    for i = 1, #handCard do
        if handCard[i]:isVisible() then
            if handCard[i].isAction then
                if handCard[i].isUp then
                    handCard[i]:down()
                else
                    handCard[i]:up()
                end
            end

            handCard[i].isAction = false
            --handCard[i].shadeImage:setVisible(false)
            handCard[i]:setColor(NORMAL_COLOR)
        end
    end

end

-- 检测每个牌是否被选中
function CardNode:isSelected(start_touch_pos, cur_touch_pos)
    local handCard = self.HandCard
    local x1, x2 = 0, 0
    for i = 1, #handCard do
        if handCard[i]:isVisible() then
            local len = 37
            if handCard[i] == handCard[#self.HandCard] then
                len = 0
            end
            local start_pos = handCard[i].Image_Card:convertToNodeSpace(start_touch_pos)
            local rect = cc.rect(2, 0, handCard[i].Image_Card:getContentSize().width - len, handCard[i].Image_Card:getContentSize().height)
            if cc.rectContainsPoint(rect, start_pos) then
                x1 = i
            end

            local cur_pos = handCard[i].Image_Card:convertToNodeSpace(cur_touch_pos)
            local rect = cc.rect(5, 0, handCard[i].Image_Card:getContentSize().width - 5, handCard[i].Image_Card:getContentSize().height)
            if cc.rectContainsPoint(rect, cur_pos) then
                x2 = i
            end
        end
    end

    for i = 1, #handCard do
        if handCard[i]:isVisible() then
            local cur_pos = handCard[i].Image_Card:convertToNodeSpace(cur_touch_pos)
            local rect = cc.rect(5, 0, handCard[i].Image_Card:getContentSize().width - 5, handCard[i]:getContentSize().height)
            if cc.rectContainsPoint(rect, cur_pos) then
                for j = 1, #handCard do
                    if handCard[j]:isVisible() then
                        handCard[j].isAction = false
                        --handCard[j].shadeImage:setVisible(false)
                        handCard[j]:setColor(NORMAL_COLOR)
                    end
                end

                break
            end
        end
    end



    if x2 > 0 then
        if x1 >= x2 then
            for i = x2, x1 do
                handCard[i].isAction = true
                --handCard[i].shadeImage:setVisible(true)
                handCard[i]:setColor(SELECTED_COLOR)
            end
        elseif x2 >= x1 then
            for i = x1, x2 do
                handCard[i].isAction = true
                --handCard[i].shadeImage:setVisible(true)
                handCard[i]:setColor(SELECTED_COLOR)
            end
        end
    end

end

--设置牌的牌面
function CardNode:setCardImage(value)
    --马牌标识
    if ViewHelp.IsMapai(value) then
        self.Image_MaTip:setVisible(true)
        self.Image_Kuang:setVisible(true)
    else
        self.Image_MaTip:setVisible(false)
        self.Image_Kuang:setVisible(false)
    end
    --[[
    --鬼牌癞子标识
    if ViewHelp.IsGuiCards(value) then
        self.Image_Kuang:setVisible(true)
    else
        self.Image_Kuang:setVisible(false)
    end
    --]]
    --同花順+癩子標識
    if ViewHelp.IsTHSLaizi(value) then
        self.Image_THSPTip:setVisible(true)
    else
        self.Image_THSPTip:setVisible(false)
    end

    local num = value % 13
    if num == 0 then
        num = 13
    end
    local path = "res/game_res/"..GAME_ID.."/ui_res/baipai/card/card_" ..(math.ceil(value / 13) -1) .. "_" .. num .. ".png"
    self.Image_Card:loadTexture(path)
    self.Value = value
end

function CardNode:up()
    --self:setPositionY(CARD_POSITION_Y_UP)
    self.isUp = true

    GameSceneModule:getInstance():getGameScene().GameLayer.BaiPaiUIManager:setCardUp(self)
end

function CardNode:down()
    --self:setPositionY(CARD_POSITION_Y_DOWN)
    self.isUp = false

    GameSceneModule:getInstance():getGameScene().GameLayer.BaiPaiUIManager:setCardDown(self)
end

return CardNode

--endregion
