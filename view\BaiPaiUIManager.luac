--region *.lua
--Date
--此文件由[BabeLua]插件自动生成

--import("..GameDefine")
local GameMusic = import("..GameMusic")
local GameLogic = import('..GameLogic')
local CardNode = import("..CardNode")
local Msg = import("....ui.Msg")
local BaiPaiUIManager = class("BaiPaiUIManager")
local AUTO_BAIPAI_ITEMNUM = 15

local Card_position_x = {}                   -- 手牌最终x坐标
local Card_position_y = {}                   -- 手牌y坐标
local INTERVAL_TIME = 0.15                   -- 手牌动画间隔时长
local CARDWIDE = 90                          -- 手牌间距   
local SELECT_COLOR = cc.c3b(56,246,56)       -- 点亮时颜色
local NORMAL_COLOR = cc.c3b(255,255,255)     -- 正常颜色





function BaiPaiUIManager:ctor(ui_project, ui_root)
    self.ui_project = ui_project
    self.ui_root = ui_root

    self.x1 = 0
    self.x2 = 0

    --选中牌的信息
    self.selectedCardInfo={
        daoType = 0,
        cardIndex = 0
    }

    if  ui_root.niaoNum  then
        GameLogic:setGuiNum(ui_root.niaoNum)
    end
    -- 玩家手牌值
    self.handCardValues = {}

    -- 牌型数据
    self.cardTypeData = {}

    -- 头 中 尾道摆牌数据
    self.touData = {}
    self.zhongData = {}
    self.weiData = {}

    -- 头中尾道和特殊牌牌型
    self.daoCardType = {0,0,0,0}
    self.bAddPoint = false

    -- 头中尾道牌型中的最大牌值
    self.compareVal = {0,0,0,0}

    -- 已显示的手牌数
    self.showHandCardsNums = 0
    
    -- 发牌是否完成
    self.isOver = false

    self.sendCardSpeed = 100

    -- 头中尾道是否点亮
    self.daoIsBright = {false,false,false}

    -- 头中尾道是否已经完成摆牌
    self.daoIsComplete = {false,false,false}

    -- 手牌排序默认大小排序
    self.isSize = true

    -- 倒计时
    self.BaiPaiTime = 20
    self.curCountDountTime = self.BaiPaiTime*10
   
    -- 1.对子 2.两对 3.三条 4.顺子 5.同花 6.葫芦 7.铁支 8.同花顺 
    self.Panel_button_paixing = ui_project:getChildByName("Panel_button_paixing")
    self.Button_px = {}
    for i = 1,9 do
        self.Button_px[i] = self.Panel_button_paixing:getChildByName("Button_" .. i)
        self.Button_px[i]:setEnabled(false)
    end
    self:addButtonListener()
    
    -- 手牌
    local Panel_handcards = ui_project:getChildByName("Panel_handcards")
    self.handCards = {}
    --self.cardShade = {}

    for i = 1,13 do
        self.handCards[i] = CardNode:create(Panel_handcards,self.handCards)
        self.handCards[i]:setVisible(false)
    end

    local start_position_x = 640
    start_position_x = start_position_x - (math.ceil(13 / 2) - 1) * CARDWIDE 
    for i = 1,13 do
        self.handCards[i]:setPositionX(start_position_x)
        start_position_x = start_position_x + CARDWIDE
        Card_position_x[i] = self.handCards[i]:getPositionX()
        Card_position_y[i] = self.handCards[i]:getPositionY()
    end

    -- 点击 滑牌
    --self:addTouch()

    self.Panel_paixing = ui_project:getChildByName("Panel_paixing")
    local Image_bj = self.Panel_paixing:getChildByName("Image_bj")

    -- 特殊牌型
    self.Text_s_cardtype = Image_bj:getChildByName("Text_s_cardtype")
    self.Text_s_cardtype:setVisible(false)
    self.Text_card_type = Image_bj:getChildByName("Text_card_type")
    self.Text_card_type:setVisible(false)

    -- 头 中 尾 道
    self.Image_tou = Image_bj:getChildByName("Image_tou")
    self.Image_zhong = Image_bj:getChildByName("Image_zhong")
    self.Image_wei = Image_bj:getChildByName("Image_wei")
    self.Image_tou:setVisible(false)
    self.Image_zhong:setVisible(false)
    self.Image_wei:setVisible(false)
    -- 倒计时
    self.Text_timer = Image_bj:getChildByName("Text_timer")

--    self.Panel_TimeLoad = Image_bj:getChildByName("Panel_TimeLoad")
--    self.Text_Time = self.Panel_TimeLoad:getChildByName("Text_Time")
--    self.Image_LoadingBarBg = self.Panel_TimeLoad:getChildByName("Image_LoadingBarBg")
--    self.LoadingBar_Time = self.Panel_TimeLoad:getChildByName("LoadingBar_Time")
--    self.Panel_TimeLoad:setVisible(false)

    -- 清除按钮
    self.button_close = {}
    for i=1,3 do
        self.button_close[i] = Image_bj:getChildByName("Button_close_" .. i)
        self.button_close[i]:setVisible(false)
        self.button_close[i]:addClickEventListener(function ()
            GameMusic:playClickEffect()
            self:cancelCallBack(i)
        end)
    end

    -- 报道
    self.Button_baodao = Image_bj:getChildByName("Button_baodao")
    self.Button_baodao:setEnabled(false)
    self.Button_baodao:addClickEventListener(function()
        GameMusic:playClickEffect()
        self:baodaoCallBack()
        
    end)

    self.Button_spcial_card_type = Image_bj:getChildByName("Button_spcial_card_type")
    self.Button_spcial_card_type:setVisible(false)
    self.Button_spcial_card_type:addClickEventListener(function()
        GameMusic:playClickEffect()
        self:baodaoCallBack()
    end)

    -- 大小
    self.Button_dx = Image_bj:getChildByName("Button_dx")
    self.Button_dx:setVisible(false)
    self.Button_dx:addClickEventListener(function ()
        GameMusic:playClickEffect()
        self:sortCallback()
    end)

    -- 全部取消
    self.Button_allclose = Image_bj:getChildByName("Button_allclose")
    self.Button_allclose:setVisible(false)
    self.Button_allclose:addClickEventListener(function ()
        GameMusic:playClickEffect()
        self:cancelCallBack(4)

    end)

    -- 提交
    self.Button_refer = Image_bj:getChildByName("Button_refer")
    self.Button_refer:setVisible(false)
    self.Button_refer:addClickEventListener(function()
        GameMusic:playClickEffect()
        self:referCallBack()
    end)

--    --摆牌按钮
--    self.Button_TouD = self.Panel_paixing:getChildByName("Button_TouD")
--    self.Button_ZhongD = self.Panel_paixing:getChildByName("Button_ZhongD")
--    self.Button_WeiD = self.Panel_paixing:getChildByName("Button_WeiD")
--    local btnTab = {self.Button_TouD,self.Button_ZhongD,self.Button_WeiD}
--    for k,v in pairs(btnTab) do
--        v:addClickEventListener( function()
--             local upCards = GameLogic:getUpCardData(self.handCardValues,self.handCards)
--             self:AddDaoCardFromUpList(k,upCards)
--        end )
--    end
    

    -- 头中尾道牌背景
    self.image_card_tou_bj = {}
    self.image_card_zhong_bj = {}
    self.image_card_wei_bj = {}
    local Panel_dao_bj = Image_bj:getChildByName("Panel_dao_bj")
    for i=1,13 do
        local image = Panel_dao_bj:getChildByName("Image_" .. i)
        self:addTouchToDaoCard_bj(image)
        if i >= 1 and i <= 3 then
            table.insert(self.image_card_tou_bj,image)
        elseif i >= 4 and i <= 8 then
            table.insert(self.image_card_zhong_bj,image)
        else
            table.insert(self.image_card_wei_bj,image)
        end
    end

    -- 头中尾道牌
    self.image_card_tou = {}
    self.image_card_zhong = {}
    self.image_card_wei = {}
    local Panel_dao_card = Image_bj:getChildByName("Panel_dao_card")
    for i=1,13 do
        local cardNode = Panel_dao_card:getChildByName("CardNode_" .. i)
        local Image_Card = cardNode:getChildByName("Image_Card")
        --Image_Card:setScale(1.05)
        Image_Card:setVisible(false)
        if i >= 1 and i <= 3 then
            table.insert(self.image_card_tou,Image_Card)
        elseif i >= 4 and i <= 8 then
            table.insert(self.image_card_zhong,Image_Card)
        else
            table.insert(self.image_card_wei,Image_Card)
        end
    end

    self.Panel_AutoBaipai = ui_project:getChildByName("Panel_AutoBaipai")
    self.ListView_Px = self.Panel_AutoBaipai:getChildByName("ListView_Px")
    self.Image_PxItem = self.Panel_AutoBaipai:getChildByName("Image_PxItem")
    self.Image_PxItem:setVisible(false)
    self.Panel_AutoBaipai:setVisible(false)

    self.baipaiMode = 0 --0:手动 ，1:自动
    local Button_BaipaiMode = ui_project:getChildByName("Button_BaipaiMode")
    self.Button_BaipaiMode = Button_BaipaiMode
    Button_BaipaiMode:addClickEventListener(function()
            if self.baipaiMode==0 then
                self.baipaiMode = 1
                Button_BaipaiMode:loadTextureNormal("res/game_res/"..GAME_ID.."/ui_res/baipai/auto.png")
            else
                self.baipaiMode = 0
                Button_BaipaiMode:loadTextureNormal("res/game_res/"..GAME_ID.."/ui_res/baipai/shoudong.png")
            end
            self:cancelCallBack(4)
            self:UpdateBaiPaiMode()
        end)

    --ui_project:getChildByName("Panel_bj"):setVisible(false)
    --self:handCardAnimate()
    self:ui_close()
    
    self.ui_project:registerScriptHandler(handler(self,self.onNodeEvent))

    local listener = cc.EventListenerTouchOneByOne:create()
    listener:setSwallowTouches(false)
    listener:registerScriptHandler(handler(self, self.onTouchBegan), cc.Handler.EVENT_TOUCH_BEGAN)
    local eventDispatcher = cc.Director:getInstance():getEventDispatcher()
    eventDispatcher:addEventListenerWithSceneGraphPriority(listener, ui_project:getChildByName("Panel_bj"))
    self:onCardColorChange()
end

function BaiPaiUIManager:onCardColorChange()
    print("--- BaiPaiUIManager:onCardColorChange")
    local CardNameTab = {"card_bj_R.png","card_bj_G.png","card_bj_B.png"}
    local path = "res/game_res/"..GAME_ID.."/ui_res/baipai/paixing/"..CardNameTab[UserData:getCardColor()]
    for i=1,3 do
        self.image_card_tou_bj[i]:loadTexture(path)
    end
    for i=1,5 do
        self.image_card_zhong_bj[i]:loadTexture(path)
    end
    for i=1,5 do
        self.image_card_wei_bj[i]:loadTexture(path)
    end
end

-- 开始触摸
function BaiPaiUIManager:onTouchBegan(touch,event)
    print("--- BaiPaiUIManager:onTouchBegan")
    if self.isOver == false  then
        return false
    end
    local location = touch:getLocation()
    if location.y < 250 or self.baipaiMode == 1 then
        return false
    end
    self:changeHandCards()
end

function BaiPaiUIManager:onNodeEvent(event)
    if event == "enter" then
        print("--- BaiPaiUIManager:onNodeEvent:enter")
    elseif event == "exit" then
        print("--- BaiPaiUIManager:onNodeEvent:exit")
        self.curCountDountTime = -1
        self:stopCount()
    end

end

-- 游戏开始，初始化改类数据
function BaiPaiUIManager:onGameStart(resp_json)
    print("--- BaiPaiUIManager:onGameStart")

    -- 玩家手牌值
    self.handCardValues = {}

    -- 牌型数据
    self.cardTypeData = {}

    -- 头 中 尾道摆牌数据
    self.touData = {}
    self.zhongData = {}
    self.weiData = {}

    -- 头中尾道牌型
    self.daoCardType = {0,0,0,0}
    self.bAddPoint = false

    -- 已显示的手牌数
    self.showHandCardsNums = 0
    
    -- 发牌是否完成
    self.isOver = false

    -- 头中尾道是否点亮
    self.daoIsBright = {false,false,false}

    -- 头中尾道是否已经完成摆牌
    self.daoIsComplete = {false,false,false}

    -- 手牌排序默认大小排序
    self.isSize = true
    if self.isSize == true then
        self.Button_dx:loadTextures("res/game_res/"..GAME_ID.."/ui_res/baipai/paixing/daxiao.png", "", "")
    else
        self.Button_dx:loadTextures("res/game_res/"..GAME_ID.."/ui_res/baipai/paixing/huase.png", "", "")
    end

    -- 倒计时
    self.curCountDountTime = self.BaiPaiTime*10
--    self.LoadingBar_Time:setPercent(100)
    self.Text_timer:setText(tostring(self.BaiPaiTime))


    self:cancelCallBack(4)
    self.showHandCardsNums = 0
    self:setCardTypeButton(false)
    self:isShowHandCards(false)
    self.Button_baodao:setEnabled(false)
    self.Button_spcial_card_type:setVisible(false)
end

-- 抓牌
function BaiPaiUIManager:onCatchCard(resp_json)
    print("--- BaiPaiUIManager:onCatchCard start")
    if resp_json.HandCards == nil or #resp_json.HandCards ~= 13 then
        return api_show_Msg_Box(" 你已经掉线 ！",function()
                    GameSceneModule:getInstance():getGameScene():Exit_game(5) end)
    end

    if resp_json.allSecialCardTypeVec ~= nil  then
        GameLogic:setSpecialCardType(resp_json.allSecialCardTypeVec)
    end

    self.Panel_AutoBaipai:setVisible(false)
    self:isShowCardTypeButton(false)
    self.Button_BaipaiMode:setVisible(false)
    self:ui_open()
    self.handCardValues = resp_json.HandCards
    GameLogic:sort(self.handCardValues,0)
    self:setHandCardsValue()
    self:handCardAnimate()
    self:DetectionPxAuto()
    --self:DetectionPxAuto()

    print("--- BaiPaiUIManager:onCatchCard end")
end

--配牌
function BaiPaiUIManager:onCheatInfo(resp_json)
--    self.handCardValues = resp_json.HandCards
--    GameLogic:sort(self.handCardValues,0)
--    self:setHandCardsValue()
--    self:DetectionPxAuto()
     api_show_tips("设置牌成功，下次发牌生效")
end

function BaiPaiUIManager:onBaipaiResult(resp_json)
    print("--- BaiPaiUIManager:onBaipaiResult start")
    if resp_json.chair == nil then
        return
    end

    local ui_chair = ViewHelp.getUIChairIndexByServerChair(resp_json.chair)
    if ui_chair == 1 then
        self:stopCount()
        self:ui_close()
    end
    print("--- BaiPaiUIManager:onBaipaiResult end")
end

-- 设置牌值
function BaiPaiUIManager:setHandCardsValue()
    dump(self.handCardValues)

    for i=1,#self.handCardValues do
        self.handCards[i]:setCardImage(self.handCardValues[i])
    end
    self:setAllCardDown()
end

-- 设置头中尾道中的一个牌值
function BaiPaiUIManager:setOneCardValue(value, card)
    local Image_MaTip = card:getChildByName("Image_MaTip")
    local Image_Kuang = card:getChildByName("Image_Kuang")
    if ViewHelp.IsMapai(value) then
        Image_MaTip:setVisible(true)
        Image_Kuang:setVisible(true)
    else
        Image_MaTip:setVisible(false)
        Image_Kuang:setVisible(false)
    end

    --鬼牌癞子标识
   -- local Image_Kuang = card:getChildByName("Image_Kuang")
   -- if ViewHelp.IsGuiCards(value) then
    --    Image_Kuang:setVisible(true)
   -- else
    --    Image_Kuang:setVisible(false)
  --  end

    --同花順+癩子標識
    local Image_THSPTip = card:getChildByName("Image_THSPTip")
    if ViewHelp.IsTHSLaizi(value) then
        Image_THSPTip:setVisible(true)
    else
        Image_THSPTip:setVisible(false)
    end

    local num = value % 13
    if num == 0 then
        num = 13
    end
    local path = "res/game_res/"..GAME_ID.."/ui_res/baipai/card/card_" ..(math.ceil(value / 13) -1) .. "_" .. num .. ".png"
    card:loadTexture(path)
end

-- 所有牌恢复原位
function BaiPaiUIManager:setAllCardDown()
    for i=1,13 do
        self.handCards[i]:setPositionY(CARD_POSITION_Y_DOWN)
    end
end

-- 将牌落下
function BaiPaiUIManager:setCardDown(card)
    card:setPositionY(CARD_POSITION_Y_DOWN)
    self:CheckBrightDao()
end

function BaiPaiUIManager:SetCardValueUp(cardVal)
    for i=1,#self.handCardValues do
        if self.handCardValues[i] == cardVal and self.handCards[i]:getPositionY() ~= CARD_POSITION_Y_UP then
            self:setCardUp(self.handCards[i])
            break
        end
    end
end
-- 将牌弹起
function BaiPaiUIManager:setCardUp(card)
    card:setPositionY(CARD_POSITION_Y_UP)
    card.isUp = true
    self:CheckBrightDao()
    if self:getCardUpNums() == 3 then
        self:brightDao(1, true)
    elseif self:getCardUpNums() == 5 then
        self:brightDao(2, true)
        self:brightDao(3, true)
    else 
        self:brightDao(1, false)
        self:brightDao(2, false)
        self:brightDao(3, false)
    end
end

function BaiPaiUIManager:CheckBrightDao()
    self:brightDao(1, false)
    self:brightDao(2, false)
    self:brightDao(3, false)
    local upCardNums = self:getCardUpNums()
    if upCardNums > 0 then
        if upCardNums <= (3 - #self.touData) then
            self:brightDao(1, true)
        end
         if upCardNums <= (5 - #self.zhongData) then
            self:brightDao(2, true)
        end
         if upCardNums <= (5 - #self.weiData) then
            self:brightDao(3, true)
        end
        if upCardNums == 3 and #self.touData == 0 then
            self:brightDao(1, true)
        end
         if upCardNums == 5  then
            if #self.zhongData == 0 then
                self:brightDao(2, true)
            end
            if #self.weiData == 0 then
                self:brightDao(3, true)
            end
        end
    end
    
end

-- 得到弹起的牌的个数
function BaiPaiUIManager:getCardUpNums()
    local nums = 0
    for i=1,#self.handCardValues do
        if self.handCards[i]:getPositionY() == CARD_POSITION_Y_UP then
            nums = nums + 1
        end
    end
    return nums
end

-- 是否点亮 1.头 2.中 3.尾
function BaiPaiUIManager:brightDao(daoType, isBright)
    local tab = { }
    if daoType == 1 then
        tab = self.image_card_tou_bj
        self.daoIsBright[1] = isBright
        --self.Button_TouD:setVisible(isBright)
    elseif daoType == 2 then
        tab = self.image_card_zhong_bj
        self.daoIsBright[2] = isBright
        --self.Button_ZhongD:setVisible(isBright)
    elseif daoType == 3 then
        tab = self.image_card_wei_bj
        self.daoIsBright[3] = isBright
        --self.Button_WeiD:setVisible(isBright)
    end

    for k, v in pairs(tab) do
        if isBright then
            v:setColor(SELECT_COLOR)
        else
            v:setColor(NORMAL_COLOR)
        end
    end

end

-- 添加滑动选牌
function BaiPaiUIManager:addTouch()
    print("--- BaiPaiUIManager:addTouch")

    local function onTouchBegan(touch, event)
        if self.isOver == false  then
            return  false
        end
        for k, v in pairs(self.handCards) do
            local flag = v:isVisible()
            local p = v:convertToNodeSpace(touch:getLocation())
            local len = 37
            if k == #self.handCardValues then
                len = 0
            end
            local rect = cc.rect(2, 0, v:getContentSize().width - len, v:getContentSize().height)
            if self.isOver and flag and cc.rectContainsPoint(rect, p) then
                print("-----------------> x1 = " .. k)
                self.x1 = k
                return true
            end
        end

        return  false
    end

    local function onTouchMoved(touch, event)
        for k, v in pairs(self.handCards) do
            local flag = v:isVisible()
            local p = v:convertToNodeSpace(touch:getLocation())
            local len = 37
            if k == #self.handCardValues then
                len = 0
            end
            local rect = cc.rect(2, 0, v:getContentSize().width - len, v:getContentSize().height)
            if self.isOver and flag and cc.rectContainsPoint(rect, p) then
                print("-----------------> x2 = " .. k)
                self.x2 = k

                if self.x1 > 0 and self.x2 > 0 then
                    for m, n in pairs(self.cardShade) do
                        n:setVisible(false)
                    end

                    if self.x1 >= self.x2 then
                        for i = self.x2, self.x1 do
                            self.cardShade[i]:setVisible(true)
                        end
                    else
                        for i = self.x1, self.x2 do
                            self.cardShade[i]:setVisible(true)
                        end
                    end

                end
                break
            end
        end


    end

    local function onTouchEnded(touch, event)
        -- GameMusic:playClickEffect()
        for k, v in pairs(self.cardShade) do
            v:setVisible(false)
        end

        print("-------------> x1 = " .. self.x1 .. " x2 = " .. self.x2)
        if self.x1 > 0 and (self.x2 <= 0 or self.x2 == self.x1) then
            if Card_position_y[self.x1] == CARD_POSITION_Y_DOWN then
                Card_position_y[self.x1] = CARD_POSITION_Y_UP
                self:setCardUp(self.handCards[self.x1])
            else
                Card_position_y[self.x1] = CARD_POSITION_Y_DOWN
                self:setCardDown(self.handCards[self.x1])
            end
        end

        if self.x1 > 0 and self.x2 > 0 then
            if self.x1 > self.x2 then
                for i = self.x2, self.x1 do
                    if Card_position_y[i] == CARD_POSITION_Y_DOWN then
                        Card_position_y[i] = CARD_POSITION_Y_UP
                        self:setCardUp(self.handCards[i])
                    else
                        Card_position_y[i] = CARD_POSITION_Y_DOWN
                        self:setCardDown(self.handCards[i])
                    end
                end
            elseif self.x1 < self.x2 then
                for i = self.x1, self.x2 do
                    if Card_position_y[i] == CARD_POSITION_Y_DOWN then
                        Card_position_y[i] = CARD_POSITION_Y_UP
                        self:setCardUp(self.handCards[i])
                    else
                        Card_position_y[i] = CARD_POSITION_Y_DOWN
                        self:setCardDown(self.handCards[i])
                    end
                end
            end

        end

        self.x1 = 0
        self.x2 = 0
    end

    local listener = cc.EventListenerTouchOneByOne:create()
    listener:setSwallowTouches(false)
    listener:registerScriptHandler(onTouchBegan, cc.Handler.EVENT_TOUCH_BEGAN)
    listener:registerScriptHandler(onTouchMoved, cc.Handler.EVENT_TOUCH_MOVED)
    listener:registerScriptHandler(onTouchEnded, cc.Handler.EVENT_TOUCH_ENDED)
    local eventDispatcher = cc.Director:getInstance():getEventDispatcher()
    eventDispatcher:addEventListenerWithSceneGraphPriority(listener, self.handCards[1])

end

-- 给头中尾道牌背景添加触摸事件
function BaiPaiUIManager:addTouchToDaoCard_bj(image)
    local function onTouchBegan(touch, event)
        if self.isOver == false  then
            return false
        end
        local p = image:convertToNodeSpace(touch:getLocation())
        local rect = cc.rect(0, 0, image:getContentSize().width, image:getContentSize().height)     
        if cc.rectContainsPoint(rect, p) then
            --点击牌面取消
            if self.daoCardType[4] ~= 0 then
                return true
            end

            --手牌为零的时候开启换牌模式
            if #self.handCardValues == 0 then
                cclog("--手牌为零开启换牌模式-------")
                self:SwapDaoCard(image)
                return true
            end

            local bInTou, indexTou = GameLogic:isContains(image,self.image_card_tou_bj)
            local bInZhong, indexZhong = GameLogic:isContains(image,self.image_card_zhong_bj)
            local bInWei, indexWei = GameLogic:isContains(image,self.image_card_wei_bj)
            local upCards = GameLogic:getUpCardData(self.handCardValues,self.handCards)
            if bInTou  then
                if indexTou <= #self.touData then
                    self:RemoveOneCardFromDao(1,indexTou)
                else
                    self:AddDaoCardFromUpList(1,upCards)
                end 
                if #self.touData > 0 and not self:IsAutoBaipaiMode() then
                    self.button_close[1]:setVisible(true)
                    self.Button_spcial_card_type:setVisible(false)
                else
                    self.button_close[1]:setVisible(false)
                end

            elseif bInZhong  then
                if indexZhong <= #self.zhongData then
                    self:RemoveOneCardFromDao(2,indexZhong)
                else
                    self:AddDaoCardFromUpList(2,upCards)
                end 
                if #self.zhongData > 0 and not self:IsAutoBaipaiMode() then
                    self.button_close[2]:setVisible(true)
                    self.Button_spcial_card_type:setVisible(false)
                else
                    self.button_close[2]:setVisible(false)
                end
            elseif bInWei  then
                if indexWei <= #self.weiData then
                    self:RemoveOneCardFromDao(3,indexWei)
                else
                    self:AddDaoCardFromUpList(3,upCards)
                end 
                if #self.weiData > 0 and not self:IsAutoBaipaiMode() then
                    self.button_close[3]:setVisible(true)
                    self.Button_spcial_card_type:setVisible(false)
                else
                    self.button_close[3]:setVisible(false)
                end
            end
            return true
        end
    end
    local listener = cc.EventListenerTouchOneByOne:create()
    listener:setSwallowTouches(true)
    listener:registerScriptHandler(onTouchBegan, cc.Handler.EVENT_TOUCH_BEGAN)
    local eventDispatcher = cc.Director:getInstance():getEventDispatcher()
    eventDispatcher:addEventListenerWithSceneGraphPriority(listener, image)
end

function BaiPaiUIManager:SwapDaoCard(image)
    local bInTou, indexTou = GameLogic:isContains(image,self.image_card_tou_bj)
    local bInZhong, indexZhong = GameLogic:isContains(image,self.image_card_zhong_bj)
    local bInWei, indexWei = GameLogic:isContains(image,self.image_card_wei_bj)
    local daoType = 0
    local cardIndex = 0
    if bInTou then
        daoType = 1
        cardIndex = indexTou
    elseif bInZhong then
        daoType = 2
        cardIndex = indexZhong
    elseif bInWei then
        daoType = 3
        cardIndex = indexWei
    end

    local dataTab = {self.touData, self.zhongData, self.weiData}
    local image_card_tab = {self.image_card_tou, self.image_card_zhong, self.image_card_wei}
    if self.selectedCardInfo.daoType == 0 or self.selectedCardInfo.cardIndex == 0 then
        --未选中一张，设置选中信息
        self.selectedCardInfo.daoType = daoType
        self.selectedCardInfo.cardIndex = cardIndex
        image_card_tab[self.selectedCardInfo.daoType][self.selectedCardInfo.cardIndex]:setColor(SELECTED_COLOR)
    else
        --选中了一张，将之前选中的与之交换
        
        local tempData = dataTab[daoType][cardIndex]
        dataTab[daoType][cardIndex] = dataTab[self.selectedCardInfo.daoType][self.selectedCardInfo.cardIndex]
        dataTab[self.selectedCardInfo.daoType][self.selectedCardInfo.cardIndex] = tempData
        self:TidyDaoCards(3)
        self:TidyDaoCards(2)
        self:TidyDaoCards(1)

--        self:TidyDaoCards(daoType)
--        self:TidyDaoCards(self.selectedCardInfo.daoType)
        image_card_tab[self.selectedCardInfo.daoType][self.selectedCardInfo.cardIndex]:setColor(NORMAL_COLOR)
        GameLogic:sortBySize(dataTab[daoType])
        self.daoCardType[daoType] = GameLogic:judgeCardType_L(dataTab[daoType])
        self.daoCardType[self.selectedCardInfo.daoType] = GameLogic:judgeCardType_L(dataTab[self.selectedCardInfo.daoType])
        self:daoImageType(daoType, self.daoCardType[daoType])
        self:daoImageType(self.selectedCardInfo.daoType, self.daoCardType[self.selectedCardInfo.daoType])

        self.selectedCardInfo.daoType = 0
        self.selectedCardInfo.cardIndex = 0
    end

end

function BaiPaiUIManager:TidyDaoCards(daoType)
    local dataTab = {self.touData, self.zhongData, self.weiData}
    local image_card_tab = {self.image_card_tou, self.image_card_zhong, self.image_card_wei}
    --GameLogic:sortBySize(dataTab[daoType])
    if daoType == 3 then
        ViewHelp.SetTHSPLaizis({})
    end
--    self.daoCardType[daoType] = GameLogic:judgeCardType_L(dataTab[daoType])
    
    --self:daoImageType(daoType, self.daoCardType[daoType])
    for i = 1, #dataTab[daoType] do
        self:setOneCardValue(dataTab[daoType][i], image_card_tab[daoType][i])
    end
end

function BaiPaiUIManager:DetectionTHSP(cards)
--    if cards==nil and self.daoCardType[3] ~= CardType.CT_TONGHUASZ then
--        ViewHelp.SetTHSPLaizis({})
--        return 
--    end
--    --同花顺加癞子检测
--    local startPoint = 0
--    local huakind = 0
--    local cardTab = self.weiData

--    if cards then
--        cardTab = cards
--    end

--    for k,v in pairs(cardTab) do 
--        if v == 53 or v==54  then
--            ViewHelp.SetTHSPLaizis({})
--            return 
--        end
--    end

--    for k,v in pairs(cardTab) do 
--        if v ~= 53 and v~=54 and not ViewHelp.IsGuiCards(v) then
--            huakind = GameLogic:GetCardHuaKind(v)
--            startPoint = GameLogic:getCardPoint(v) + k - 1
--            break
--        end
--    end
--    local thspCards = {}
--    if startPoint<14 then
--        for i = startPoint+1, 14 do
--            local cardval = huakind*13 + i
--            if i == 14 then
--                cardval = cardval - 13
--            end
--            if GameLogic:isContains(cardval,self.handCardValues) 
--            or GameLogic:isContains(cardval,self.touData)
--            or GameLogic:isContains(cardval,self.zhongData) then
--                table.insert(thspCards,cardval)
--            else
--                break
--            end
--        end
--    end

--    if startPoint>5 then
--        for i = startPoint-5, 1, -1 do
--            local cardval = huakind*13 + i
--            if GameLogic:isContains(cardval,self.handCardValues) 
--            or GameLogic:isContains(cardval,self.touData)
--            or GameLogic:isContains(cardval,self.zhongData) then
--                if not (i == 1 and startPoint==14) then
--                    table.insert(thspCards,cardval)
--                end
--            else
--                break
--            end
--        end
--    end
--    ViewHelp.SetTHSPLaizis(thspCards)
end

function BaiPaiUIManager:RemoveOneCardFromDao(daoType,Cardindex)
    local removeTab = {}
    local dataTab = {self.touData, self.zhongData, self.weiData}
    local DaoData = dataTab[daoType]
    local image_card_tab = {self.image_card_tou, self.image_card_zhong, self.image_card_wei}
    local image_card = image_card_tab[daoType]
    table.insert(removeTab,DaoData[Cardindex])
    table.remove(DaoData, Cardindex)
    GameLogic:sortBySize(DaoData)
    for i = 1, #DaoData do
       self:setOneCardValue(DaoData[i], image_card[i])
    end
    for i = (#DaoData+1),(#image_card) do
        image_card[i]:setVisible(false)
    end
    for i = 1, #DaoData do
        self:setOneCardValue(DaoData[i], image_card[i])
    end
    GameLogic:changeTab(self.handCardValues, removeTab, true)
    self.Button_allclose:setVisible(false)
    self.Button_refer:setVisible(false)
    self:isShowCardTypeButton(true)
    self.Text_s_cardtype:setVisible(false)
    self.Text_card_type:setVisible(false)
    if #self.handCardValues == 13 then
        self:judgeSpecialCardType()
    end
    local ImageTab = {self.Image_tou, self.Image_zhong, self.Image_wei}
    local pngNameTab = {"toudao.png","zhongdao.png","weidao.png"}
    ImageTab[daoType]:loadTexture("res/game_res/"..GAME_ID.."/ui_res/baipai/paixing/"..pngNameTab[daoType])
    ImageTab[daoType]:setVisible(false)
    self.daoCardType[daoType] = -1
    self:daoImageType(daoType,self.daoCardType[daoType])
    self:changeHandCards()
end

function BaiPaiUIManager:AddDaoCardFromUpList(daoType,cards)
    local imageCardTab = {self.image_card_tou, self.image_card_zhong, self.image_card_wei}    
    local maxDaoNumsTab = {3, 5, 5}
    local daoDataTab = {self.touData, self.zhongData, self.weiData}
    local imageCard = imageCardTab[daoType]
    local maxDaoNums = maxDaoNumsTab[daoType]
    local daoData = daoDataTab[daoType]
    if (#cards + #daoData) > maxDaoNums then
        return
    end
    for key,val in pairs(cards) do
        table.insert(daoData,val)
    end
    GameLogic:sortBySize(daoData)
    if #daoData == maxDaoNums then
        self.daoIsBright[daoType] = false
        self.daoIsComplete[daoType] = true
        self.daoCardType[daoType] = GameLogic:judgeCardType_L(daoData)
        
        self:daoImageType(daoType, self.daoCardType[daoType])
        if not self:IsAutoBaipaiMode() then
            self.button_close[daoType]:setVisible(true)
        end
    end
    for i = 1, #daoData do
       self:setOneCardValue(daoData[i], imageCard[i])
    end
    for k, v in pairs(daoData) do
        imageCard[k]:setVisible(true)
    end
    GameLogic:changeTab(self.handCardValues, cards, false)
    self:changeHandCards()
    if #self.handCardValues == 0 then
        if not self:IsAutoBaipaiMode() then
            self.Button_allclose:setVisible(true)
        end
        self.Button_refer:setVisible(true)
        self:isShowCardTypeButton(false)
--        self:judgeDaoShui()
    else
        if (3 - #self.touData) == #self.handCardValues then
            local tab = clone(self.handCardValues)
            self:AddDaoCardFromUpList(1,tab)
        elseif (5 - #self.zhongData) == #self.handCardValues then
            local tab = clone(self.handCardValues)
            self:AddDaoCardFromUpList(2,tab)
        elseif (5 - #self.weiData) == #self.handCardValues then
            local tab = clone(self.handCardValues)
            self:AddDaoCardFromUpList(3,tab)
        end
    end
    
end
---- 给头中尾道牌背景添加触摸事件
--function BaiPaiUIManager:addTouchToDaoCard_bj(image)
--    local function onTouchBegan(touch, event)
--        local p = image:convertToNodeSpace(touch:getLocation())
--        local rect = cc.rect(0, 0, image:getContentSize().width, image:getContentSize().height)     
--        if cc.rectContainsPoint(rect, p) then
--            --点击牌面取消
--            if self.daoIsComplete[1] and GameLogic:isContains(image,self.image_card_tou_bj) then
--                self:cancelCallBack(1)
--            elseif self.daoIsComplete[2] and GameLogic:isContains(image,self.image_card_zhong_bj) then
--                self:cancelCallBack(2)
--            elseif self.daoIsComplete[3] and GameLogic:isContains(image,self.image_card_wei_bj) then
--                self:cancelCallBack(3)
--            end
--            local ModeID =  ViewHelp.getDeskInfo().config.Mode.id
--            if not self.daoIsComplete[1] and self.daoIsBright[1] and self:getCardUpNums() == 3 and GameLogic:isContains(image,self.image_card_tou_bj) then
--                GameMusic:playClickEffect()
--                local daoData,cardType = GameLogic:getUpCardData(self.handCardValues,self.handCards)
--                self:showDaoCard(1,true,daoData,cardType)
--            elseif not self.daoIsComplete[2] and self.daoIsBright[2] and self:getCardUpNums() == 5 and GameLogic:isContains(image,self.image_card_zhong_bj) then
--                GameMusic:playClickEffect()
--                local daoData,cardType = GameLogic:getUpCardData(self.handCardValues,self.handCards)
--                self:showDaoCard(2,true,daoData,cardType)
--            elseif not self.daoIsComplete[3] and self.daoIsBright[3] and self:getCardUpNums() == 5 and GameLogic:isContains(image,self.image_card_wei_bj) then
--                GameMusic:playClickEffect()
--                local daoData,cardType = GameLogic:getUpCardData(self.handCardValues,self.handCards)
--                self:showDaoCard(3,true,daoData,cardType)
--            end
--        end
--    end
--    local listener = cc.EventListenerTouchOneByOne:create()
--    listener:setSwallowTouches(true)
--    listener:registerScriptHandler(onTouchBegan, cc.Handler.EVENT_TOUCH_BEGAN)
--    local eventDispatcher = cc.Director:getInstance():getEventDispatcher()
--    eventDispatcher:addEventListenerWithSceneGraphPriority(listener, image)
--end
-- 是否显示 1.头 2.中 3.尾道牌
function BaiPaiUIManager:showDaoCard(daoType, isShow, daoData, cardType)
    self.Button_baodao:setEnabled(false)
    self.Button_spcial_card_type:setVisible(false)

    local tab = { }
    if daoType == 1 then
        tab = self.image_card_tou
        self.daoIsBright[1] = false
        self.daoIsComplete[1] = true
        if daoData ~= nil and #daoData == 3 then
            self.touData = clone(daoData)
            self.daoCardType[1] = cardType
            self.button_close[1]:setVisible(true)
            self.Button_spcial_card_type:setVisible(false)
        end
    elseif daoType == 2 then
        tab = self.image_card_zhong
        self.daoIsBright[2] = false
        self.daoIsComplete[2] = true
        if daoData ~= nil and #daoData == 5 then
            self.zhongData = clone(daoData)
            self.daoCardType[2] = cardType
            self.button_close[2]:setVisible(true)
            self.Button_spcial_card_type:setVisible(false)
        end
    elseif daoType == 3 then
        tab = self.image_card_wei
        self.daoIsBright[3] = false
        self.daoIsComplete[3] = true
        if daoData ~= nil and #daoData == 5 then
            self.weiData = clone(daoData)
            self.daoCardType[3] = cardType
            self.button_close[3]:setVisible(true)
            self.Button_spcial_card_type:setVisible(false)
        end
    end

    if isShow and daoData ~= nil then
        for i = 1, #daoData do
            self:setOneCardValue(daoData[i], tab[i])
        end

        -- 删除手牌中的一些牌
        GameLogic:changeTab(self.handCardValues, daoData, false)
        self:changeHandCards()

        if #self.handCardValues == 3 then

--            local tabTemp = clone(self.handCardValues)
--            local cardT = 0
--            GameLogic:sort(tabTemp, 0)
--            cardT = GameLogic:judgeCardType(tabTemp, cardT)
--            self:showDaoCard(1, true, tabTemp, cardT)

            performWithDelay(self.ui_project,
            function()
                local tabTemp = clone(self.handCardValues)
                local cardT = 0
                GameLogic:sort(tabTemp, 0)
                local desk_config =  ViewHelp.getDeskInfo()
--                if desk_config.config.Mode.id == 2 then
--                    cardT = GameLogic:judgeCardType_L(tabTemp, cardT)
--                else
--                    cardT = GameLogic:judgeCardType(tabTemp, cardT)
--                end
                cardT = GameLogic:judgeCardType_L(tabTemp, cardT)
                self:showDaoCard(1, true, tabTemp, cardT)
            end ,
            0.3
            )

        elseif #self.handCardValues == 5 then
            local flag = 0
            if self.daoIsComplete[2] then
                flag = 3
            else 
                flag = 2
            end

--            local tabTemp = clone(self.handCardValues)
--            local cardT = 0
--            GameLogic:sort(tabTemp, 0)
--            cardT = GameLogic:judgeCardType(tabTemp, cardT)
--            self:showDaoCard(flag, true, tabTemp, cardT)

            performWithDelay(self.ui_project,
            function()
                local tabTemp = clone(self.handCardValues)
                local cardT = 0
                GameLogic:sort(tabTemp, 0)
                local desk_config =  ViewHelp.getDeskInfo()
--                if desk_config.config.Mode.id == 2 then
--                    cardT = GameLogic:judgeCardType_L(tabTemp, cardT)
--                else
--                    cardT = GameLogic:judgeCardType(tabTemp, cardT)
--                end
                cardT = GameLogic:judgeCardType_L(tabTemp, cardT)
                self:showDaoCard(flag, true, tabTemp, cardT)
            end ,
            0.3
            )

        elseif #self.handCardValues == 0 then
            self.Button_allclose:setVisible(true)
            self.Button_refer:setVisible(true)
            self:isShowCardTypeButton(false)
        end

    end

    self:daoImageType(daoType, cardType)

    for k, v in pairs(tab) do
        v:setVisible(isShow)
    end

--    if #self.handCardValues == 0 then
--        self:judgeDaoShui()
--    end

end

-- 倒水判断
function BaiPaiUIManager:judgeDaoShui()
    print("--- BaiPaiUIManager:judgeDaoShui start")
    if GameLogic:compare_tou_zhong(self.touData,self.zhongData,self.daoCardType) then
        api_show_tips("摆牌失败，头道不能大于中道")
        return true
        --self:cancelCallBack(4)
    elseif GameLogic:compare_zhong_wei(self.zhongData,self.weiData,self.daoCardType) then
        api_show_tips("摆牌失败，中道不能大于尾道")
        return true
        --self:cancelCallBack(4)
    end
    print("--- BaiPaiUIManager:judgeDaoShui end")
    return false
end

-- 重新整理手牌
function BaiPaiUIManager:changeHandCards()
    print("--- BaiPaiUIManager:changeHandCard start")
    if self:IsAutoBaipaiMode() then
        return
    end
    if self.isSize then
        GameLogic:sort(self.handCardValues, 0)
    else 
        GameLogic:sort(self.handCardValues, 1)
    end

    self:setHandCardsValue()
    self:isShowHandCards(false)

    local nums = #self.handCardValues
    for i = 1, nums do
        self.handCards[i]:setVisible(true)
        self.handCards[i]:down()
    end

    if nums >= 1 then
        local start_position_x = 640
        if nums % 2 == 0 then
            start_position_x = start_position_x -(nums / 2 - 0.5) * CARDWIDE
        else
            start_position_x = start_position_x -(math.ceil(nums / 2) -1) * CARDWIDE
        end
        for i = 1, nums do
            self.handCards[i]:setPositionX(start_position_x)
            self.handCards[i]:setVisible(true)
            start_position_x = start_position_x + CARDWIDE
        end
    end

--    self.Button_dx:setVisible(true)
    self:setCardTypeButton(false)
    self:detectionCardType(self.handCardValues)

    self:brightDao(1, false)
    self:brightDao(2, false)
    self:brightDao(3, false)

    print("--- BaiPaiUIManager:changeHandCard end")
end

-- 头中尾道牌型图片
function BaiPaiUIManager:daoImageType(daoType, cardType)
    print("--- BaiPaiUIManager:daoImageType daoType cardType =" .. daoType .. " " .. cardType)
    local path = "res/game_res/"..GAME_ID.."/ui_res/baipai/paixing/"

    if cardType == 0 then
        path = path .. "wulong.png"
    elseif cardType == 1 then
        path = path .. "duizi.png"
    elseif cardType == 2 then
        path = path .. "liangdui.png"
    elseif cardType == 3 then
        path = path .. "santiao.png"
    elseif cardType == 4 then
        path = path .. "shunzi.png"
    elseif cardType == 5 then
        path = path .. "tonghua.png"
    elseif cardType == 6 then
        path = path .. "hulu.png"
    elseif cardType == 7 then
        path = path .. "tiezhi.png"
    elseif cardType == 8 then
        path = path .. "tonghuashun.png"
    elseif cardType == 9 then
        path = path .. "wutong.png"
    else
        path = "res/game_res/"..GAME_ID.."/ui_res/baipai/paixing/"
        if daoType == 1 then
            path = path .. "toudao.png"
        elseif daoType == 2 then
            path = path .. "zhong.png"
        else
            path = path .. "wei.png"
        end
    end
    local image = self.Image_tou
    if daoType == 1 then
        self.Image_tou:loadTexture(path)
    elseif daoType == 2 then
        image = self.Image_zhong
        self.Image_zhong:loadTexture(path)
    elseif daoType == 3 then
        image = self.Image_wei
        self.Image_wei:loadTexture(path)
        self:DetectionTHSP()
    end
    if cardType<0 or cardType>9 then
        image:setVisible(false)
    else
        image:setVisible(true)
    end
    print("--- BaiPaiUIManager:daoImageType end")
    
end

-- 手牌动画居中显示 
function BaiPaiUIManager:handCardAnimate()
    print("--- BaiPaiUIManager:handCardAnimate_" .. self.showHandCardsNums)
    self.showHandCardsNums = self.showHandCardsNums + 1

    if self.showHandCardsNums > 13 then
        self.curCountDountTime = self.BaiPaiTime*10
        self:animateComplete()
        return
    end

    GameMusic:playSendCardEffect()

    local start_position_x = 640
    if self.showHandCardsNums % 2 == 0 then
        start_position_x = start_position_x - (self.showHandCardsNums / 2 - 0.5) * CARDWIDE
    else
        start_position_x = start_position_x - (math.ceil(self.showHandCardsNums / 2) - 1) * CARDWIDE 
    end
    --if not self:IsAutoBaipaiMode() then
        for i = 1,self.showHandCardsNums do
            self.handCards[i]:setPositionX(start_position_x)
            self.handCards[i]:setVisible(true)
            start_position_x = start_position_x + CARDWIDE
        end
    --end
    print ("******************************self.sendCardSpeed = " .. self.sendCardSpeed)
    performWithDelay(self.ui_project,
        function ()
            self:handCardAnimate()
        end,
        INTERVAL_TIME * 100 / self.sendCardSpeed
    )

end

-- 发牌动画完成
function BaiPaiUIManager:animateComplete()
    print("--- BaiPaiUIManager:animateComplete")
    for i = 1, 13 do
        self.handCards[i]:setPositionX(Card_position_x[i])
    end
    self:startCountdown()
    self.Button_BaipaiMode:setVisible(true)
    self.isOver = true
    self.showHandCardsNums = 13
    --self:DetectionPxAuto()
    self:detectionCardType(self.handCardValues)
    self:judgeSpecialCardType(true, true)
    self:UpdateBaiPaiMode()
end

    

 -- 检测有哪些牌型
 function BaiPaiUIManager:detectionCardType(handCardValues)
    print("--- BaiPaiUIManager:detectionCardType")
    local desk_config =  ViewHelp.getDeskInfo()
    local cardType,cardTypeData
--    if desk_config.config.Mode.id == 2 then
--        cardType,cardTypeData= GameLogic:detectionCardType_L(handCardValues)
--    else
--        cardType,cardTypeData= GameLogic:detectionCardType(handCardValues)
--    end
    cardType,cardTypeData= GameLogic:detectionCardType_L(handCardValues)
    self.cardTypeData = cardTypeData

    if cardType[CardType.CT_PAIRS] == 1 then
        self.Button_px[CardType.CT_PAIRS]:setEnabled(true)
    else
        self.Button_px[CardType.CT_PAIRS]:setEnabled(false)
    end

    if cardType[CardType.CT_PAIRS] == 1 and #self.cardTypeData.Pairs.PairsTab >= 2 then
        self.Button_px[CardType.CT_TWO_PAIRS]:setEnabled(true)
    else
        self.Button_px[CardType.CT_TWO_PAIRS]:setEnabled(false)
    end

    if cardType[CardType.CT_THREE] == 1 then
        self.Button_px[CardType.CT_THREE]:setEnabled(true)
    else
        self.Button_px[CardType.CT_THREE]:setEnabled(false)
    end

    if cardType[CardType.CT_SHUNZA] == 1 then
        self.Button_px[CardType.CT_SHUNZA]:setEnabled(true)
    else
        self.Button_px[CardType.CT_SHUNZA]:setEnabled(false)
    end

    if cardType[CardType.CT_TONGHUA] == 1 then
        self.Button_px[CardType.CT_TONGHUA]:setEnabled(true)
    else
        self.Button_px[CardType.CT_TONGHUA]:setEnabled(false)
    end

    if cardType[CardType.CT_GOURD] == 1 then
        self.Button_px[CardType.CT_GOURD]:setEnabled(true)
    else
        self.Button_px[CardType.CT_GOURD]:setEnabled(false)
    end

    if cardType[CardType.CT_TIEZHI] == 1 then
        self.Button_px[CardType.CT_TIEZHI]:setEnabled(true)
    else
        self.Button_px[CardType.CT_TIEZHI]:setEnabled(false)
    end

    if cardType[CardType.CT_TONGHUASZ] == 1 then
        self.Button_px[CardType.CT_TONGHUASZ]:setEnabled(true)
    else
        self.Button_px[CardType.CT_TONGHUASZ]:setEnabled(false)
    end

    if cardType[CardType.CT_WUTONG] == 1 then
        self.Button_px[CardType.CT_WUTONG]:setEnabled(true)
    else
        self.Button_px[CardType.CT_WUTONG]:setEnabled(false)
    end
    
 end

-- 给按钮添加点击事件
function BaiPaiUIManager:addButtonListener()
    self.Button_px[CardType.CT_PAIRS]:addClickEventListener( function()
        GameMusic:playClickEffect()
        self:pairsCallBack_L()
    end )

    self.Button_px[CardType.CT_TWO_PAIRS]:addClickEventListener( function()
        GameMusic:playClickEffect()
        self:twoPairsCallBack_L()
    end )

    self.Button_px[CardType.CT_THREE]:addClickEventListener( function()
        GameMusic:playClickEffect()
        self:threeCallBack_L()
    end )

    self.Button_px[CardType.CT_SHUNZA]:addClickEventListener( function()
        GameMusic:playClickEffect()
        self:shunzaCallBack_L()
    end )

    self.Button_px[CardType.CT_TONGHUA]:addClickEventListener( function()
        GameMusic:playClickEffect()
        self:tonghuaCallBack_L()
    end )

    self.Button_px[CardType.CT_GOURD]:addClickEventListener( function()
        GameMusic:playClickEffect()
        self:gourdCallBack_L()
    end )

    self.Button_px[CardType.CT_TIEZHI]:addClickEventListener( function()
        GameMusic:playClickEffect()
        self:tiezhiCallBack_L()
    end )

    self.Button_px[CardType.CT_TONGHUASZ]:addClickEventListener( function()
        GameMusic:playClickEffect()
        self:tonghuaszCallBack_L()
    end )

    self.Button_px[CardType.CT_WUTONG]:addClickEventListener( function()
        GameMusic:playClickEffect()
        self:wutongCallBack_L()
    end )

end

-- 对子
function BaiPaiUIManager:pairsCallBack()
    local desk_config =  ViewHelp.getDeskInfo()
    print("--- BaiPaiUIManager:pairsCallBack start")

    if self.cardTypeData.Pairs == nil then
        return
    end

    local Pairs = self.cardTypeData.Pairs
    print("--- BaiPaiUIManager:pairsCallBack pairs = ".. #Pairs.PairsValue)
    if #Pairs.PairsValue < 1 then
        return 
    end

    self:setAllCardDown()

    for i=1,#self.handCardValues do
        if GameLogic:getCardPoint(self.handCardValues[i]) == GameLogic:getCardPoint(Pairs.PairsValue[Pairs.index]) then
            self:setCardUp(self.handCards[i])
        end
    end

    if #self.cardTypeData.SingleCard >= 3 then
        for i=1,3 do
            local singlePoint = GameLogic:getCardPoint(self.cardTypeData.SingleCard[i])
            for j=1,#self.handCardValues do
                local cardPoint = GameLogic:getCardPoint(self.handCardValues[j])
                if singlePoint == cardPoint then
                    self:setCardUp(self.handCards[j])
                    break
                end
            end
        end
    end

    Pairs.index = Pairs.index + 1
    if Pairs.index > #Pairs.PairsValue then
        Pairs.index = 1
    end

    print("--- BaiPaiUIManager:pairsCallBack end")
end

-- 两对
function BaiPaiUIManager:twoPairsCallBack()
    local desk_config =  ViewHelp.getDeskInfo()
    print("--- BaiPaiUIManager:twoPairsCallBack start")

    if self.cardTypeData.Pairs == nil then
        return
    end

    local Pairs = self.cardTypeData.Pairs
    print("--- BaiPaiUIManager:twoPairsCallBack pairs = " .. #Pairs.PairsValue)
    if #Pairs.PairsValue < 2 then
        return
    end

    self:setAllCardDown()

    for i = 1, #self.handCardValues do
        if GameLogic:getCardPoint(self.handCardValues[i]) == GameLogic:getCardPoint(Pairs.PairsValue[Pairs.twoIndex]) or
            GameLogic:getCardPoint(self.handCardValues[i]) == GameLogic:getCardPoint(Pairs.PairsValue[Pairs.twoIndex + 1]) then
            self:setCardUp(self.handCards[i])
        end
    end

    if #self.cardTypeData.SingleCard >= 1 then
        local singlePoint = GameLogic:getCardPoint(self.cardTypeData.SingleCard[1])
        for j = 1, #self.handCardValues do
            local cardPoint = GameLogic:getCardPoint(self.handCardValues[j])
            if singlePoint == cardPoint then
                self:setCardUp(self.handCards[j])
                break
            end
        end
    end

    Pairs.twoIndex = Pairs.twoIndex + 1
    if Pairs.twoIndex >= #Pairs.PairsValue then
        Pairs.twoIndex = 1
    end

    print("--- BaiPaiUIManager:twoPairsCallBack end")
end

-- 三条
function BaiPaiUIManager:threeCallBack()
    local desk_config =  ViewHelp.getDeskInfo()
    print("--- BaiPaiUIManager:threeCallBack start")

    if self.cardTypeData.Three == nil then
        return
    end

    local Three = self.cardTypeData.Three
    print("--- BaiPaiUIManager:threeCallBack Three = " .. #Three.ThreeValue)
    if #Three.ThreeValue < 1 then
        return
    end

    self:setAllCardDown()

    for i = 1, #self.handCardValues do
        if GameLogic:getCardPoint(self.handCardValues[i]) == GameLogic:getCardPoint(Three.ThreeValue[Three.index]) then
            self:setCardUp(self.handCards[i])
        end
    end

    if #self.cardTypeData.SingleCard >= 2 then
        for i=1,2 do
            local singlePoint = GameLogic:getCardPoint(self.cardTypeData.SingleCard[i])
            for j=1,#self.handCardValues do
                local cardPoint = GameLogic:getCardPoint(self.handCardValues[j])
                if singlePoint == cardPoint then
                    self:setCardUp(self.handCards[j])
                    break
                end
            end
        end
    end

    Three.index = Three.index + 1
    if Three.index > #Three.ThreeValue then
        Three.index = 1
    end

    print("--- BaiPaiUIManager:threeCallBack end")
end

-- 顺子
function BaiPaiUIManager:shunzaCallBack()
    local desk_config =  ViewHelp.getDeskInfo()
    print("--- BaiPaiUIManager:shunzaCallBack start")

    if self.cardTypeData.Shunza == nil then
        return
    end

    local Shunza = self.cardTypeData.Shunza
    print("--- BaiPaiUIManager:shunzaCallBack Shunza = " .. #Shunza.ShunzaValue)
    if #Shunza.ShunzaValue < 1 then
        return
    end

    self:setAllCardDown()

    local cardPoint = GameLogic:getCardPoint(Shunza.ShunzaValue[Shunza.index])
    if cardPoint == 14 then
        if GameLogic:isInTable(cardPoint - 1, self.handCardValues) and GameLogic:isInTable(cardPoint - 2, self.handCardValues) and
            GameLogic:isInTable(cardPoint - 3, self.handCardValues) and GameLogic:isInTable(cardPoint - 4, self.handCardValues) and Shunza.index == 1 then
            for i = 0, 4 do
                for j = 1, #self.handCardValues do
                    if GameLogic:getCardPoint(self.handCardValues[j]) ==(cardPoint - i) then
                        self:setCardUp(self.handCards[j])
                        break
                    end
                end
            end
        else
            local tab = { 14, 2, 3, 4, 5 }
            for i = 1, 5 do
                for j = 1, #self.handCardValues do
                    if GameLogic:getCardPoint(self.handCardValues[j]) ==(tab[i]) then
                        self:setCardUp(self.handCards[j])
                        break
                    end
                end
            end


        end
    else
        for i = 0, 4 do
            for j = 1, #self.handCardValues do
                if GameLogic:getCardPoint(self.handCardValues[j]) ==(cardPoint - i) then
                    self:setCardUp(self.handCards[j])
                    break
                end
            end
        end
    end

    Shunza.index = Shunza.index + 1
    if Shunza.index > #Shunza.ShunzaValue then
        Shunza.index = 1
    end

    print("--- BaiPaiUIManager:shunzaCallBack end")
end

-- 同花
function BaiPaiUIManager:tonghuaCallBack()
    local desk_config =  ViewHelp.getDeskInfo()
    print("--- BaiPaiUIManager:tonghuaCallBack start")

    if self.cardTypeData.Tonghua == nil then
        return
    end

    local Tonghua = self.cardTypeData.Tonghua
    print("--- BaiPaiUIManager:tonghuaCallBack Tonghua = " .. #Tonghua.TonghuaValue)
    if #Tonghua.TonghuaValue < 1 then
        return
    end

    self:setAllCardDown()

--    local cardHuaKind = GameLogic:GetCardHuaKind(Tonghua.TonghuaValue[Tonghua.index])
--    local cardPoint = GameLogic:getCardPoint(Tonghua.TonghuaValue[Tonghua.index])
--    for i = 0, 4 do
--        for j = 1, #self.handCardValues do
--            if GameLogic:GetCardHuaKind(self.handCardValues[j]) == cardHuaKind  and 
--            (GameLogic:getCardPoint(self.handCardValues[j]) < cardPoint or (i == 0 and GameLogic:getCardPoint(self.handCardValues[j]) == cardPoint)) then
--                cardPoint = GameLogic:getCardPoint(self.handCardValues[j])
--                self:setCardUp(self.handCards[j])
--                break
--            end
--        end
--    end

    for i=1,#self.handCardValues do
        if GameLogic:GetCardHuaKind(self.handCardValues[i]) == Tonghua.TonghuaValue[Tonghua.index] then
            self:setCardUp(self.handCards[i])
        end
    end


    Tonghua.index = Tonghua.index + 1
    if Tonghua.index > #Tonghua.TonghuaValue then
        Tonghua.index = 1
    end

    print("--- BaiPaiUIManager:tonghuaCallBack end")
end

-- 葫芦
function BaiPaiUIManager:gourdCallBack()
    local desk_config =  ViewHelp.getDeskInfo()
    print("--- BaiPaiUIManager:gourdCallBack start")

    if self.cardTypeData.Gourd == nil then
        return
    end

    local Gourd = self.cardTypeData.Gourd
    print("--- BaiPaiUIManager:gourdCallBack Gourd = " .. #Gourd.GourdValue)
    if #Gourd.GourdValue < 1 or(#Gourd.GourdValue < 2 and #Gourd.Pairs < 1) then
        return
    end

    self:setAllCardDown()

    for i = 1, #self.handCardValues do
        local cardNums = GameLogic:getCardNum(self.handCardValues[i],self.handCardValues)
        if cardNums == 2 or cardNums == 3 then
            self:setCardUp(self.handCards[i])
        end
    end

--    for i = 1, #self.handCardValues do
--        if GameLogic:getCardPoint(self.handCardValues[i]) == GameLogic:getCardPoint(Gourd.GourdValue[Gourd.index]) then
--            self:setCardUp(self.handCards[i])
--        end
--    end

--    if #Gourd.Pairs >= 1 then
--        for i = 1, #self.handCardValues do
--            if GameLogic:getCardPoint(self.handCardValues[i]) == GameLogic:getCardPoint(Gourd.Pairs[#Gourd.Pairs]) then
--                self:setCardUp(self.handCards[i])
--            end
--        end
--    elseif #Gourd.GourdValue >= 2 then
--        local flag = 0
--        for i = 1, #self.handCardValues do
--            if flag >= 2 then
--                break
--            end
--            if GameLogic:getCardPoint(self.handCardValues[i]) == GameLogic:getCardPoint(Gourd.GourdValue[#Gourd.GourdValue]) then
--                self:setCardUp(self.handCards[i])
--                flag = flag + 1
--            end
--        end
--    end

--    Gourd.index = Gourd.index + 1
--    if Gourd.index > #Gourd.GourdValue then
--        Gourd.index = 1
--    end

--    if #Gourd.Pairs < 1 and Gourd.index == #Gourd.GourdValue then
--        Gourd.index = 1
--    end

    print("--- BaiPaiUIManager:gourdCallBack end")
end

-- 铁支
function BaiPaiUIManager:tiezhiCallBack()
    local desk_config =  ViewHelp.getDeskInfo()
    print("--- BaiPaiUIManager:tiezhiCallBack start")

    if self.cardTypeData.Tiezhi == nil then
        return
    end

    local Tiezhi = self.cardTypeData.Tiezhi
    print("--- BaiPaiUIManager:tiezhiCallBack Tiezhi = " .. #Tiezhi.TiezhiValue)
    if #Tiezhi.TiezhiValue < 1 then
        return
    end

    self:setAllCardDown()

    for i = 1, #self.handCardValues do
        if GameLogic:getCardPoint(self.handCardValues[i]) == GameLogic:getCardPoint(Tiezhi.TiezhiValue[Tiezhi.index]) then
            self:setCardUp(self.handCards[i])
        end
    end

    if #self.cardTypeData.SingleCard >= 1 then
        local singlePoint = GameLogic:getCardPoint(self.cardTypeData.SingleCard[1])
        for j = 1, #self.handCardValues do
            local cardPoint = GameLogic:getCardPoint(self.handCardValues[j])
            if singlePoint == cardPoint then
                self:setCardUp(self.handCards[j])
                break
            end
        end
    end

    Tiezhi.index = Tiezhi.index + 1
    if Tiezhi.index > #Tiezhi.TiezhiValue then
        Tiezhi.index = 1
    end

    print("--- BaiPaiUIManager:tiezhiCallBack end")
end

-- 同花顺
function BaiPaiUIManager:tonghuaszCallBack()
    local desk_config =  ViewHelp.getDeskInfo()
    print("--- BaiPaiUIManager:tonghuaszCallBack start")

    if self.cardTypeData.Tonghuasz == nil then
        return
    end

    local Tonghuasz = self.cardTypeData.Tonghuasz
    print("--- BaiPaiUIManager:tonghuaszCallBack Tonghuasz = " .. #Tonghuasz.TonghuaszValue)
    if #Tonghuasz.TonghuaszValue < 1 then
        return
    end

    self:setAllCardDown()

    local cardHuaKind = GameLogic:GetCardHuaKind(Tonghuasz.TonghuaszValue[Tonghuasz.index])
    local cardPoint = GameLogic:getCardPoint(Tonghuasz.TonghuaszValue[Tonghuasz.index])
    for i = 0, 4 do
        for j = 1, #self.handCardValues do
            if (GameLogic:GetCardHuaKind(self.handCardValues[j]) == cardHuaKind and
                (GameLogic:getCardPoint(self.handCardValues[j]) == (cardPoint - 1) or(i == 0 and GameLogic:getCardPoint(self.handCardValues[j]) == cardPoint)))
                then
                cardPoint = GameLogic:getCardPoint(self.handCardValues[j])
                self:setCardUp(self.handCards[j])
                break
            end
        end
    end

    Tonghuasz.index = Tonghuasz.index + 1
    if Tonghuasz.index > #Tonghuasz.TonghuaszValue then
        Tonghuasz.index = 1
    end

    print("--- BaiPaiUIManager:tonghuaszCallBack end")
end




-- 对子(带癞子)
function BaiPaiUIManager:pairsCallBack_L()
    print("--- BaiPaiUIManager:pairsCallBack_L start")
    if self.cardTypeData.Pairs == nil then
        return
    end

    local Pairs = self.cardTypeData.Pairs
    print("--- BaiPaiUIManager:pairsCallBack pairs = ".. #Pairs.PairsTab)
    if #Pairs.PairsTab < 1 then
        return 
    end
    self:setAllCardDown()
    local cards = Pairs.PairsTab[Pairs.index]
    for i=1,#self.handCardValues do
        if self.handCardValues[i] == cards[1] or self.handCardValues[i] == cards[2] then
            self:setCardUp(self.handCards[i])
        end
    end
    local needSingleCard = 5 - #cards   
    if needSingleCard>0 and #self.cardTypeData.SingleCard > 0 then
        for i=1,#self.cardTypeData.SingleCard do
            local card = self.cardTypeData.SingleCard[i]
            self:SetCardValueUp(card)
            needSingleCard = needSingleCard -1 
            if needSingleCard <= 0 then
                break
            end
        end
    end
    Pairs.index = Pairs.index + 1
    if Pairs.index > #Pairs.PairsTab then
        Pairs.index = 1
    end
    print("--- BaiPaiUIManager:pairsCallBack end")
end

-- 两对(带癞子)
function BaiPaiUIManager:twoPairsCallBack_L()
    print("--- BaiPaiUIManager:twoPairsCallBack_L start")
    if self.cardTypeData.Pairs == nil then
        return
    end
    local Pairs = self.cardTypeData.Pairs
    if #Pairs.PairsTab < 2 then
        return
    end

    self:setAllCardDown()


    self:SetCardValueUp(Pairs.PairsTab[Pairs.twoIndex][1])
    self:SetCardValueUp(Pairs.PairsTab[Pairs.twoIndex][2])
    self:SetCardValueUp(Pairs.PairsTab[Pairs.twoIndex2][1])
    self:SetCardValueUp(Pairs.PairsTab[Pairs.twoIndex2][2])

    if #self.cardTypeData.SingleCard > 0 then
        self:SetCardValueUp(self.cardTypeData.SingleCard[1])
    end
    

    Pairs.twoIndex2 = Pairs.twoIndex2 + 1
    if Pairs.twoIndex2 > #Pairs.PairsTab then
        Pairs.twoIndex = Pairs.twoIndex  + 1
        if Pairs.twoIndex >= #Pairs.PairsTab then
            Pairs.twoIndex = 1
        end
        Pairs.twoIndex2  = Pairs.twoIndex + 1
    end

    print("--- BaiPaiUIManager:twoPairsCallBack_L end")
end

-- 三条(带癞子)
function BaiPaiUIManager:threeCallBack_L()
    print("--- BaiPaiUIManager:threeCallBack_L start")
    if self.cardTypeData.Three == nil then
        return
    end
    local Three = self.cardTypeData.Three
    if #Three.ThreeTab < 1 then
        return
    end

    self:setAllCardDown()

    local cards = Three.ThreeTab[Three.index]
    for _,v in pairs(cards) do
        self:SetCardValueUp(v)
    end

    local needSingleCard = 5-#cards
    if #self.cardTypeData.SingleCard > 0 and needSingleCard > 0 then
         for i=1,#self.cardTypeData.SingleCard do
            local card = self.cardTypeData.SingleCard[i]
            self:SetCardValueUp(card)
            needSingleCard = needSingleCard -1 
            if needSingleCard <= 0 then
                break
            end
        end
    end

    Three.index = Three.index + 1
    if Three.index > #Three.ThreeTab then
        Three.index = 1
    end

    print("--- BaiPaiUIManager:threeCallBack_L end")
end

-- 顺子(带癞子)
function BaiPaiUIManager:shunzaCallBack_L()
    print("--- BaiPaiUIManager:shunzaCallBack_L start")
    if self.cardTypeData.Shunzi == nil then
        return
    end

    local Shunzi = self.cardTypeData.Shunzi
    print("--- BaiPaiUIManager:shunzaCallBack Shunza = " .. #Shunzi.ShunziTab)
    if #Shunzi.ShunziTab < 1 then
        return
    end

    self:setAllCardDown()
    for _,v in pairs(Shunzi.ShunziTab[Shunzi.index]) do
        self:SetCardValueUp(v)
    end

    Shunzi.index = Shunzi.index + 1
    if Shunzi.index > #Shunzi.ShunziTab then
        Shunzi.index = 1
    end

    print("--- BaiPaiUIManager:shunzaCallBack_L end")
end

-- 同花(带癞子)
function BaiPaiUIManager:tonghuaCallBack_L()
    print("--- BaiPaiUIManager:tonghuaCallBack start")
    if self.cardTypeData.Tonghua == nil then
        return
    end
    local Tonghua = self.cardTypeData.Tonghua
    print("--- BaiPaiUIManager:tonghuaCallBack Tonghua = " .. #Tonghua.TonghuaTab)
    if #Tonghua.TonghuaTab < 1 then
        return
    end
    self:setAllCardDown()
    for _,v in pairs(Tonghua.TonghuaTab[Tonghua.index]) do
        self:SetCardValueUp(v)
    end
    Tonghua.index = Tonghua.index + 1
    if Tonghua.index > #Tonghua.TonghuaTab then
        Tonghua.index = 1
    end
    print("--- BaiPaiUIManager:tonghuaCallBack end")
end

-- 葫芦(带癞子)
function BaiPaiUIManager:gourdCallBack_L()
    print("--- BaiPaiUIManager:gourdCallBack_L start")
    if self.cardTypeData.Gourd == nil then
        return
    end
    self:setAllCardDown()

    local Gourd = self.cardTypeData.Gourd

    for _,v in pairs(Gourd.GourdTab[Gourd.index]) do
        self:SetCardValueUp(v)
    end
    

    Gourd.index = Gourd.index + 1
    if Gourd.index > #Gourd.GourdTab then
        Gourd.index = 1
    end

    print("--- BaiPaiUIManager:gourdCallBack_L end")
end

-- 铁支(带癞子)
function BaiPaiUIManager:tiezhiCallBack_L()
    print("--- BaiPaiUIManager:tiezhiCallBack_L start")
    if self.cardTypeData.Tiezhi == nil then
        return
    end

    local Tiezhi = self.cardTypeData.Tiezhi
    print("--- BaiPaiUIManager:tiezhiCallBack_L Tiezhi = " .. #Tiezhi.TiezhiTab)
    if #Tiezhi.TiezhiTab < 1 then
        return
    end

    self:setAllCardDown()
    local TiezhiTab = Tiezhi.TiezhiTab[Tiezhi.index]
    
    for _,v in pairs(TiezhiTab) do 
        self:SetCardValueUp(v)
    end

    local needSingleCard = 5-#TiezhiTab
    if #self.cardTypeData.SingleCard > 0 and needSingleCard > 0 then
         for i=1,#self.cardTypeData.SingleCard do
            local card = self.cardTypeData.SingleCard[i]
            self:SetCardValueUp(card)
            needSingleCard = needSingleCard -1 
            if needSingleCard <= 0 then
                break
            end
        end
    end

    Tiezhi.index = Tiezhi.index + 1
    if Tiezhi.index > #Tiezhi.TiezhiTab then
        Tiezhi.index = 1
    end

    print("--- BaiPaiUIManager:tiezhiCallBack_L end")
end

-- 同花顺(带癞子)
function BaiPaiUIManager:tonghuaszCallBack_L()
    print("--- BaiPaiUIManager:tonghuaszCallBack start")
    if self.cardTypeData.Tonghuasz == nil then
        return
    end

    local Tonghuasz = self.cardTypeData.Tonghuasz
    print("--- BaiPaiUIManager:tonghuaszCallBack Tonghuasz = " .. #Tonghuasz.TonghuaszTab)
    if #Tonghuasz.TonghuaszTab < 1 then
        return
    end

    self:setAllCardDown()
    for _,v in pairs(Tonghuasz.TonghuaszTab[Tonghuasz.index]) do
        self:SetCardValueUp(v)
    end

    Tonghuasz.index = Tonghuasz.index + 1
    if Tonghuasz.index > #Tonghuasz.TonghuaszTab then
        Tonghuasz.index = 1
    end

    print("--- BaiPaiUIManager:tonghuaszCallBack end")
end

function BaiPaiUIManager:wutongCallBack_L()
    print("--- BaiPaiUIManager:tiezhiCallBack_L start")
    if self.cardTypeData.Wutong == nil then
        return
    end

    local Wutong = self.cardTypeData.Wutong
    print("--- BaiPaiUIManager:tiezhiCallBack_L Tiezhi = " .. #Wutong.WutongTab)
    if #Wutong.WutongTab < 1 then
        return
    end

    self:setAllCardDown()
    local WutongTab = Wutong.WutongTab[Wutong.index]
    
    for _,v in pairs(WutongTab) do 
        self:SetCardValueUp(v)
    end

    local needSingleCard = 5-#WutongTab
    if #self.cardTypeData.SingleCard > 0 and needSingleCard > 0 then
         for i=1,#self.cardTypeData.SingleCard do
            local card = self.cardTypeData.SingleCard[i]
            self:SetCardValueUp(card)
            needSingleCard = needSingleCard -1 
            if needSingleCard <= 0 then
                break
            end
        end
    end

    Wutong.index = Wutong.index + 1
    if Wutong.index > #Wutong.WutongTab then
        Wutong.index = 1
    end

    print("--- BaiPaiUIManager:WutongCallBack_L end")
end

-- 开始倒计时
function BaiPaiUIManager:startCountdown()
    --self.Panel_TimeLoad:setVisible(true)
    self.Text_timer:setText(tostring(self.curCountDountTime/10)) 
    self.schedulerID = cc.Director:getInstance():getScheduler():scheduleScriptFunc(handler(self, self.update), 0.1, false)
end

function BaiPaiUIManager:update(a)
    self.curCountDountTime = self.curCountDountTime - 1
    local strPercent = string.format("%.2f",100*self.curCountDountTime/(self.BaiPaiTime*10))
    --self.LoadingBar_Time:setPercent(tonumber(strPercent))
    if self.curCountDountTime >= 0 and self.schedulerID and not tolua.isnull(self.Text_timer) then
        if self.curCountDountTime%10 == 0  then
           local time = self.curCountDountTime/10
           if time == 0 then
               GameMusic:playCountDownEffect(true)
           elseif time <=5 then
               GameMusic:playCountDownEffect(false)
           end
           self.Text_timer:setText(tostring(time)) 
        end
    else
        self:stopCount()
    end

end

--停止计时
function BaiPaiUIManager:stopCount()
    if self.schedulerID ~= nil then    
        cc.Director:getInstance():getScheduler():unscheduleScriptEntry( self.schedulerID )
        self.schedulerID = nil
    end
    self.curCountDountTime = 200
end

function BaiPaiUIManager:sortCallback()
    self:brightDao(1, false)
    self:brightDao(2, false)
    self:brightDao(3, false)
    
    if self.isSize == true then
        self.isSize = false
        GameLogic:sort(self.handCardValues, 1)
        self.Button_dx:loadTextures("res/game_res/"..GAME_ID.."/ui_res/baipai/paixing/huase.png", "", "")
    else
        self.isSize = true
        GameLogic:sort(self.handCardValues, 0)
        self.Button_dx:loadTextures("res/game_res/"..GAME_ID.."/ui_res/baipai/paixing/daxiao.png", "", "")
    end
    self:setHandCardsValue()

end

-- 取消摆牌1.头 2.中 3.尾 4.全部取消
function BaiPaiUIManager:cancelCallBack(cancelType)
    local flag1 = false
    local flag2 = false
    local flag3 = false
    self.daoCardType[4]= 0
    self.bAddPoint = false
    if cancelType == 1 then
        flag1 = true
    elseif cancelType == 2 then
        flag2 = true
    elseif cancelType == 3 then
        flag3 = true
    elseif cancelType == 4 then
        flag1 = true
        flag2 = true
        flag3 = true
    end

    if flag1 then
        --self.Image_tou:loadTexture("res/game_res/"..GAME_ID.."/ui_res/baipai/paixing/toudao.png")
        --self.Image_tou:setVisible(true)
        self.Image_tou:setVisible(false)
        self.button_close[1]:setVisible(false)
        self.daoIsComplete[1] = false
        self.daoCardType[1] = 0
        for k, v in pairs(self.image_card_tou) do
            v:setVisible(false)
        end
        if #self.handCardValues ~= 13 then
            GameLogic:changeTab(self.handCardValues, self.touData, true)
        end
        self.touData = {}
    end
    if flag2 then
        --self.Image_zhong:loadTexture("res/game_res/"..GAME_ID.."/ui_res/baipai/paixing/zhongdao.png")
        --self.Image_zhong:setVisible(true)
        self.Image_zhong:setVisible(false)
        self.button_close[2]:setVisible(false)
        self.daoIsComplete[2] = false
        self.daoCardType[2] = 0
        for k, v in pairs(self.image_card_zhong) do
            v:setVisible(false)
        end
        if #self.handCardValues ~= 13 then
            GameLogic:changeTab(self.handCardValues, self.zhongData, true)
        end
        self.zhongData = {}
    end
    if flag3 then
        --self.Image_wei:loadTexture("res/game_res/"..GAME_ID.."/ui_res/baipai/paixing/weidao.png")
        --self.Image_wei:setVisible(true)
        
        self.button_close[3]:setVisible(false)
        self.daoIsComplete[3] = false
        self.daoCardType[3] = 0
        for k, v in pairs(self.image_card_wei) do
            v:setVisible(false)
        end
        if #self.handCardValues ~= 13 then
            GameLogic:changeTab(self.handCardValues, self.weiData, true)
        end
        self.weiData = {}
        self:daoImageType(3, self.daoCardType[3])
        self.Image_wei:setVisible(false)
    end
    self:changeHandCards()
    self.Button_allclose:setVisible(false)
    self.Button_refer:setVisible(false)
    self:isShowCardTypeButton(true)

    self.Text_s_cardtype:setVisible(false)
    self.Text_card_type:setVisible(false)

    if #self.handCardValues == 13 then
        self:judgeSpecialCardType()
    end

end

-- 判断特殊牌型
function BaiPaiUIManager:judgeSpecialCardType(bshowTip, bCancelAuto)
    print("--- BaiPaiUIManager:judgeSpecialCardType")
    local desk_config =  ViewHelp.getDeskInfo()
    local s_card_type
--    if desk_config.config.Mode.id == 2 then
--        s_card_type = GameLogic:judgeSpecialCardType_L(self.handCardValues)
--    else
--        s_card_type = GameLogic:judgeSpecialCardType(self.handCardValues)
--    end
    s_card_type = GameLogic:judgeSpecialCardType_L(self.handCardValues)
    if s_card_type >= 1 and s_card_type <= 18 then
        if g_server_obj and g_server_obj:get_server_type() ~= "video" then
            tmpparaseStruct = GameLogic:ParaseCards(self.handCardValues,true)
            if  #tmpparaseStruct.LaiziTab > 0 then
                self.Button_baodao:setEnabled(false)
                self.Button_spcial_card_type:setVisible(false)
                return
            else
                self.Button_baodao:setEnabled(true)
                if bCancelAuto == true  then
                    self.Button_BaipaiMode:loadTextureNormal("res/game_res/"..GAME_ID.."/ui_res/baipai/shoudong.png")
                    self.baipaiMode = 0
                end
            end

            if s_card_type == SpecialCardType.SCT_LT or s_card_type == SpecialCardType.SCT_YTL or s_card_type == SpecialCardType.SCT_QT or s_card_type == SpecialCardType.SCT_QL then
               
                self.Button_baodao:setEnabled(false)
                self.Button_spcial_card_type:setVisible(true)
                local filePathArr = {
                [SpecialCardType.SCT_LT] = "ui_res/baipai/button/liutong.png", 
                [SpecialCardType.SCT_YTL] = "ui_res/baipai/button/yitiaolong.png", 
                [SpecialCardType.SCT_QT] = "ui_res/baipai/button/qitong.png", 
                [SpecialCardType.SCT_QL] = "ui_res/baipai/button/qinglong.png"
                }
               
                print("s_card_type = " .. s_card_type)
                print("filePathArr[s_card_type] = " .. filePathArr[s_card_type])
                if filePathArr[s_card_type] ~= nil  then
                    self.Button_spcial_card_type:loadTextures(filePathArr[s_card_type], "", "")
                end
                
            end

            if bshowTip == nil  or bshowTip == false then
                return
            end

            --self.Button_spcial_card_type:setVisible(false)
            

            performWithDelay( self.ui_project , function()
--                self.specialCardTypeTip = Msg:showMsg(2,"是否需要特殊牌型: "..SpecCTName[s_card_type] , function() self:baodaoCallBack() end,
--                function() self.specialCardTypeTip = nil end )
--                local TipStr = tostring("可组成特殊牌型" .. SpecCTName[s_card_type] .. ",是否按此牌型\n与其他玩家一决高下？")
--                self:showSpeCardTypeBox(TipStr)
            end , 0.2)
        end
        
    end
end

-- 报道
function BaiPaiUIManager:baodaoCallBack()
    print("--- BaiPaiUIManager:baodaoCallBack")
    self:cancelCallBack(4)
    self.specialCardTypeTip = nil
    for k, v in pairs(self.image_card_tou) do
        self:setOneCardValue(self.handCardValues[k],v)
        table.insert(self.touData,self.handCardValues[k])
        v:setVisible(true)
    end
    for k, v in pairs(self.image_card_zhong) do
        self:setOneCardValue(self.handCardValues[k+3],v)
        table.insert(self.zhongData,self.handCardValues[k+3])
        v:setVisible(true)
    end
    for k, v in pairs(self.image_card_wei) do
        self:setOneCardValue(self.handCardValues[k+8],v)
        table.insert(self.weiData,self.handCardValues[k+8])
        v:setVisible(true)
    end

    self.daoCardType[4],self.bAddPoint = GameLogic:judgeSpecialCardType_L(self.handCardValues)

    self:isShowHandCards(false)

    self.Image_tou:setVisible(false)
    self.Image_zhong:setVisible(false)
    self.Image_wei:setVisible(false)

    self.Button_baodao:setEnabled(false)
    self.Button_spcial_card_type:setVisible(false)
    self.Button_allclose:setVisible(true)
    self.Button_refer:setVisible(true)
    self:isShowCardTypeButton(false)

    for i = 1, #self.button_close do
        self.button_close[i]:setVisible(true)
    end

    --self.Text_s_cardtype:setVisible(true)
    --self.Text_card_type:setVisible(true)

    local text = "清龙"
    if self.daoCardType[4] >= 1 and self.daoCardType[4] <= 11 then
        text = SpecCTName[self.daoCardType[4]]
    end
    self.Text_card_type:setText(text)

end

function BaiPaiUIManager:showSpeCardTypeBox(TipStr)
     local msgTip = createCSB("game_res/"..GAME_ID.."/ui_csb/Game_MsgTip.csb", true)
     local Image_bg = msgTip:getChildByName("Panel_setting"):getChildByName("Image_bg")
     local Button_cancel = Image_bg:getChildByName("Button_cancel")
     Button_cancel:addClickEventListener(function()
         self.ui_root:removeChild(msgTip)
     end)

     local Button_suer = Image_bg:getChildByName("Button_suer")
     Button_suer:addClickEventListener(function()
         self:baodaoCallBack()
        
         self.ui_root:removeChild(msgTip)
     end)

     local Text_Tip = Image_bg:getChildByName("Text_Tip")
     Text_Tip:setString(TipStr)

     self.ui_root:addChild(msgTip)
end

-- 提交
function BaiPaiUIManager:referCallBack()
    print("--- BaiPaiUIManager:referCallBack")

    --self.Text_Time:setText(tostring(28))
    --倒水
    if self.daoCardType[4]==0 then
        local dataTab = {self.touData, self.zhongData, self.weiData}
        for daoType = 1,3 do
            GameLogic:sortBySize(dataTab[daoType])
            self.daoCardType[daoType] = GameLogic:judgeCardType_L(dataTab[daoType])
        end
    end
    if self.daoCardType[4]==0 and self:judgeDaoShui() then
        return 
    end
    local request = { }
    request.touData = clone(self.touData)
    request.zhongData = clone(self.zhongData)
    request.weiData = clone(self.weiData)
    request.daoCardType = clone(self.daoCardType)
    request.bAddPoint = self.bAddPoint
    request.thspLaizis = ViewHelp.GetTHSPLaizis()--上传本地的同花顺+牌
    
    --以下代码为防止出现重复牌的放错措施
--    local checkTab ={}
--    local index = 1
--    for i = 1, #request.touData do
--        checkTab[index] = request.touData[i]
--        index = index + 1
--    end
--    for i = 1, #request.zhongData do
--        checkTab[index] = request.zhongData[i]
--        index = index + 1
--    end
--    for i = 1, #request.weiData do
--        checkTab[index] = request.weiData[i]
--        index = index + 1
--    end
--    for i = 1,#checkTab-1 do
--        for j = i+1,#checkTab do 
--            if checkTab[i] == checkTab[j] then
--                return api_show_Msg_Box(" 你已经掉线 ！",function()
--                    GameSceneModule:getInstance():getGameScene():Exit_game(5)
--                end , true )
--            end
--        end
--    end
    ----end

    handleGameMessage.SendGameMessage(handleGameMessage.Table_GameMessage_ASSID.ASS_BAIPAI_OVER, request)

end

--断线重连处理
function BaiPaiUIManager:onGameStation(resp_json)
    print("--- BaiPaiUIManager:onGameStation start")

    -- 玩家摆牌状态

    if resp_json.allSecialCardTypeVec ~= nil  then
        GameLogic:setSpecialCardType(resp_json.allSecialCardTypeVec)
    end

    if resp_json.BaiPaiTime then
        self.BaiPaiTime = resp_json.BaiPaiTime
    end

    if resp_json.SendCardSpeed then
        self.sendCardSpeed  = resp_json.SendCardSpeed
    end

    if resp_json.CountDown then
        self.curCountDountTime = resp_json.CountDown*10
    end

    if resp_json.bA_5BigShunzi ~= nil  then
        GameLogic:setA_5ShunziMode(resp_json.bA_5BigShunzi)
    end

    if resp_json.GSID == GameStation.GS_PLAYING then
        if not resp_json.BaiPaiOK then
            self.handCardValues = resp_json.HandCards
            self:changeHandCards()
            self:animateComplete()
            self:DetectionPxAuto()
            self:ui_open()
        end
    elseif resp_json.GSID == GameStation.GS_WAIT_NEXT_ROUND then

        self:ui_close()
    end

    print("--- BaiPaiUIManager:onGameStation end")
end

-- 牌型按钮状态
function BaiPaiUIManager:setCardTypeButton(show)
    for k,v in pairs(self.Button_px) do
        v:setEnabled(show)
    end
end

-- 是否显示牌型按钮
function BaiPaiUIManager:isShowCardTypeButton(isShow)
    if isShow and self:IsAutoBaipaiMode() then
        return
    end
    self.Panel_button_paixing:setVisible(isShow)
    self.Button_baodao:setVisible(isShow)
end

-- 是否显示手牌
function BaiPaiUIManager:isShowHandCards(isShow)
    for i = 1,13 do
        self.handCards[i]:setVisible(isShow)
    end
end

function BaiPaiUIManager:ui_open()
    self.ui_project:setVisible(true)
end

function BaiPaiUIManager:ui_close()
    self.ui_project:setVisible(false)
    if self.specialCardTypeTip and self.specialCardTypeTip.removeFromParent then
        self.specialCardTypeTip:removeFromParent()
        self.specialCardTypeTip = nil
    end
end

--自动摆牌检测牌型
function BaiPaiUIManager:DetectionPxAuto()
    self:DetectionPxAuto_optimize()
    if true then
        return 
    end

    local pxItem = {
        paixing = {
            [1] = 0,--头
            [2] = 0,--中
            [3] = 0,--尾
        },
        daoData = {
            [1] = {},--头
            [2] = {},--中
            [3] = {},--尾
        },
    }

    self.autoPaixingTab = {}

    

    function ConsistCard(pxItem,cards,daoIndex)
        pxItem.paixing[daoIndex] = 0
        pxItem.daoData[daoIndex] = {}
        if daoIndex == 3 then
            ViewHelp.SetTHSPLaizis({})
        end
        local cardType = nil
        local cardTypeData = nil
        local allCards = clone(cards)
        cardType,cardTypeData= GameLogic:detectionCardType_L(allCards)
        local NameTab = {"Pairs","TwoPairs","Three","Shunzi","Tonghua","Gourd","Tiezhi","Tonghuasz","Wutong"}
        local TableTab = {"PairsTab","TwoPairsTab","ThreeTab","ShunziTab","TonghuaTab","GourdTab","TiezhiTab","TonghuaszTab","WutongTab"}
        local startCardtypeIndex = CardType.CT_WUTONG
        if daoIndex<3 then
            startCardtypeIndex = pxItem.paixing[daoIndex+1]
        end
        for ctIdx = startCardtypeIndex, CardType.CT_WL, -1 do 
            
            if #self.autoPaixingTab >= AUTO_BAIPAI_ITEMNUM then
                break
            end
            local bTouDaoOverNum = false
            if daoIndex==1 and (ctIdx>CardType.CT_THREE or ctIdx==CardType.CT_TWO_PAIRS) then
                bTouDaoOverNum = true
            end
            if (cardType[ctIdx] == 1 or ctIdx == CardType.CT_WL) and not bTouDaoOverNum then
                local ctDataTab = {}
                if ctIdx == CardType.CT_WL then
                    local wlCards = {}
                    table.insert(wlCards,#wlCards+1, cards[1] )
                    table.insert(wlCards,#wlCards+1, cards[2] )
                    table.insert(wlCards,#wlCards+1, cards[3] )
                    table.insert(ctDataTab,wlCards)
                else
                    ctDataTab = cardTypeData[NameTab[ctIdx]][TableTab[ctIdx]]
                end
                local tabsize = #ctDataTab
                if tabsize > 57 then
                    tabsize = 57
                end
                if daoIndex==1 then
                    tabsize = 1
                end
                for i = 1, tabsize do
                    if daoIndex == 3 then
                        ViewHelp.SetTHSPLaizis({})
                    end
                    if #self.autoPaixingTab >= AUTO_BAIPAI_ITEMNUM then
                        break
                    end
                    pxItem.paixing[daoIndex] = 0
                    pxItem.daoData[daoIndex] = {}
                    pxItem.paixing[daoIndex] = ctIdx
                    pxItem.daoData[daoIndex] = clone(ctDataTab[i])
                    local ctData = ctDataTab[i]
                    local remainCards = GameLogic:RemoveCards(cards,ctDataTab[i])
                    if daoIndex>1 then
                        if ctIdx == CardType.CT_TONGHUASZ and daoIndex == 3 then
                            self:DetectionTHSP(ctDataTab[i])
                        end
                        ConsistCard(pxItem, remainCards, daoIndex-1)
                    else
                        --补牌
                        local remainCardIndex = 1
                        local daoCardNum = {3,5,5}
                        local insertItem = clone(pxItem)
                        if insertItem.paixing[3] == CardType.CT_WL and #pxItem.paixing[3]<5 then
                            table.insert(insertItem.daoData[3], remainCards[remainCardIndex])
                            remainCardIndex = remainCardIndex + 1
                        end
                        if insertItem.paixing[2] == CardType.CT_WL and #pxItem.paixing[2]<5 then
                            table.insert(insertItem.daoData[2], remainCards[remainCardIndex])
                            remainCardIndex = remainCardIndex + 1
                        end

                        for j = 1,3 do
                            if #insertItem.daoData[j] < daoCardNum[j] then
                                local num = #insertItem.daoData[j]
                                for k = num+1,daoCardNum[j] do
                                    table.insert(insertItem.daoData[j], remainCards[remainCardIndex])
                                    remainCardIndex = remainCardIndex + 1
                                end
                            end
                            insertItem.paixing[j]= GameLogic:judgeCardType_L(insertItem.daoData[j])
                        end
                        if not GameLogic:compare_tou_zhong(insertItem.daoData[1],insertItem.daoData[2], insertItem.paixing) 
                        and not GameLogic:compare_zhong_wei(insertItem.daoData[2],insertItem.daoData[3], insertItem.paixing) then
                            local bexist = false
                            if #self.autoPaixingTab > 0 then
                                for _,item in pairs(self.autoPaixingTab) do
                                    if item.paixing[1]==insertItem.paixing[1] and
                                       item.paixing[2]==insertItem.paixing[2] and
                                       item.paixing[3]==insertItem.paixing[3] then
                                       bexist = true
                                    end
                                end
                            end
                            if not bexist then
                                table.insert(self.autoPaixingTab, insertItem)
                            end
                        end
                    end
                end
                if daoIndex == 1 then
                    break
                end
            end
        end
        return
    end
    cclog("begin ConsistCard")
    ConsistCard(pxItem,self.handCardValues,3)
    cclog("end ConsistCard")
    function PxItemClickCallBack(pxIndex)
        if self.autoPaixingTab[pxIndex] then
            self:cancelCallBack(4)
            for daoIndex = 3,1,-1 do
                self:AddDaoCardFromUpList(daoIndex,self.autoPaixingTab[pxIndex].daoData[daoIndex])
            end
        end
    end
    if #self.autoPaixingTab > 0 then
        self.ListView_Px:removeAllItems()
        table.sort(self.autoPaixingTab, function(item1, item2)
            for daoindex = 3, 1, -1 do
                if item1.paixing[daoindex] > item2.paixing[daoindex] then
                    return true
                elseif item1.paixing[daoindex] < item2.paixing[daoindex] then
                    return false
                end
            end
            return false
        end)
        for index,paixing in pairs(self.autoPaixingTab) do
            local item = self.Image_PxItem:clone()
            item:setVisible(true)
            item:addClickEventListener(function() 
                PxItemClickCallBack(index)
            end)
            --item:removeFromParent()
            local Text_PxTou = item:getChildByName("Text_PxTou")
            local Text_PxZhong = item:getChildByName("Text_PxZhong")
            local Text_PxWei = item:getChildByName("Text_PxWei")
            if paixing.paixing[1] >= CardType.CT_THREE then
                Text_PxTou:setText("冲三")
                Text_PxTou:setColor(cc.c3b(255, 0, 0))
            else
                Text_PxTou:setText(CardTypeName[paixing.paixing[1]+1])
            end
            
            if paixing.paixing[2] >= CardType.CT_GOURD then
                Text_PxZhong:setText("中墩"..CardTypeName[paixing.paixing[2]+1])
                Text_PxZhong:setColor(cc.c3b(255, 0, 0))
            else
                Text_PxZhong:setText(CardTypeName[paixing.paixing[2]+1])
            end
            
            if paixing.paixing[3] >= CardType.CT_TIEZHI then
                Text_PxWei:setColor(cc.c3b(255, 0, 0))
            end
            Text_PxWei:setText(CardTypeName[paixing.paixing[3]+1])
            self.ListView_Px:pushBackCustomItem(item)
        end
    end
    ViewHelp.SetTHSPLaizis({})
end

function BaiPaiUIManager:UpdateBaiPaiMode()
    --0:手动 ，1:自动
    local bAuto = self:IsAutoBaipaiMode()
    self.Panel_AutoBaipai:setVisible(bAuto)
    self:isShowCardTypeButton(not bAuto)
    self:isShowHandCards(not bAuto)
    for i = 1, 3  do
        --self.button_close[i]:setVisible(false)
    end  
    if bAuto and self.autoPaixingTab and #self.autoPaixingTab > 0 then
        self:cancelCallBack(4)
        for daoIndex = 3,1,-1 do
            self:AddDaoCardFromUpList(daoIndex,self.autoPaixingTab[1].daoData[daoIndex])
        end

        for i = 1, 3  do
            --self.button_close[i]:setVisible(true)
        end
    end
end

function BaiPaiUIManager:IsAutoBaipaiMode()
    if self.baipaiMode and self.baipaiMode==1 then
        return true
    else
        return false
    end
end

--自动摆牌检测牌型
function BaiPaiUIManager:DetectionPxAuto_optimize()
    local pxItem = {
        paixing = {
            [1] = 0,--头
            [2] = 0,--中
            [3] = 0,--尾
        },
        daoData = {
            [1] = {},--头
            [2] = {},--中
            [3] = {},--尾
        },
    }

    self.autoPaixingTab = {}

    function optimizeCard(PaixingTab)
        --优化牌型
        --对子，两队，两队牌型:最大的对子放头道
        if PaixingTab.paixing[1] == CardType.CT_PAIRS and
        PaixingTab.paixing[2] == CardType.CT_TWO_PAIRS and
        PaixingTab.paixing[3] == CardType.CT_TWO_PAIRS and 
        not GameLogic:IsLaizi(PaixingTab.daoData[1][2]) then
            local tempCards = clone(self.handCardValues)
            GameLogic:sort(tempCards,0)
            local cardType = {0,0,0,0,0,0,0,0}       -- 牌型类型
            local cardTypeData = {}                  -- 牌型数据
            local paraseStruct = GameLogic:ParaseCards(tempCards)    
            GameLogic:detectionPairs_L(tempCards,paraseStruct,cardType,cardTypeData)
            if #cardTypeData.Pairs.PairsTab>=5 then
                local removeTab = {}
                PaixingTab.daoData[1]={}
                PaixingTab.daoData[1][1] = cardTypeData.Pairs.PairsTab[1][1]
                PaixingTab.daoData[1][2] = cardTypeData.Pairs.PairsTab[1][2]
                tempCards = GameLogic:RemoveCards(tempCards,PaixingTab.daoData[1])

                PaixingTab.daoData[2] = {}
                PaixingTab.daoData[2][1] = cardTypeData.Pairs.PairsTab[3][1]
                PaixingTab.daoData[2][2] = cardTypeData.Pairs.PairsTab[3][2]
                PaixingTab.daoData[2][3] = cardTypeData.Pairs.PairsTab[4][1]
                PaixingTab.daoData[2][4] = cardTypeData.Pairs.PairsTab[4][2]
                tempCards = GameLogic:RemoveCards(tempCards,PaixingTab.daoData[2])
                PaixingTab.daoData[3] = {}
                PaixingTab.daoData[3][1] = cardTypeData.Pairs.PairsTab[2][1]
                PaixingTab.daoData[3][2] = cardTypeData.Pairs.PairsTab[2][2]
                PaixingTab.daoData[3][3] = cardTypeData.Pairs.PairsTab[5][1]
                PaixingTab.daoData[3][4] = cardTypeData.Pairs.PairsTab[5][2]
                tempCards = GameLogic:RemoveCards(tempCards,PaixingTab.daoData[3])

                for daoIdx = 1,3 do
                    for k,v in pairs(tempCards) do
                        if GameLogic:getCardPoint(v) ~= GameLogic:getCardPoint(PaixingTab.daoData[daoIdx][1]) then
                            if daoIdx ~= 1 then
                                if GameLogic:getCardPoint(v) ~= GameLogic:getCardPoint(PaixingTab.daoData[daoIdx][3]) then
                                    table.insert(PaixingTab.daoData[daoIdx], #PaixingTab.daoData[daoIdx]+1,v)
                                    table.remove(tempCards,k)
                                    break
                                end
                            else
                                table.insert(PaixingTab.daoData[daoIdx], #PaixingTab.daoData[daoIdx]+1,v)
                                table.remove(tempCards,k)
                                break
                            end
                        end
                    end
                end

            end
        end

        --两对，对子，对子，把两对最小的放尾道
        if PaixingTab.paixing[1] == CardType.CT_PAIRS and
        PaixingTab.paixing[2] == CardType.CT_PAIRS and
        PaixingTab.paixing[3] == CardType.CT_TWO_PAIRS and 
        not GameLogic:IsLaizi(PaixingTab.daoData[1][2]) and 
        not GameLogic:IsLaizi(PaixingTab.daoData[2][2]) then
            if GameLogic:getCardPoint(PaixingTab.daoData[3][1]) > GameLogic:getCardPoint(PaixingTab.daoData[2][1]) then
                    local card1 = PaixingTab.daoData[2][1]
                    local card2 = PaixingTab.daoData[2][2]
                    PaixingTab.daoData[2][1] = PaixingTab.daoData[3][1]
                    PaixingTab.daoData[2][2] = PaixingTab.daoData[3][2]
                    PaixingTab.daoData[3][1] = card1
                    PaixingTab.daoData[3][2] = card2
            end
            if GameLogic:getCardPoint(PaixingTab.daoData[3][1]) > GameLogic:getCardPoint(PaixingTab.daoData[1][1]) then
                    local card1 = PaixingTab.daoData[1][1]
                    local card2 = PaixingTab.daoData[1][2]
                    PaixingTab.daoData[1][1] = PaixingTab.daoData[3][1]
                    PaixingTab.daoData[1][2] = PaixingTab.daoData[3][2]
                    PaixingTab.daoData[3][1] = card1
                    PaixingTab.daoData[3][2] = card2
            end
        end 
    end

    function ConsistCard(pxItem,cards,daoIndex)
        pxItem.paixing[daoIndex] = 0
        pxItem.daoData[daoIndex] = {}
        if daoIndex == 3 then
            ViewHelp.SetTHSPLaizis({})
        end
        local cardType = nil
        local cardTypeData = nil
        local allCards = clone(cards)
        cardType,cardTypeData= GameLogic:detectionCardType_L(allCards)
        local NameTab = {"Pairs","TwoPairs","Three","Shunzi","Tonghua","Gourd","Tiezhi","Tonghuasz","Wutong"}
        local TableTab = {"PairsTab","TwoPairsTab","ThreeTab","ShunziTab","TonghuaTab","GourdTab","TiezhiTab","TonghuaszTab","WutongTab"}
        local startCardtypeIndex = CardType.CT_WUTONG
        local endCardtypeIndex = CardType.CT_WL
        if daoIndex==2 then
            startCardtypeIndex = pxItem.paixing[3]
            endCardtypeIndex = pxItem.paixing[1]
        elseif daoIndex == 1 then
            startCardtypeIndex = pxItem.paixing[3]
        end

        if daoIndex == 1 and startCardtypeIndex>CardType.CT_THREE then
            startCardtypeIndex = CardType.CT_THREE
        end

        for ctIdx = startCardtypeIndex, endCardtypeIndex, -1 do 
            if #self.autoPaixingTab >= AUTO_BAIPAI_ITEMNUM then
                break
            end
            local bTouDaoOverNum = false
            if daoIndex==1 and (ctIdx>CardType.CT_THREE or ctIdx==CardType.CT_TWO_PAIRS) then
                bTouDaoOverNum = true
            end
            if (cardType[ctIdx] == 1 or ctIdx == CardType.CT_WL) and not bTouDaoOverNum then
                local ctDataTab = {}
                if ctIdx == CardType.CT_WL then
                    local wlCards = {}
                    table.insert(ctDataTab,wlCards)
                else
                    ctDataTab = cardTypeData[NameTab[ctIdx]][TableTab[ctIdx]]
                end
                local tabsize = #ctDataTab
                if tabsize > 57 then
                    tabsize = 57
                end
                if daoIndex==2 then
                    tabsize = 1
                end
                for i = 1, tabsize do
                    cclog(tostring(os.time())..": daoindex = "..tostring(daoIndex)..",cardType="..tostring(ctIdx)..",tabsize="..tostring(tabsize))
                    if daoIndex == 3 then
                        ViewHelp.SetTHSPLaizis({})
                    end
                    if #self.autoPaixingTab >= AUTO_BAIPAI_ITEMNUM then
                        break
                    end
                    pxItem.paixing[daoIndex] = 0
                    pxItem.daoData[daoIndex] = {}
                    pxItem.paixing[daoIndex] = ctIdx
                    pxItem.daoData[daoIndex] = clone(ctDataTab[i])
                    local ctData = ctDataTab[i]
                    local remainCards = GameLogic:RemoveCards(cards,ctDataTab[i])
                    if daoIndex ~= 2 then
                        if ctIdx == CardType.CT_TONGHUASZ and daoIndex == 3 then
                            self:DetectionTHSP(ctDataTab[i])
                        end
                        local nextDaoIndex = 0 
                        if daoIndex == 3 then
                            nextDaoIndex = 1 
                        elseif daoIndex == 1 then
                            nextDaoIndex = 2
                        end
                        ConsistCard(pxItem, remainCards, nextDaoIndex)
                    else
                        --补牌
                        local remainCardIndex = 1
                        local daoCardNum = {3,5,5}
                        local insertItem = clone(pxItem)
                        if insertItem.paixing[3] == CardType.CT_WL and #insertItem.daoData[3]<5 then
                            table.insert(insertItem.daoData[3], remainCards[remainCardIndex])
                            remainCardIndex = remainCardIndex + 1
                        end
                        if insertItem.paixing[2] == CardType.CT_WL and #insertItem.daoData[2]<5 then
                            table.insert(insertItem.daoData[2], remainCards[remainCardIndex])
                            remainCardIndex = remainCardIndex + 1
                        end

                        for j = 1,3 do
                            if #insertItem.daoData[j] < daoCardNum[j] then
                                local num = #insertItem.daoData[j]
                                for k = num+1,daoCardNum[j] do
                                    table.insert(insertItem.daoData[j], remainCards[remainCardIndex])
                                    remainCardIndex = remainCardIndex + 1
                                end
                            end
                            GameLogic:sortBySize(insertItem.daoData[j])
                            insertItem.paixing[j]= GameLogic:judgeCardType_L(insertItem.daoData[j])
                        end
                        if not GameLogic:compare_tou_zhong(insertItem.daoData[1],insertItem.daoData[2], insertItem.paixing) 
                        and not GameLogic:compare_zhong_wei(insertItem.daoData[2],insertItem.daoData[3], insertItem.paixing) then
                            local bexist = false
                            if #self.autoPaixingTab > 0 then
                                for _,item in pairs(self.autoPaixingTab) do
                                    if item.paixing[1]==insertItem.paixing[1] and
                                       item.paixing[2]==insertItem.paixing[2] and
                                       item.paixing[3]==insertItem.paixing[3] then
                                       bexist = true
                                    end
                                end
                            end
                            if not bexist then
                                optimizeCard(insertItem)
                                table.insert(self.autoPaixingTab, insertItem)
                            end
                        end
                    end
                end
--                if daoIndex == 2 then
--                    break
--                end
            end
        end
        return
    end

    


    local socket = require "socket"
    local start_time = socket.gettime()
    cclog("begin ConsistCard")
    local begingtime = os.time()
    ConsistCard(pxItem,self.handCardValues,3)
    local endTime = os.time()
    cclog("end ConsistCard")
    cclog("cost time-----------------------------"..tostring(endTime-begingtime))


    
 
    local end_time= socket.gettime()
    local use_time = (end_time - start_time )*1000
    print("used time: "..use_time .."ms \n")


  function PxItemClickCallBack(pxIndex)
        if self.autoPaixingTab[pxIndex] then
            self:cancelCallBack(4)
            for daoIndex = 3,1,-1 do
                self:AddDaoCardFromUpList(daoIndex,self.autoPaixingTab[pxIndex].daoData[daoIndex])
            end
        end
    end
    if #self.autoPaixingTab > 0 then
        self.ListView_Px:removeAllItems()
        table.sort(self.autoPaixingTab, function(item1, item2)
            for daoindex = 3, 1, -1 do
                if item1.paixing[daoindex] > item2.paixing[daoindex] then
                    return true
                elseif item1.paixing[daoindex] < item2.paixing[daoindex] then
                    return false
                end
            end
            return false
        end)
        for index,paixing in pairs(self.autoPaixingTab) do
            local item = self.Image_PxItem:clone()
            item:setVisible(true)
            item:addClickEventListener(function() 
                PxItemClickCallBack(index)
            end)
            --item:removeFromParent()
            local Text_PxTou = item:getChildByName("Text_PxTou")
            local Text_PxZhong = item:getChildByName("Text_PxZhong")
            local Text_PxWei = item:getChildByName("Text_PxWei")
            if paixing.paixing[1] >= CardType.CT_THREE then
                Text_PxTou:setText("冲三")
                Text_PxTou:setColor(cc.c3b(255, 0, 0))
            else
                Text_PxTou:setText(CardTypeName[paixing.paixing[1]+1])
            end
            
            if paixing.paixing[2] >= CardType.CT_GOURD then
                Text_PxZhong:setText("中墩"..CardTypeName[paixing.paixing[2]+1])
                Text_PxZhong:setColor(cc.c3b(255, 0, 0))
            else
                Text_PxZhong:setText(CardTypeName[paixing.paixing[2]+1])
            end
            
            if paixing.paixing[3] >= CardType.CT_TIEZHI then
                Text_PxWei:setColor(cc.c3b(255, 0, 0))
            end
            Text_PxWei:setText(CardTypeName[paixing.paixing[3]+1])
            self.ListView_Px:pushBackCustomItem(item)
        end
    end
    ViewHelp.SetTHSPLaizis({})
end
return BaiPaiUIManager

--endregion
