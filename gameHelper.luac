--[[
日期：	2016-10-13
作者:	hhw
描述:	转转麻将的游戏接口对象 继承与 app.platform.game.gameHelperBase
注意:   尽可能业务逻辑独立(维护成本降到最低)
--]]
local game_scene = nil
local m_gameHelperBase = import("app.platform.game.gameHelperBase")
local CGameHelper = class("CGameHelper" , m_gameHelperBase )
MAPAI_COLOR = cc.c3b(255,255,0)
NORMAL_COLOR = cc.c3b(255,255,255)
SELECTED_COLOR = cc.c3b(191,191,191)
function CGameHelper:ctor( game_room )
    --self.game_handler = nil
    PLAYER_COUNT = 7
    self.super:ctor( PLAYER_COUNT )
    GAME_ID = 21013708
    GameSceneModule = require("app/platform/game/"..GAME_ID.."/GameSceneModule.lua")
end

--@Override
-- 客户端发送游戏消息
function CGameHelper:sendGameMessage( AssistantID , request )
    self.super:sendGameMessage( AssistantID , request )
end

--@Override
--desc 自己进入游戏桌子成功的通知 用于切换场景
function CGameHelper:selfJoinDesk( hcode , room_info, callVedioBack)
    local jsonPath = "res/game_res/"..tostring(GAME_ID).."/Config.json"
    local jsonData = api_get_assets_file_data(jsonPath)
    if jsonData ~= nil and jsonData ~= "" then
        ConfigData = json.decode( jsonData )
    end
    cc.FileUtils:getInstance():addSearchPath(g_upd .."/res/game_res/"..GAME_ID.."/",true)
    cc.FileUtils:getInstance():addSearchPath("res/game_res/"..GAME_ID.."/")
    game_scene = GameApp:enterScene("game."..GAME_ID..".view.GameScene")   -- 进入游戏
    game_scene.callVedioBack = callVedioBack
    game_scene:reqDeskRebind()
    game_scene:resetEmojiShowPositions()
    self.super:init_game( game_scene )
    --local request = { }
    --handleGameMessage.SendGameMessage(handleGameMessage.Table_GameMessage_ASSID.ASS_GM_AGREE_GAME, request)
end

function CGameHelper:play_game_video( game_data )
    game_scene:play_game_video( game_data )
end
--@Override
--desc 具体游戏消息（180）的游戏接口
function CGameHelper:handleGameMessage( AssistantID , message )
    print("CGameHelper:handleGameMessage" , AssistantID , message )
   handleGameMessage.HandleGameMessage(AssistantID , message)
end

return CGameHelper