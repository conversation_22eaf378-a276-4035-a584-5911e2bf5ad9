--region *.lua
--Date
--此文件由[BabeLua]插件自动生成

--import("..GameDefine")
local m_download = import("....common.download")
local PlayerInfoLayer = class("PlayerInfoLayer",function() 
        return createCSB("game_res/"..GAME_ID.."/ui_csb/PlayerInfoLayer.csb", true) 
    end)

function PlayerInfoLayer:ctor(userInfo)
    

    local Panel_user_info = self:getChildByName("Panel_user_info")

    local Image_bg = Panel_user_info:getChildByName("Image_bg")
    self.Image_bg = Image_bg


    local Text_Nick = Panel_user_info:getChildByName("Text_Nick")
    local Text_ID = Panel_user_info:getChildByName("Text_ID")
    local Text_IP = Panel_user_info:getChildByName("Text_IP")
    
    Text_Nick:setText(api_get_ascll_sub_str_by_ui(userInfo.nickName,16))
    Text_ID:setText(userInfo.dwUserID)
    Text_IP:setText(userInfo.dwUserIP)

    local Image_Head = Panel_user_info:getChildByName("Image_Head")
    m_download:get_instance():set_head_image_and_auto_update(Image_Head, userInfo.avatarUrl, tostring(userInfo.dwUserID), nil, userInfo.HeadID)


     local posTab = {
        {station = 0, pos = cc.p(0,0)},
        {station = 1, pos = cc.p(0,0)},
        {station = 2, pos = cc.p(0,0)},
        {station = 3, pos = cc.p(0,0)},
        {station = 4, pos = cc.p(0,0)},
        {station = 5, pos = cc.p(0,0)},
        {station = 6, pos = cc.p(0,0)},
        {station = 7, pos = cc.p(0,0)},
        {station = 8, pos = cc.p(0,0)}
     }
    local gamescene = GameSceneModule:getInstance():getGameScene()
    local PlayerLayer =  gamescene.GameLayer.PlayersHeadUIManager.PlayerHeads
    
    for i = 1,7 do
        local pos={}
        posTab[i+1].pos=cc.p(PlayerLayer[i]:convertToWorldSpace(cc.p(0,0)))
 
    end
    local selfchair = ViewHelp.getBasePosChair()
    local Node = cc.Node:create() 
    if userInfo then
        gamescene:createInteractView(Node, posTab, selfchair, userInfo.bDeskStation, 400, 0, function () ViewHelp.hidePlayerInfoLayer() end)
    else
        gamescene:createInteractView(Node, posTab, selfchair, 200, 400, 0, function () ViewHelp.hidePlayerInfoLayer() end)
    end
    Node:setScale(0.75)
    Node:setPosition(cc.p(340,175))
    Panel_user_info:addChild(Node)
    if ViewHelp.getSelfInfo().look == true  then
        Node:setVisible(false)
    end 

    local touch_listener = cc.EventListenerTouchOneByOne:create()
    touch_listener:setSwallowTouches(false) 
    touch_listener:registerScriptHandler(handler(self, self.onTouchBegan), cc.Handler.EVENT_TOUCH_BEGAN)
    local eventDispatcher = self:getEventDispatcher()      
    eventDispatcher:addEventListenerWithSceneGraphPriority(touch_listener, self.Image_bg) 

end


function PlayerInfoLayer:onTouchBegan(touch, event)
    
    local p = self.Image_bg:convertToNodeSpace(touch:getLocation())
    local  rect = cc.rect(0, 0, self.Image_bg:getContentSize().width, self.Image_bg:getContentSize().height)
    if cc.rectContainsPoint(rect, p) then

    else
        ViewHelp.hidePlayerInfoLayer()
    end
end


return PlayerInfoLayer

--endregion
